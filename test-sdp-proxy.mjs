#!/usr/bin/env node

/**
 * Test script to verify SDP proxy with SDK headers
 */

// Sample valid SDP offer (minimal)
const sdpOffer = `v=0
o=- 0 0 IN IP4 127.0.0.1
s=-
t=0 0
m=audio 9 UDP/TLS/RTP/SAVPF 111
c=IN IP4 0.0.0.0
a=rtcp:9 IN IP4 0.0.0.0
a=ice-ufrag:test
a=ice-pwd:testpassword
a=fingerprint:sha-256 00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00
a=setup:actpass
a=mid:0
a=sendrecv
a=rtpmap:111 opus/48000/2`;

const apiKey = process.env.VITE_OPENAI_API_KEY || process.env.OPENAI_API_KEY;
const model = 'gpt-4o-realtime-preview-2024-12-17';

if (!apiKey) {
  console.error('❌ Missing API key in environment');
  process.exit(1);
}

console.log('🧪 Testing SDP Proxy with SDK Headers\n');

const url = `http://localhost:3001/api/openai/realtime/calls?model=${encodeURIComponent(model)}`;

console.log('📤 Sending request to:', url);
console.log('🔑 API Key:', apiKey.substring(0, 20) + '...');
console.log('📋 Model:', model);
console.log('📝 SDK Headers:', {
  'X-OpenAI-Agents-SDK': 'openai-agents-sdk.0.1.3',
  'User-Agent': 'Agents/JavaScript 0.1.3'
});
console.log('');

try {
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/sdp',
      'Accept': 'application/sdp',
      'Authorization': `Bearer ${apiKey}`,
      'X-OpenAI-Agents-SDK': 'openai-agents-sdk.0.1.3',
      'User-Agent': 'Agents/JavaScript 0.1.3'
    },
    body: sdpOffer
  });

  console.log('📥 Response Status:', response.status, response.statusText);
  console.log('📋 Content-Type:', response.headers.get('content-type'));
  console.log('🔗 Correlation-ID:', response.headers.get('x-correlation-id'));
  console.log('');

  const text = await response.text();

  console.log('📄 Response Body (first 500 chars):');
  console.log(text.substring(0, 500));
  console.log('');

  if (text.startsWith('v=')) {
    console.log('✅ Valid SDP response received!');
    console.log('📊 SDP Lines:', text.split('\n').length);
  } else if (text.startsWith('{')) {
    console.log('❌ JSON response (expected SDP)');
    try {
      const json = JSON.parse(text);
      console.log('📋 Error Details:', JSON.stringify(json, null, 2));
    } catch (e) {
      console.log('⚠️  Could not parse as JSON');
    }
  } else {
    console.log('❌ Unexpected response format');
  }

} catch (error) {
  console.error('❌ Request failed:', error.message);
  console.error('Stack:', error.stack);
}
