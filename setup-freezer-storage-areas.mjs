#!/usr/bin/env node

/**
 * Setup storage areas for freezer sensors
 */

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'http://127.0.0.1:54321';
const SERVICE_ROLE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;
const USER_ID = '8ef6a32c-e34c-46cc-bbb8-2e534fa64dcc'; // <EMAIL>

if (!SERVICE_ROLE_KEY) {
  console.error('❌ VITE_SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SERVICE_ROLE_KEY);

console.log('🏢 Setting up storage areas for freezer sensors\n');
console.log('='.repeat(60));

async function setupStorageAreas() {
  try {
    // Get all freezer sensors
    const { data: sensors, error: sensorsError } = await supabase
      .from('sensors')
      .select('id, sensor_id, name, location_description')
      .eq('user_id', USER_ID)
      .eq('is_active', true)
      .ilike('name', '%freezer%');

    if (sensorsError) throw sensorsError;

    console.log(`\n📡 Found ${sensors.length} freezer sensors to configure\n`);

    // Create storage areas for each freezer
    const storageAreas = [
      {
        name: 'Walk-In Freezer Downstairs',
        area_code: 'WIF-DOWN',
        area_type: 'walk_in_freezer',
        description: 'Downstairs walk-in freezer for bulk storage',
        temp_min_fahrenheit: -20,
        temp_max_fahrenheit: 0,
        temp_unit: 'fahrenheit',
        haccp_required: true,
        haccp_ccp_number: 'CCP-2',
        alert_enabled: true,
        alert_threshold_minutes: 15,
        matchSensor: (sensor) => sensor.name.includes('Downstairs'),
      },
      {
        name: 'Chest Freezer East Coast',
        area_code: 'CHEST-EC',
        area_type: 'reach_in_freezer',
        description: 'East coast chest freezer for east coast products',
        temp_min_fahrenheit: -30,
        temp_max_fahrenheit: 0,
        temp_unit: 'fahrenheit',
        haccp_required: true,
        haccp_ccp_number: 'CCP-3',
        alert_enabled: true,
        alert_threshold_minutes: 15,
        matchSensor: (sensor) => sensor.name.includes('East coast'),
      },
      {
        name: 'Upright Freezer Alaskan Fish',
        area_code: 'UF-ALASKAN',
        area_type: 'reach_in_freezer',
        description: 'White upright freezer for Alaskan fish products',
        temp_min_fahrenheit: -20,
        temp_max_fahrenheit: 5,
        temp_unit: 'fahrenheit',
        haccp_required: true,
        haccp_ccp_number: 'CCP-4',
        alert_enabled: true,
        alert_threshold_minutes: 15,
        matchSensor: (sensor) => sensor.name.includes('Upright'),
      },
    ];

    for (const areaConfig of storageAreas) {
      const { matchSensor, ...areaData } = areaConfig;

      console.log(`\n📦 Creating storage area: ${areaData.name}`);

      // Create storage area
      const { data: storageArea, error: areaError } = await supabase
        .from('storage_areas')
        .upsert({
          user_id: USER_ID,
          ...areaData,
          is_active: true,
        }, {
          onConflict: 'user_id,area_code',
        })
        .select('id, name')
        .single();

      if (areaError) {
        console.error(`   ❌ Failed to create storage area: ${areaError.message}`);
        continue;
      }

      console.log(`   ✅ Created: ${storageArea.name} (${storageArea.id})`);

      // Find matching sensor
      const matchingSensor = sensors.find(matchSensor);

      if (matchingSensor) {
        console.log(`   🔗 Linking sensor: ${matchingSensor.name}`);

        // Update sensor with storage_area_id
        const { error: updateError } = await supabase
          .from('sensors')
          .update({ storage_area_id: storageArea.id })
          .eq('id', matchingSensor.id);

        if (updateError) {
          console.error(`   ❌ Failed to link sensor: ${updateError.message}`);
        } else {
          console.log(`   ✅ Linked sensor successfully`);
        }
      } else {
        console.log(`   ⚠️  No matching sensor found`);
      }
    }

    console.log('\n' + '='.repeat(60));
    console.log('✅ Storage area setup completed!\n');

    // Verify the setup
    console.log('📊 Verification:\n');
    const { data: verifyData } = await supabase
      .from('sensors')
      .select(`
        id,
        name,
        storage_areas (
          id,
          name,
          area_type,
          temp_min_fahrenheit,
          temp_max_fahrenheit
        )
      `)
      .eq('user_id', USER_ID)
      .eq('is_active', true)
      .ilike('name', '%freezer%');

    verifyData.forEach(sensor => {
      const area = sensor.storage_areas;
      if (area) {
        console.log(`✅ ${sensor.name}`);
        console.log(`   → ${area.name} (${area.area_type})`);
        console.log(`   → Temperature range: ${area.temp_min_fahrenheit}°F to ${area.temp_max_fahrenheit}°F`);
      } else {
        console.log(`⚠️  ${sensor.name} - No storage area`);
      }
    });

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    console.error(error);
    process.exit(1);
  }
}

setupStorageAreas();
