{"mcpServers": {"cipher": {"command": "cipher", "args": ["--mode", "mcp", "--agent", "/Users/<USER>/Dev/Seafood-Manager/memAgent/cipher.yml"], "env": {"VECTOR_STORE_TYPE": "qdrant", "VECTOR_STORE_URL": "http://localhost:6333", "OPENAI_API_KEY": "********************************************************************************************************************************************************************", "PROJECT_NAME": "seafood-manager", "PROJECT_ROOT": "/Users/<USER>/Dev/Seafood-Manager"}, "disabled": false, "alwaysAllow": ["ask_cipher"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}}}