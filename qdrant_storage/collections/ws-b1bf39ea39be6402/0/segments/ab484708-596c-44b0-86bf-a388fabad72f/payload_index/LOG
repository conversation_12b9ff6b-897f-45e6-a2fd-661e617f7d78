2025/10/04-18:23:28.342483 21270 RocksDB version: 9.9.3
2025/10/04-18:23:28.342613 21270 Compile date 2024-12-05 01:25:31
2025/10/04-18:23:28.342618 21270 DB SUMMARY
2025/10/04-18:23:28.342621 21270 Host name (Env):  7f765a29171f
2025/10/04-18:23:28.342622 21270 DB Session ID:  HAJNJ04S1DEBGB0GHF6N
2025/10/04-18:23:28.343124 21270 CURRENT file:  CURRENT
2025/10/04-18:23:28.343126 21270 IDENTITY file:  IDENTITY
2025/10/04-18:23:28.343131 21270 MANIFEST file:  MANIFEST-000005 size: 1642 Bytes
2025/10/04-18:23:28.343133 21270 SST files in ./storage/collections/ws-b1bf39ea39be6402/0/segments/ab484708-596c-44b0-86bf-a388fabad72f/payload_index dir, Total Num: 5, files: 000039.sst 000040.sst 000041.sst 000042.sst 000043.sst 
2025/10/04-18:23:28.343135 21270 Write Ahead Log file in ./storage/collections/ws-b1bf39ea39be6402/0/segments/ab484708-596c-44b0-86bf-a388fabad72f/payload_index: 000038.log size: 0 ; 
2025/10/04-18:23:28.343136 21270                         Options.error_if_exists: 0
2025/10/04-18:23:28.343137 21270                       Options.create_if_missing: 1
2025/10/04-18:23:28.343138 21270                         Options.paranoid_checks: 1
2025/10/04-18:23:28.343139 21270             Options.flush_verify_memtable_count: 1
2025/10/04-18:23:28.343140 21270          Options.compaction_verify_record_count: 1
2025/10/04-18:23:28.343141 21270                               Options.track_and_verify_wals_in_manifest: 0
2025/10/04-18:23:28.343142 21270        Options.verify_sst_unique_id_in_manifest: 1
2025/10/04-18:23:28.343143 21270                                     Options.env: 0xffff8b630380
2025/10/04-18:23:28.343145 21270                                      Options.fs: PosixFileSystem
2025/10/04-18:23:28.343146 21270                                Options.info_log: 0xffff74594c00
2025/10/04-18:23:28.343147 21270                Options.max_file_opening_threads: 16
2025/10/04-18:23:28.343148 21270                              Options.statistics: (nil)
2025/10/04-18:23:28.343149 21270                               Options.use_fsync: 0
2025/10/04-18:23:28.343150 21270                       Options.max_log_file_size: 1048576
2025/10/04-18:23:28.343151 21270                  Options.max_manifest_file_size: 1073741824
2025/10/04-18:23:28.343152 21270                   Options.log_file_time_to_roll: 0
2025/10/04-18:23:28.343153 21270                       Options.keep_log_file_num: 1
2025/10/04-18:23:28.343154 21270                    Options.recycle_log_file_num: 0
2025/10/04-18:23:28.343155 21270                         Options.allow_fallocate: 1
2025/10/04-18:23:28.343156 21270                        Options.allow_mmap_reads: 0
2025/10/04-18:23:28.343157 21270                       Options.allow_mmap_writes: 0
2025/10/04-18:23:28.343158 21270                        Options.use_direct_reads: 0
2025/10/04-18:23:28.343159 21270                        Options.use_direct_io_for_flush_and_compaction: 0
2025/10/04-18:23:28.343160 21270          Options.create_missing_column_families: 1
2025/10/04-18:23:28.343161 21270                              Options.db_log_dir: 
2025/10/04-18:23:28.343162 21270                                 Options.wal_dir: 
2025/10/04-18:23:28.343162 21270                Options.table_cache_numshardbits: 6
2025/10/04-18:23:28.343163 21270                         Options.WAL_ttl_seconds: 0
2025/10/04-18:23:28.343164 21270                       Options.WAL_size_limit_MB: 0
2025/10/04-18:23:28.343165 21270                        Options.max_write_batch_group_size_bytes: 1048576
2025/10/04-18:23:28.343166 21270             Options.manifest_preallocation_size: 4194304
2025/10/04-18:23:28.343167 21270                     Options.is_fd_close_on_exec: 1
2025/10/04-18:23:28.343168 21270                   Options.advise_random_on_open: 1
2025/10/04-18:23:28.343169 21270                    Options.db_write_buffer_size: 0
2025/10/04-18:23:28.343170 21270                    Options.write_buffer_manager: 0xffff8b60bc40
2025/10/04-18:23:28.343171 21270           Options.random_access_max_buffer_size: 1048576
2025/10/04-18:23:28.343171 21270                      Options.use_adaptive_mutex: 0
2025/10/04-18:23:28.343172 21270                            Options.rate_limiter: (nil)
2025/10/04-18:23:28.343174 21270     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/10/04-18:23:28.343175 21270                       Options.wal_recovery_mode: 0
2025/10/04-18:23:28.343176 21270                  Options.enable_thread_tracking: 0
2025/10/04-18:23:28.343177 21270                  Options.enable_pipelined_write: 0
2025/10/04-18:23:28.343178 21270                  Options.unordered_write: 0
2025/10/04-18:23:28.343179 21270         Options.allow_concurrent_memtable_write: 1
2025/10/04-18:23:28.343180 21270      Options.enable_write_thread_adaptive_yield: 1
2025/10/04-18:23:28.343181 21270             Options.write_thread_max_yield_usec: 100
2025/10/04-18:23:28.343182 21270            Options.write_thread_slow_yield_usec: 3
2025/10/04-18:23:28.343183 21270                               Options.row_cache: None
2025/10/04-18:23:28.343184 21270                              Options.wal_filter: None
2025/10/04-18:23:28.343185 21270             Options.avoid_flush_during_recovery: 0
2025/10/04-18:23:28.343186 21270             Options.allow_ingest_behind: 0
2025/10/04-18:23:28.343187 21270             Options.two_write_queues: 0
2025/10/04-18:23:28.343188 21270             Options.manual_wal_flush: 0
2025/10/04-18:23:28.343189 21270             Options.wal_compression: 0
2025/10/04-18:23:28.343190 21270             Options.background_close_inactive_wals: 0
2025/10/04-18:23:28.343191 21270             Options.atomic_flush: 0
2025/10/04-18:23:28.343191 21270             Options.avoid_unnecessary_blocking_io: 0
2025/10/04-18:23:28.343192 21270             Options.prefix_seek_opt_in_only: 0
2025/10/04-18:23:28.343193 21270                 Options.persist_stats_to_disk: 0
2025/10/04-18:23:28.343194 21270                 Options.write_dbid_to_manifest: 1
2025/10/04-18:23:28.343195 21270                 Options.write_identity_file: 1
2025/10/04-18:23:28.343196 21270                 Options.log_readahead_size: 0
2025/10/04-18:23:28.343197 21270                 Options.file_checksum_gen_factory: Unknown
2025/10/04-18:23:28.343198 21270                 Options.best_efforts_recovery: 0
2025/10/04-18:23:28.343199 21270                Options.max_bgerror_resume_count: 2147483647
2025/10/04-18:23:28.343200 21270            Options.bgerror_resume_retry_interval: 1000000
2025/10/04-18:23:28.343201 21270             Options.allow_data_in_errors: 0
2025/10/04-18:23:28.343202 21270             Options.db_host_id: __hostname__
2025/10/04-18:23:28.343203 21270             Options.enforce_single_del_contracts: true
2025/10/04-18:23:28.343204 21270             Options.metadata_write_temperature: kUnknown
2025/10/04-18:23:28.343205 21270             Options.wal_write_temperature: kUnknown
2025/10/04-18:23:28.343206 21270             Options.max_background_jobs: 2
2025/10/04-18:23:28.343207 21270             Options.max_background_compactions: -1
2025/10/04-18:23:28.343208 21270             Options.max_subcompactions: 1
2025/10/04-18:23:28.343209 21270             Options.avoid_flush_during_shutdown: 0
2025/10/04-18:23:28.343209 21270           Options.writable_file_max_buffer_size: 1048576
2025/10/04-18:23:28.343210 21270             Options.delayed_write_rate : 16777216
2025/10/04-18:23:28.343213 21270             Options.max_total_wal_size: 0
2025/10/04-18:23:28.343214 21270             Options.delete_obsolete_files_period_micros: 180000000
2025/10/04-18:23:28.343215 21270                   Options.stats_dump_period_sec: 600
2025/10/04-18:23:28.343216 21270                 Options.stats_persist_period_sec: 600
2025/10/04-18:23:28.343217 21270                 Options.stats_history_buffer_size: 1048576
2025/10/04-18:23:28.343218 21270                          Options.max_open_files: 256
2025/10/04-18:23:28.343219 21270                          Options.bytes_per_sync: 0
2025/10/04-18:23:28.343220 21270                      Options.wal_bytes_per_sync: 0
2025/10/04-18:23:28.343221 21270                   Options.strict_bytes_per_sync: 0
2025/10/04-18:23:28.343221 21270       Options.compaction_readahead_size: 2097152
2025/10/04-18:23:28.343223 21270                  Options.max_background_flushes: -1
2025/10/04-18:23:28.343224 21270 Options.daily_offpeak_time_utc: 
2025/10/04-18:23:28.343225 21270 Compression algorithms supported:
2025/10/04-18:23:28.343226 21270 	kZSTD supported: 0
2025/10/04-18:23:28.343227 21270 	kXpressCompression supported: 0
2025/10/04-18:23:28.343227 21270 	kBZip2Compression supported: 0
2025/10/04-18:23:28.343228 21270 	kZSTDNotFinalCompression supported: 0
2025/10/04-18:23:28.343229 21270 	kLZ4Compression supported: 1
2025/10/04-18:23:28.343230 21270 	kZlibCompression supported: 0
2025/10/04-18:23:28.343231 21270 	kLZ4HCCompression supported: 1
2025/10/04-18:23:28.343232 21270 	kSnappyCompression supported: 1
2025/10/04-18:23:28.343233 21270 Fast CRC32 supported: Not supported on x86
2025/10/04-18:23:28.343234 21270 DMutex implementation: pthread_mutex_t
2025/10/04-18:23:28.343235 21270 Jemalloc supported: 0
2025/10/04-18:23:28.344101 21270               Options.comparator: leveldb.BytewiseComparator
2025/10/04-18:23:28.344105 21270           Options.merge_operator: None
2025/10/04-18:23:28.344106 21270        Options.compaction_filter: None
2025/10/04-18:23:28.344107 21270        Options.compaction_filter_factory: None
2025/10/04-18:23:28.344108 21270  Options.sst_partitioner_factory: None
2025/10/04-18:23:28.344109 21270         Options.memtable_factory: SkipListFactory
2025/10/04-18:23:28.344111 21270            Options.table_factory: BlockBasedTable
2025/10/04-18:23:28.344126 21270            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8b660840)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8b60b7d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/10/04-18:23:28.344128 21270        Options.write_buffer_size: 10485760
2025/10/04-18:23:28.344129 21270  Options.max_write_buffer_number: 2
2025/10/04-18:23:28.344130 21270          Options.compression: LZ4
2025/10/04-18:23:28.344131 21270                  Options.bottommost_compression: Disabled
2025/10/04-18:23:28.344132 21270       Options.prefix_extractor: nullptr
2025/10/04-18:23:28.344133 21270   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/10/04-18:23:28.344134 21270             Options.num_levels: 7
2025/10/04-18:23:28.344135 21270        Options.min_write_buffer_number_to_merge: 1
2025/10/04-18:23:28.344135 21270     Options.max_write_buffer_number_to_maintain: 0
2025/10/04-18:23:28.344136 21270     Options.max_write_buffer_size_to_maintain: 0
2025/10/04-18:23:28.344137 21270            Options.bottommost_compression_opts.window_bits: -14
2025/10/04-18:23:28.344138 21270                  Options.bottommost_compression_opts.level: 32767
2025/10/04-18:23:28.344139 21270               Options.bottommost_compression_opts.strategy: 0
2025/10/04-18:23:28.344140 21270         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.344141 21270         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.344142 21270         Options.bottommost_compression_opts.parallel_threads: 1
2025/10/04-18:23:28.344143 21270                  Options.bottommost_compression_opts.enabled: false
2025/10/04-18:23:28.344144 21270         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.344145 21270         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.344146 21270            Options.compression_opts.window_bits: -14
2025/10/04-18:23:28.344147 21270                  Options.compression_opts.level: 32767
2025/10/04-18:23:28.344148 21270               Options.compression_opts.strategy: 0
2025/10/04-18:23:28.344149 21270         Options.compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.344150 21270         Options.compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.344150 21270         Options.compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.344151 21270         Options.compression_opts.parallel_threads: 1
2025/10/04-18:23:28.344152 21270                  Options.compression_opts.enabled: false
2025/10/04-18:23:28.344153 21270         Options.compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.344154 21270      Options.level0_file_num_compaction_trigger: 4
2025/10/04-18:23:28.344155 21270          Options.level0_slowdown_writes_trigger: 20
2025/10/04-18:23:28.344156 21270              Options.level0_stop_writes_trigger: 36
2025/10/04-18:23:28.344157 21270                   Options.target_file_size_base: 67108864
2025/10/04-18:23:28.344158 21270             Options.target_file_size_multiplier: 1
2025/10/04-18:23:28.344159 21270                Options.max_bytes_for_level_base: 268435456
2025/10/04-18:23:28.344160 21270 Options.level_compaction_dynamic_level_bytes: 1
2025/10/04-18:23:28.344161 21270          Options.max_bytes_for_level_multiplier: 10.000000
2025/10/04-18:23:28.344162 21270 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/10/04-18:23:28.344163 21270 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/10/04-18:23:28.344164 21270 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/10/04-18:23:28.344165 21270 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/10/04-18:23:28.344166 21270 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/10/04-18:23:28.344167 21270 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/10/04-18:23:28.344169 21270 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/10/04-18:23:28.344169 21270       Options.max_sequential_skip_in_iterations: 8
2025/10/04-18:23:28.344170 21270                    Options.max_compaction_bytes: 1677721600
2025/10/04-18:23:28.344171 21270                        Options.arena_block_size: 1048576
2025/10/04-18:23:28.344172 21270   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/10/04-18:23:28.344173 21270   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/10/04-18:23:28.344174 21270                Options.disable_auto_compactions: 0
2025/10/04-18:23:28.344176 21270                        Options.compaction_style: kCompactionStyleLevel
2025/10/04-18:23:28.344177 21270                          Options.compaction_pri: kMinOverlappingRatio
2025/10/04-18:23:28.344178 21270 Options.compaction_options_universal.size_ratio: 1
2025/10/04-18:23:28.344179 21270 Options.compaction_options_universal.min_merge_width: 2
2025/10/04-18:23:28.344180 21270 Options.compaction_options_universal.max_merge_width: 4294967295
2025/10/04-18:23:28.344181 21270 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/10/04-18:23:28.344182 21270 Options.compaction_options_universal.compression_size_percent: -1
2025/10/04-18:23:28.344183 21270 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/10/04-18:23:28.344184 21270 Options.compaction_options_universal.max_read_amp: -1
2025/10/04-18:23:28.344185 21270 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/10/04-18:23:28.344186 21270 Options.compaction_options_fifo.allow_compaction: 0
2025/10/04-18:23:28.344189 21270                   Options.table_properties_collectors: 
2025/10/04-18:23:28.344190 21270                   Options.inplace_update_support: 0
2025/10/04-18:23:28.344191 21270                 Options.inplace_update_num_locks: 10000
2025/10/04-18:23:28.344192 21270               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/10/04-18:23:28.344193 21270               Options.memtable_whole_key_filtering: 0
2025/10/04-18:23:28.344194 21270   Options.memtable_huge_page_size: 0
2025/10/04-18:23:28.344195 21270                           Options.bloom_locality: 0
2025/10/04-18:23:28.344196 21270                    Options.max_successive_merges: 0
2025/10/04-18:23:28.344197 21270             Options.strict_max_successive_merges: 0
2025/10/04-18:23:28.344197 21270                Options.optimize_filters_for_hits: 0
2025/10/04-18:23:28.344198 21270                Options.paranoid_file_checks: 0
2025/10/04-18:23:28.344199 21270                Options.force_consistency_checks: 1
2025/10/04-18:23:28.344200 21270                Options.report_bg_io_stats: 0
2025/10/04-18:23:28.344201 21270                               Options.ttl: 2592000
2025/10/04-18:23:28.344202 21270          Options.periodic_compaction_seconds: 0
2025/10/04-18:23:28.344203 21270                        Options.default_temperature: kUnknown
2025/10/04-18:23:28.344204 21270  Options.preclude_last_level_data_seconds: 0
2025/10/04-18:23:28.344205 21270    Options.preserve_internal_time_seconds: 0
2025/10/04-18:23:28.344205 21270                       Options.enable_blob_files: false
2025/10/04-18:23:28.344206 21270                           Options.min_blob_size: 0
2025/10/04-18:23:28.344207 21270                          Options.blob_file_size: 268435456
2025/10/04-18:23:28.344208 21270                   Options.blob_compression_type: NoCompression
2025/10/04-18:23:28.344209 21270          Options.enable_blob_garbage_collection: false
2025/10/04-18:23:28.344210 21270      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/10/04-18:23:28.344211 21270 Options.blob_garbage_collection_force_threshold: 1.000000
2025/10/04-18:23:28.344211 21270          Options.blob_compaction_readahead_size: 0
2025/10/04-18:23:28.344212 21270                Options.blob_file_starting_level: 0
2025/10/04-18:23:28.344213 21270         Options.experimental_mempurge_threshold: 0.000000
2025/10/04-18:23:28.344214 21270            Options.memtable_max_range_deletions: 0
2025/10/04-18:23:28.344306 21270               Options.comparator: leveldb.BytewiseComparator
2025/10/04-18:23:28.344308 21270           Options.merge_operator: None
2025/10/04-18:23:28.344309 21270        Options.compaction_filter: None
2025/10/04-18:23:28.344309 21270        Options.compaction_filter_factory: None
2025/10/04-18:23:28.344310 21270  Options.sst_partitioner_factory: None
2025/10/04-18:23:28.344311 21270         Options.memtable_factory: SkipListFactory
2025/10/04-18:23:28.344312 21270            Options.table_factory: BlockBasedTable
2025/10/04-18:23:28.344320 21270            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8b660840)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8b60b7d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/10/04-18:23:28.344323 21270        Options.write_buffer_size: 10485760
2025/10/04-18:23:28.344324 21270  Options.max_write_buffer_number: 2
2025/10/04-18:23:28.344325 21270          Options.compression: LZ4
2025/10/04-18:23:28.344326 21270                  Options.bottommost_compression: Disabled
2025/10/04-18:23:28.344326 21270       Options.prefix_extractor: nullptr
2025/10/04-18:23:28.344327 21270   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/10/04-18:23:28.344328 21270             Options.num_levels: 7
2025/10/04-18:23:28.344329 21270        Options.min_write_buffer_number_to_merge: 1
2025/10/04-18:23:28.344330 21270     Options.max_write_buffer_number_to_maintain: 0
2025/10/04-18:23:28.344331 21270     Options.max_write_buffer_size_to_maintain: 0
2025/10/04-18:23:28.344332 21270            Options.bottommost_compression_opts.window_bits: -14
2025/10/04-18:23:28.344333 21270                  Options.bottommost_compression_opts.level: 32767
2025/10/04-18:23:28.344333 21270               Options.bottommost_compression_opts.strategy: 0
2025/10/04-18:23:28.344334 21270         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.344335 21270         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.344336 21270         Options.bottommost_compression_opts.parallel_threads: 1
2025/10/04-18:23:28.344337 21270                  Options.bottommost_compression_opts.enabled: false
2025/10/04-18:23:28.344338 21270         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.344339 21270         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.344340 21270            Options.compression_opts.window_bits: -14
2025/10/04-18:23:28.344341 21270                  Options.compression_opts.level: 32767
2025/10/04-18:23:28.344341 21270               Options.compression_opts.strategy: 0
2025/10/04-18:23:28.344342 21270         Options.compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.344343 21270         Options.compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.344344 21270         Options.compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.344344 21270         Options.compression_opts.parallel_threads: 1
2025/10/04-18:23:28.344345 21270                  Options.compression_opts.enabled: false
2025/10/04-18:23:28.344346 21270         Options.compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.344347 21270      Options.level0_file_num_compaction_trigger: 4
2025/10/04-18:23:28.344348 21270          Options.level0_slowdown_writes_trigger: 20
2025/10/04-18:23:28.344349 21270              Options.level0_stop_writes_trigger: 36
2025/10/04-18:23:28.344349 21270                   Options.target_file_size_base: 67108864
2025/10/04-18:23:28.344350 21270             Options.target_file_size_multiplier: 1
2025/10/04-18:23:28.344351 21270                Options.max_bytes_for_level_base: 268435456
2025/10/04-18:23:28.344352 21270 Options.level_compaction_dynamic_level_bytes: 1
2025/10/04-18:23:28.344353 21270          Options.max_bytes_for_level_multiplier: 10.000000
2025/10/04-18:23:28.344354 21270 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/10/04-18:23:28.344355 21270 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/10/04-18:23:28.344356 21270 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/10/04-18:23:28.344356 21270 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/10/04-18:23:28.344357 21270 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/10/04-18:23:28.344358 21270 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/10/04-18:23:28.344359 21270 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/10/04-18:23:28.344360 21270       Options.max_sequential_skip_in_iterations: 8
2025/10/04-18:23:28.344361 21270                    Options.max_compaction_bytes: 1677721600
2025/10/04-18:23:28.344361 21270                        Options.arena_block_size: 1048576
2025/10/04-18:23:28.344362 21270   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/10/04-18:23:28.344363 21270   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/10/04-18:23:28.344364 21270                Options.disable_auto_compactions: 0
2025/10/04-18:23:28.344365 21270                        Options.compaction_style: kCompactionStyleLevel
2025/10/04-18:23:28.344366 21270                          Options.compaction_pri: kMinOverlappingRatio
2025/10/04-18:23:28.344366 21270 Options.compaction_options_universal.size_ratio: 1
2025/10/04-18:23:28.344367 21270 Options.compaction_options_universal.min_merge_width: 2
2025/10/04-18:23:28.344368 21270 Options.compaction_options_universal.max_merge_width: 4294967295
2025/10/04-18:23:28.344369 21270 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/10/04-18:23:28.344370 21270 Options.compaction_options_universal.compression_size_percent: -1
2025/10/04-18:23:28.344371 21270 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/10/04-18:23:28.344372 21270 Options.compaction_options_universal.max_read_amp: -1
2025/10/04-18:23:28.344373 21270 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/10/04-18:23:28.344388 21270 Options.compaction_options_fifo.allow_compaction: 0
2025/10/04-18:23:28.344392 21270                   Options.table_properties_collectors: 
2025/10/04-18:23:28.344393 21270                   Options.inplace_update_support: 0
2025/10/04-18:23:28.344394 21270                 Options.inplace_update_num_locks: 10000
2025/10/04-18:23:28.344396 21270               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/10/04-18:23:28.344397 21270               Options.memtable_whole_key_filtering: 0
2025/10/04-18:23:28.344398 21270   Options.memtable_huge_page_size: 0
2025/10/04-18:23:28.344399 21270                           Options.bloom_locality: 0
2025/10/04-18:23:28.344399 21270                    Options.max_successive_merges: 0
2025/10/04-18:23:28.344401 21270             Options.strict_max_successive_merges: 0
2025/10/04-18:23:28.344401 21270                Options.optimize_filters_for_hits: 0
2025/10/04-18:23:28.344402 21270                Options.paranoid_file_checks: 0
2025/10/04-18:23:28.344403 21270                Options.force_consistency_checks: 1
2025/10/04-18:23:28.344404 21270                Options.report_bg_io_stats: 0
2025/10/04-18:23:28.344405 21270                               Options.ttl: 2592000
2025/10/04-18:23:28.344406 21270          Options.periodic_compaction_seconds: 0
2025/10/04-18:23:28.344407 21270                        Options.default_temperature: kUnknown
2025/10/04-18:23:28.344408 21270  Options.preclude_last_level_data_seconds: 0
2025/10/04-18:23:28.344408 21270    Options.preserve_internal_time_seconds: 0
2025/10/04-18:23:28.344409 21270                       Options.enable_blob_files: false
2025/10/04-18:23:28.344410 21270                           Options.min_blob_size: 0
2025/10/04-18:23:28.344411 21270                          Options.blob_file_size: 268435456
2025/10/04-18:23:28.344413 21270                   Options.blob_compression_type: NoCompression
2025/10/04-18:23:28.344414 21270          Options.enable_blob_garbage_collection: false
2025/10/04-18:23:28.344415 21270      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/10/04-18:23:28.344416 21270 Options.blob_garbage_collection_force_threshold: 1.000000
2025/10/04-18:23:28.344417 21270          Options.blob_compaction_readahead_size: 0
2025/10/04-18:23:28.344418 21270                Options.blob_file_starting_level: 0
2025/10/04-18:23:28.344419 21270         Options.experimental_mempurge_threshold: 0.000000
2025/10/04-18:23:28.344419 21270            Options.memtable_max_range_deletions: 0
2025/10/04-18:23:28.344458 21270               Options.comparator: leveldb.BytewiseComparator
2025/10/04-18:23:28.344460 21270           Options.merge_operator: None
2025/10/04-18:23:28.344460 21270        Options.compaction_filter: None
2025/10/04-18:23:28.344462 21270        Options.compaction_filter_factory: None
2025/10/04-18:23:28.344463 21270  Options.sst_partitioner_factory: None
2025/10/04-18:23:28.344464 21270         Options.memtable_factory: SkipListFactory
2025/10/04-18:23:28.344464 21270            Options.table_factory: BlockBasedTable
2025/10/04-18:23:28.344473 21270            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8b660840)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8b60b7d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/10/04-18:23:28.344475 21270        Options.write_buffer_size: 10485760
2025/10/04-18:23:28.344476 21270  Options.max_write_buffer_number: 2
2025/10/04-18:23:28.344477 21270          Options.compression: LZ4
2025/10/04-18:23:28.344478 21270                  Options.bottommost_compression: Disabled
2025/10/04-18:23:28.344479 21270       Options.prefix_extractor: nullptr
2025/10/04-18:23:28.344480 21270   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/10/04-18:23:28.344480 21270             Options.num_levels: 7
2025/10/04-18:23:28.344481 21270        Options.min_write_buffer_number_to_merge: 1
2025/10/04-18:23:28.344482 21270     Options.max_write_buffer_number_to_maintain: 0
2025/10/04-18:23:28.344483 21270     Options.max_write_buffer_size_to_maintain: 0
2025/10/04-18:23:28.344484 21270            Options.bottommost_compression_opts.window_bits: -14
2025/10/04-18:23:28.344485 21270                  Options.bottommost_compression_opts.level: 32767
2025/10/04-18:23:28.344486 21270               Options.bottommost_compression_opts.strategy: 0
2025/10/04-18:23:28.344486 21270         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.344487 21270         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.344488 21270         Options.bottommost_compression_opts.parallel_threads: 1
2025/10/04-18:23:28.344489 21270                  Options.bottommost_compression_opts.enabled: false
2025/10/04-18:23:28.344490 21270         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.344491 21270         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.344492 21270            Options.compression_opts.window_bits: -14
2025/10/04-18:23:28.344492 21270                  Options.compression_opts.level: 32767
2025/10/04-18:23:28.344493 21270               Options.compression_opts.strategy: 0
2025/10/04-18:23:28.344494 21270         Options.compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.344495 21270         Options.compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.344496 21270         Options.compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.344497 21270         Options.compression_opts.parallel_threads: 1
2025/10/04-18:23:28.344497 21270                  Options.compression_opts.enabled: false
2025/10/04-18:23:28.344498 21270         Options.compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.344499 21270      Options.level0_file_num_compaction_trigger: 4
2025/10/04-18:23:28.344500 21270          Options.level0_slowdown_writes_trigger: 20
2025/10/04-18:23:28.344500 21270              Options.level0_stop_writes_trigger: 36
2025/10/04-18:23:28.344501 21270                   Options.target_file_size_base: 67108864
2025/10/04-18:23:28.344502 21270             Options.target_file_size_multiplier: 1
2025/10/04-18:23:28.344503 21270                Options.max_bytes_for_level_base: 268435456
2025/10/04-18:23:28.344504 21270 Options.level_compaction_dynamic_level_bytes: 1
2025/10/04-18:23:28.344505 21270          Options.max_bytes_for_level_multiplier: 10.000000
2025/10/04-18:23:28.344506 21270 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/10/04-18:23:28.344507 21270 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/10/04-18:23:28.344508 21270 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/10/04-18:23:28.344509 21270 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/10/04-18:23:28.344510 21270 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/10/04-18:23:28.344511 21270 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/10/04-18:23:28.344512 21270 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/10/04-18:23:28.344512 21270       Options.max_sequential_skip_in_iterations: 8
2025/10/04-18:23:28.344513 21270                    Options.max_compaction_bytes: 1677721600
2025/10/04-18:23:28.344514 21270                        Options.arena_block_size: 1048576
2025/10/04-18:23:28.344515 21270   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/10/04-18:23:28.344516 21270   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/10/04-18:23:28.344517 21270                Options.disable_auto_compactions: 0
2025/10/04-18:23:28.344518 21270                        Options.compaction_style: kCompactionStyleLevel
2025/10/04-18:23:28.344519 21270                          Options.compaction_pri: kMinOverlappingRatio
2025/10/04-18:23:28.344519 21270 Options.compaction_options_universal.size_ratio: 1
2025/10/04-18:23:28.344520 21270 Options.compaction_options_universal.min_merge_width: 2
2025/10/04-18:23:28.344521 21270 Options.compaction_options_universal.max_merge_width: 4294967295
2025/10/04-18:23:28.344522 21270 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/10/04-18:23:28.344523 21270 Options.compaction_options_universal.compression_size_percent: -1
2025/10/04-18:23:28.344524 21270 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/10/04-18:23:28.344525 21270 Options.compaction_options_universal.max_read_amp: -1
2025/10/04-18:23:28.344526 21270 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/10/04-18:23:28.344527 21270 Options.compaction_options_fifo.allow_compaction: 0
2025/10/04-18:23:28.344528 21270                   Options.table_properties_collectors: 
2025/10/04-18:23:28.344528 21270                   Options.inplace_update_support: 0
2025/10/04-18:23:28.344529 21270                 Options.inplace_update_num_locks: 10000
2025/10/04-18:23:28.344530 21270               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/10/04-18:23:28.344531 21270               Options.memtable_whole_key_filtering: 0
2025/10/04-18:23:28.344532 21270   Options.memtable_huge_page_size: 0
2025/10/04-18:23:28.344533 21270                           Options.bloom_locality: 0
2025/10/04-18:23:28.344533 21270                    Options.max_successive_merges: 0
2025/10/04-18:23:28.344534 21270             Options.strict_max_successive_merges: 0
2025/10/04-18:23:28.344535 21270                Options.optimize_filters_for_hits: 0
2025/10/04-18:23:28.344536 21270                Options.paranoid_file_checks: 0
2025/10/04-18:23:28.344536 21270                Options.force_consistency_checks: 1
2025/10/04-18:23:28.344537 21270                Options.report_bg_io_stats: 0
2025/10/04-18:23:28.344538 21270                               Options.ttl: 2592000
2025/10/04-18:23:28.344539 21270          Options.periodic_compaction_seconds: 0
2025/10/04-18:23:28.344540 21270                        Options.default_temperature: kUnknown
2025/10/04-18:23:28.344541 21270  Options.preclude_last_level_data_seconds: 0
2025/10/04-18:23:28.344542 21270    Options.preserve_internal_time_seconds: 0
2025/10/04-18:23:28.344542 21270                       Options.enable_blob_files: false
2025/10/04-18:23:28.344543 21270                           Options.min_blob_size: 0
2025/10/04-18:23:28.344544 21270                          Options.blob_file_size: 268435456
2025/10/04-18:23:28.344545 21270                   Options.blob_compression_type: NoCompression
2025/10/04-18:23:28.344546 21270          Options.enable_blob_garbage_collection: false
2025/10/04-18:23:28.344547 21270      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/10/04-18:23:28.344547 21270 Options.blob_garbage_collection_force_threshold: 1.000000
2025/10/04-18:23:28.344548 21270          Options.blob_compaction_readahead_size: 0
2025/10/04-18:23:28.344549 21270                Options.blob_file_starting_level: 0
2025/10/04-18:23:28.344550 21270         Options.experimental_mempurge_threshold: 0.000000
2025/10/04-18:23:28.344551 21270            Options.memtable_max_range_deletions: 0
2025/10/04-18:23:28.344569 21270               Options.comparator: leveldb.BytewiseComparator
2025/10/04-18:23:28.344570 21270           Options.merge_operator: None
2025/10/04-18:23:28.344571 21270        Options.compaction_filter: None
2025/10/04-18:23:28.344571 21270        Options.compaction_filter_factory: None
2025/10/04-18:23:28.344572 21270  Options.sst_partitioner_factory: None
2025/10/04-18:23:28.344573 21270         Options.memtable_factory: SkipListFactory
2025/10/04-18:23:28.344574 21270            Options.table_factory: BlockBasedTable
2025/10/04-18:23:28.344580 21270            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8b660840)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8b60b7d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/10/04-18:23:28.344583 21270        Options.write_buffer_size: 10485760
2025/10/04-18:23:28.344583 21270  Options.max_write_buffer_number: 2
2025/10/04-18:23:28.344584 21270          Options.compression: LZ4
2025/10/04-18:23:28.344585 21270                  Options.bottommost_compression: Disabled
2025/10/04-18:23:28.344586 21270       Options.prefix_extractor: nullptr
2025/10/04-18:23:28.344587 21270   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/10/04-18:23:28.344587 21270             Options.num_levels: 7
2025/10/04-18:23:28.344588 21270        Options.min_write_buffer_number_to_merge: 1
2025/10/04-18:23:28.344589 21270     Options.max_write_buffer_number_to_maintain: 0
2025/10/04-18:23:28.344590 21270     Options.max_write_buffer_size_to_maintain: 0
2025/10/04-18:23:28.344590 21270            Options.bottommost_compression_opts.window_bits: -14
2025/10/04-18:23:28.344591 21270                  Options.bottommost_compression_opts.level: 32767
2025/10/04-18:23:28.344592 21270               Options.bottommost_compression_opts.strategy: 0
2025/10/04-18:23:28.344593 21270         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.344594 21270         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.344595 21270         Options.bottommost_compression_opts.parallel_threads: 1
2025/10/04-18:23:28.344596 21270                  Options.bottommost_compression_opts.enabled: false
2025/10/04-18:23:28.344597 21270         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.344598 21270         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.344599 21270            Options.compression_opts.window_bits: -14
2025/10/04-18:23:28.344600 21270                  Options.compression_opts.level: 32767
2025/10/04-18:23:28.344600 21270               Options.compression_opts.strategy: 0
2025/10/04-18:23:28.344601 21270         Options.compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.344602 21270         Options.compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.344603 21270         Options.compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.344604 21270         Options.compression_opts.parallel_threads: 1
2025/10/04-18:23:28.344604 21270                  Options.compression_opts.enabled: false
2025/10/04-18:23:28.344605 21270         Options.compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.344606 21270      Options.level0_file_num_compaction_trigger: 4
2025/10/04-18:23:28.344607 21270          Options.level0_slowdown_writes_trigger: 20
2025/10/04-18:23:28.344607 21270              Options.level0_stop_writes_trigger: 36
2025/10/04-18:23:28.344608 21270                   Options.target_file_size_base: 67108864
2025/10/04-18:23:28.344609 21270             Options.target_file_size_multiplier: 1
2025/10/04-18:23:28.344610 21270                Options.max_bytes_for_level_base: 268435456
2025/10/04-18:23:28.344611 21270 Options.level_compaction_dynamic_level_bytes: 1
2025/10/04-18:23:28.344612 21270          Options.max_bytes_for_level_multiplier: 10.000000
2025/10/04-18:23:28.344613 21270 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/10/04-18:23:28.344614 21270 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/10/04-18:23:28.344614 21270 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/10/04-18:23:28.344615 21270 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/10/04-18:23:28.344616 21270 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/10/04-18:23:28.344617 21270 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/10/04-18:23:28.344618 21270 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/10/04-18:23:28.344618 21270       Options.max_sequential_skip_in_iterations: 8
2025/10/04-18:23:28.344619 21270                    Options.max_compaction_bytes: 1677721600
2025/10/04-18:23:28.344620 21270                        Options.arena_block_size: 1048576
2025/10/04-18:23:28.344621 21270   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/10/04-18:23:28.344622 21270   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/10/04-18:23:28.344622 21270                Options.disable_auto_compactions: 0
2025/10/04-18:23:28.344623 21270                        Options.compaction_style: kCompactionStyleLevel
2025/10/04-18:23:28.344624 21270                          Options.compaction_pri: kMinOverlappingRatio
2025/10/04-18:23:28.344625 21270 Options.compaction_options_universal.size_ratio: 1
2025/10/04-18:23:28.344626 21270 Options.compaction_options_universal.min_merge_width: 2
2025/10/04-18:23:28.344627 21270 Options.compaction_options_universal.max_merge_width: 4294967295
2025/10/04-18:23:28.344628 21270 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/10/04-18:23:28.344629 21270 Options.compaction_options_universal.compression_size_percent: -1
2025/10/04-18:23:28.344630 21270 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/10/04-18:23:28.344630 21270 Options.compaction_options_universal.max_read_amp: -1
2025/10/04-18:23:28.344631 21270 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/10/04-18:23:28.344632 21270 Options.compaction_options_fifo.allow_compaction: 0
2025/10/04-18:23:28.344633 21270                   Options.table_properties_collectors: 
2025/10/04-18:23:28.344634 21270                   Options.inplace_update_support: 0
2025/10/04-18:23:28.344635 21270                 Options.inplace_update_num_locks: 10000
2025/10/04-18:23:28.344636 21270               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/10/04-18:23:28.344637 21270               Options.memtable_whole_key_filtering: 0
2025/10/04-18:23:28.344637 21270   Options.memtable_huge_page_size: 0
2025/10/04-18:23:28.344638 21270                           Options.bloom_locality: 0
2025/10/04-18:23:28.344639 21270                    Options.max_successive_merges: 0
2025/10/04-18:23:28.344640 21270             Options.strict_max_successive_merges: 0
2025/10/04-18:23:28.344640 21270                Options.optimize_filters_for_hits: 0
2025/10/04-18:23:28.344641 21270                Options.paranoid_file_checks: 0
2025/10/04-18:23:28.344642 21270                Options.force_consistency_checks: 1
2025/10/04-18:23:28.344643 21270                Options.report_bg_io_stats: 0
2025/10/04-18:23:28.344644 21270                               Options.ttl: 2592000
2025/10/04-18:23:28.344644 21270          Options.periodic_compaction_seconds: 0
2025/10/04-18:23:28.344645 21270                        Options.default_temperature: kUnknown
2025/10/04-18:23:28.344660 21270  Options.preclude_last_level_data_seconds: 0
2025/10/04-18:23:28.344661 21270    Options.preserve_internal_time_seconds: 0
2025/10/04-18:23:28.344662 21270                       Options.enable_blob_files: false
2025/10/04-18:23:28.344663 21270                           Options.min_blob_size: 0
2025/10/04-18:23:28.344663 21270                          Options.blob_file_size: 268435456
2025/10/04-18:23:28.344664 21270                   Options.blob_compression_type: NoCompression
2025/10/04-18:23:28.344665 21270          Options.enable_blob_garbage_collection: false
2025/10/04-18:23:28.344666 21270      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/10/04-18:23:28.344667 21270 Options.blob_garbage_collection_force_threshold: 1.000000
2025/10/04-18:23:28.344668 21270          Options.blob_compaction_readahead_size: 0
2025/10/04-18:23:28.344668 21270                Options.blob_file_starting_level: 0
2025/10/04-18:23:28.344669 21270         Options.experimental_mempurge_threshold: 0.000000
2025/10/04-18:23:28.344670 21270            Options.memtable_max_range_deletions: 0
2025/10/04-18:23:28.344693 21270               Options.comparator: leveldb.BytewiseComparator
2025/10/04-18:23:28.344695 21270           Options.merge_operator: None
2025/10/04-18:23:28.344695 21270        Options.compaction_filter: None
2025/10/04-18:23:28.344696 21270        Options.compaction_filter_factory: None
2025/10/04-18:23:28.344697 21270  Options.sst_partitioner_factory: None
2025/10/04-18:23:28.344698 21270         Options.memtable_factory: SkipListFactory
2025/10/04-18:23:28.344699 21270            Options.table_factory: BlockBasedTable
2025/10/04-18:23:28.344704 21270            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8b660840)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8b60b7d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/10/04-18:23:28.344707 21270        Options.write_buffer_size: 10485760
2025/10/04-18:23:28.344707 21270  Options.max_write_buffer_number: 2
2025/10/04-18:23:28.344708 21270          Options.compression: LZ4
2025/10/04-18:23:28.344709 21270                  Options.bottommost_compression: Disabled
2025/10/04-18:23:28.344710 21270       Options.prefix_extractor: nullptr
2025/10/04-18:23:28.344711 21270   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/10/04-18:23:28.344711 21270             Options.num_levels: 7
2025/10/04-18:23:28.344712 21270        Options.min_write_buffer_number_to_merge: 1
2025/10/04-18:23:28.344713 21270     Options.max_write_buffer_number_to_maintain: 0
2025/10/04-18:23:28.344714 21270     Options.max_write_buffer_size_to_maintain: 0
2025/10/04-18:23:28.344715 21270            Options.bottommost_compression_opts.window_bits: -14
2025/10/04-18:23:28.344715 21270                  Options.bottommost_compression_opts.level: 32767
2025/10/04-18:23:28.344716 21270               Options.bottommost_compression_opts.strategy: 0
2025/10/04-18:23:28.344717 21270         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.344718 21270         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.344719 21270         Options.bottommost_compression_opts.parallel_threads: 1
2025/10/04-18:23:28.344720 21270                  Options.bottommost_compression_opts.enabled: false
2025/10/04-18:23:28.344721 21270         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.344721 21270         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.344722 21270            Options.compression_opts.window_bits: -14
2025/10/04-18:23:28.344723 21270                  Options.compression_opts.level: 32767
2025/10/04-18:23:28.344724 21270               Options.compression_opts.strategy: 0
2025/10/04-18:23:28.344725 21270         Options.compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.344725 21270         Options.compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.344726 21270         Options.compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.344727 21270         Options.compression_opts.parallel_threads: 1
2025/10/04-18:23:28.344728 21270                  Options.compression_opts.enabled: false
2025/10/04-18:23:28.344728 21270         Options.compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.344729 21270      Options.level0_file_num_compaction_trigger: 4
2025/10/04-18:23:28.344730 21270          Options.level0_slowdown_writes_trigger: 20
2025/10/04-18:23:28.344731 21270              Options.level0_stop_writes_trigger: 36
2025/10/04-18:23:28.344732 21270                   Options.target_file_size_base: 67108864
2025/10/04-18:23:28.344733 21270             Options.target_file_size_multiplier: 1
2025/10/04-18:23:28.344734 21270                Options.max_bytes_for_level_base: 268435456
2025/10/04-18:23:28.344735 21270 Options.level_compaction_dynamic_level_bytes: 1
2025/10/04-18:23:28.344736 21270          Options.max_bytes_for_level_multiplier: 10.000000
2025/10/04-18:23:28.344737 21270 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/10/04-18:23:28.344738 21270 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/10/04-18:23:28.344739 21270 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/10/04-18:23:28.344739 21270 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/10/04-18:23:28.344740 21270 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/10/04-18:23:28.344741 21270 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/10/04-18:23:28.344742 21270 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/10/04-18:23:28.344743 21270       Options.max_sequential_skip_in_iterations: 8
2025/10/04-18:23:28.344743 21270                    Options.max_compaction_bytes: 1677721600
2025/10/04-18:23:28.344744 21270                        Options.arena_block_size: 1048576
2025/10/04-18:23:28.344745 21270   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/10/04-18:23:28.344746 21270   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/10/04-18:23:28.344747 21270                Options.disable_auto_compactions: 0
2025/10/04-18:23:28.344748 21270                        Options.compaction_style: kCompactionStyleLevel
2025/10/04-18:23:28.344749 21270                          Options.compaction_pri: kMinOverlappingRatio
2025/10/04-18:23:28.344750 21270 Options.compaction_options_universal.size_ratio: 1
2025/10/04-18:23:28.344751 21270 Options.compaction_options_universal.min_merge_width: 2
2025/10/04-18:23:28.344752 21270 Options.compaction_options_universal.max_merge_width: 4294967295
2025/10/04-18:23:28.344753 21270 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/10/04-18:23:28.344753 21270 Options.compaction_options_universal.compression_size_percent: -1
2025/10/04-18:23:28.344754 21270 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/10/04-18:23:28.344755 21270 Options.compaction_options_universal.max_read_amp: -1
2025/10/04-18:23:28.344756 21270 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/10/04-18:23:28.344757 21270 Options.compaction_options_fifo.allow_compaction: 0
2025/10/04-18:23:28.344758 21270                   Options.table_properties_collectors: 
2025/10/04-18:23:28.344759 21270                   Options.inplace_update_support: 0
2025/10/04-18:23:28.344759 21270                 Options.inplace_update_num_locks: 10000
2025/10/04-18:23:28.344760 21270               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/10/04-18:23:28.344761 21270               Options.memtable_whole_key_filtering: 0
2025/10/04-18:23:28.344762 21270   Options.memtable_huge_page_size: 0
2025/10/04-18:23:28.344762 21270                           Options.bloom_locality: 0
2025/10/04-18:23:28.344763 21270                    Options.max_successive_merges: 0
2025/10/04-18:23:28.344764 21270             Options.strict_max_successive_merges: 0
2025/10/04-18:23:28.344765 21270                Options.optimize_filters_for_hits: 0
2025/10/04-18:23:28.344766 21270                Options.paranoid_file_checks: 0
2025/10/04-18:23:28.344766 21270                Options.force_consistency_checks: 1
2025/10/04-18:23:28.344767 21270                Options.report_bg_io_stats: 0
2025/10/04-18:23:28.344768 21270                               Options.ttl: 2592000
2025/10/04-18:23:28.344769 21270          Options.periodic_compaction_seconds: 0
2025/10/04-18:23:28.344770 21270                        Options.default_temperature: kUnknown
2025/10/04-18:23:28.344770 21270  Options.preclude_last_level_data_seconds: 0
2025/10/04-18:23:28.344771 21270    Options.preserve_internal_time_seconds: 0
2025/10/04-18:23:28.344772 21270                       Options.enable_blob_files: false
2025/10/04-18:23:28.344773 21270                           Options.min_blob_size: 0
2025/10/04-18:23:28.344773 21270                          Options.blob_file_size: 268435456
2025/10/04-18:23:28.344774 21270                   Options.blob_compression_type: NoCompression
2025/10/04-18:23:28.344775 21270          Options.enable_blob_garbage_collection: false
2025/10/04-18:23:28.344776 21270      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/10/04-18:23:28.344777 21270 Options.blob_garbage_collection_force_threshold: 1.000000
2025/10/04-18:23:28.344778 21270          Options.blob_compaction_readahead_size: 0
2025/10/04-18:23:28.344778 21270                Options.blob_file_starting_level: 0
2025/10/04-18:23:28.344779 21270         Options.experimental_mempurge_threshold: 0.000000
2025/10/04-18:23:28.344780 21270            Options.memtable_max_range_deletions: 0
2025/10/04-18:23:28.344798 21270               Options.comparator: leveldb.BytewiseComparator
2025/10/04-18:23:28.344799 21270           Options.merge_operator: None
2025/10/04-18:23:28.344800 21270        Options.compaction_filter: None
2025/10/04-18:23:28.344801 21270        Options.compaction_filter_factory: None
2025/10/04-18:23:28.344801 21270  Options.sst_partitioner_factory: None
2025/10/04-18:23:28.344802 21270         Options.memtable_factory: SkipListFactory
2025/10/04-18:23:28.344803 21270            Options.table_factory: BlockBasedTable
2025/10/04-18:23:28.344809 21270            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8b660840)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8b60b7d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/10/04-18:23:28.344811 21270        Options.write_buffer_size: 10485760
2025/10/04-18:23:28.344812 21270  Options.max_write_buffer_number: 2
2025/10/04-18:23:28.344813 21270          Options.compression: LZ4
2025/10/04-18:23:28.344814 21270                  Options.bottommost_compression: Disabled
2025/10/04-18:23:28.344815 21270       Options.prefix_extractor: nullptr
2025/10/04-18:23:28.344815 21270   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/10/04-18:23:28.344816 21270             Options.num_levels: 7
2025/10/04-18:23:28.344817 21270        Options.min_write_buffer_number_to_merge: 1
2025/10/04-18:23:28.344818 21270     Options.max_write_buffer_number_to_maintain: 0
2025/10/04-18:23:28.344819 21270     Options.max_write_buffer_size_to_maintain: 0
2025/10/04-18:23:28.344819 21270            Options.bottommost_compression_opts.window_bits: -14
2025/10/04-18:23:28.344820 21270                  Options.bottommost_compression_opts.level: 32767
2025/10/04-18:23:28.344821 21270               Options.bottommost_compression_opts.strategy: 0
2025/10/04-18:23:28.344822 21270         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.344823 21270         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.344824 21270         Options.bottommost_compression_opts.parallel_threads: 1
2025/10/04-18:23:28.344825 21270                  Options.bottommost_compression_opts.enabled: false
2025/10/04-18:23:28.344825 21270         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.344826 21270         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.344827 21270            Options.compression_opts.window_bits: -14
2025/10/04-18:23:28.344828 21270                  Options.compression_opts.level: 32767
2025/10/04-18:23:28.344829 21270               Options.compression_opts.strategy: 0
2025/10/04-18:23:28.344830 21270         Options.compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.344830 21270         Options.compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.344831 21270         Options.compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.344832 21270         Options.compression_opts.parallel_threads: 1
2025/10/04-18:23:28.344833 21270                  Options.compression_opts.enabled: false
2025/10/04-18:23:28.344834 21270         Options.compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.344834 21270      Options.level0_file_num_compaction_trigger: 4
2025/10/04-18:23:28.344835 21270          Options.level0_slowdown_writes_trigger: 20
2025/10/04-18:23:28.344836 21270              Options.level0_stop_writes_trigger: 36
2025/10/04-18:23:28.344837 21270                   Options.target_file_size_base: 67108864
2025/10/04-18:23:28.344840 21270             Options.target_file_size_multiplier: 1
2025/10/04-18:23:28.344841 21270                Options.max_bytes_for_level_base: 268435456
2025/10/04-18:23:28.344842 21270 Options.level_compaction_dynamic_level_bytes: 1
2025/10/04-18:23:28.344843 21270          Options.max_bytes_for_level_multiplier: 10.000000
2025/10/04-18:23:28.344844 21270 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/10/04-18:23:28.344844 21270 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/10/04-18:23:28.344845 21270 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/10/04-18:23:28.344846 21270 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/10/04-18:23:28.344847 21270 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/10/04-18:23:28.344848 21270 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/10/04-18:23:28.344848 21270 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/10/04-18:23:28.344849 21270       Options.max_sequential_skip_in_iterations: 8
2025/10/04-18:23:28.344850 21270                    Options.max_compaction_bytes: 1677721600
2025/10/04-18:23:28.344851 21270                        Options.arena_block_size: 1048576
2025/10/04-18:23:28.344852 21270   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/10/04-18:23:28.344852 21270   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/10/04-18:23:28.344853 21270                Options.disable_auto_compactions: 0
2025/10/04-18:23:28.344854 21270                        Options.compaction_style: kCompactionStyleLevel
2025/10/04-18:23:28.344855 21270                          Options.compaction_pri: kMinOverlappingRatio
2025/10/04-18:23:28.344856 21270 Options.compaction_options_universal.size_ratio: 1
2025/10/04-18:23:28.344856 21270 Options.compaction_options_universal.min_merge_width: 2
2025/10/04-18:23:28.344857 21270 Options.compaction_options_universal.max_merge_width: 4294967295
2025/10/04-18:23:28.344858 21270 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/10/04-18:23:28.344859 21270 Options.compaction_options_universal.compression_size_percent: -1
2025/10/04-18:23:28.344860 21270 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/10/04-18:23:28.344861 21270 Options.compaction_options_universal.max_read_amp: -1
2025/10/04-18:23:28.344862 21270 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/10/04-18:23:28.344863 21270 Options.compaction_options_fifo.allow_compaction: 0
2025/10/04-18:23:28.344863 21270                   Options.table_properties_collectors: 
2025/10/04-18:23:28.344864 21270                   Options.inplace_update_support: 0
2025/10/04-18:23:28.344865 21270                 Options.inplace_update_num_locks: 10000
2025/10/04-18:23:28.344866 21270               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/10/04-18:23:28.344867 21270               Options.memtable_whole_key_filtering: 0
2025/10/04-18:23:28.344868 21270   Options.memtable_huge_page_size: 0
2025/10/04-18:23:28.344868 21270                           Options.bloom_locality: 0
2025/10/04-18:23:28.344869 21270                    Options.max_successive_merges: 0
2025/10/04-18:23:28.344870 21270             Options.strict_max_successive_merges: 0
2025/10/04-18:23:28.344870 21270                Options.optimize_filters_for_hits: 0
2025/10/04-18:23:28.344871 21270                Options.paranoid_file_checks: 0
2025/10/04-18:23:28.344872 21270                Options.force_consistency_checks: 1
2025/10/04-18:23:28.344873 21270                Options.report_bg_io_stats: 0
2025/10/04-18:23:28.344888 21270                               Options.ttl: 2592000
2025/10/04-18:23:28.344889 21270          Options.periodic_compaction_seconds: 0
2025/10/04-18:23:28.344890 21270                        Options.default_temperature: kUnknown
2025/10/04-18:23:28.344891 21270  Options.preclude_last_level_data_seconds: 0
2025/10/04-18:23:28.344892 21270    Options.preserve_internal_time_seconds: 0
2025/10/04-18:23:28.344893 21270                       Options.enable_blob_files: false
2025/10/04-18:23:28.344893 21270                           Options.min_blob_size: 0
2025/10/04-18:23:28.344894 21270                          Options.blob_file_size: 268435456
2025/10/04-18:23:28.344895 21270                   Options.blob_compression_type: NoCompression
2025/10/04-18:23:28.344896 21270          Options.enable_blob_garbage_collection: false
2025/10/04-18:23:28.344897 21270      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/10/04-18:23:28.344897 21270 Options.blob_garbage_collection_force_threshold: 1.000000
2025/10/04-18:23:28.344898 21270          Options.blob_compaction_readahead_size: 0
2025/10/04-18:23:28.344899 21270                Options.blob_file_starting_level: 0
2025/10/04-18:23:28.344900 21270         Options.experimental_mempurge_threshold: 0.000000
2025/10/04-18:23:28.344901 21270            Options.memtable_max_range_deletions: 0
2025/10/04-18:23:28.344920 21270               Options.comparator: leveldb.BytewiseComparator
2025/10/04-18:23:28.344921 21270           Options.merge_operator: None
2025/10/04-18:23:28.344922 21270        Options.compaction_filter: None
2025/10/04-18:23:28.344922 21270        Options.compaction_filter_factory: None
2025/10/04-18:23:28.344923 21270  Options.sst_partitioner_factory: None
2025/10/04-18:23:28.344924 21270         Options.memtable_factory: SkipListFactory
2025/10/04-18:23:28.344925 21270            Options.table_factory: BlockBasedTable
2025/10/04-18:23:28.344930 21270            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8b660840)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8b60b7d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/10/04-18:23:28.344933 21270        Options.write_buffer_size: 10485760
2025/10/04-18:23:28.344933 21270  Options.max_write_buffer_number: 2
2025/10/04-18:23:28.344934 21270          Options.compression: LZ4
2025/10/04-18:23:28.344935 21270                  Options.bottommost_compression: Disabled
2025/10/04-18:23:28.344936 21270       Options.prefix_extractor: nullptr
2025/10/04-18:23:28.344936 21270   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/10/04-18:23:28.344937 21270             Options.num_levels: 7
2025/10/04-18:23:28.344938 21270        Options.min_write_buffer_number_to_merge: 1
2025/10/04-18:23:28.344939 21270     Options.max_write_buffer_number_to_maintain: 0
2025/10/04-18:23:28.344940 21270     Options.max_write_buffer_size_to_maintain: 0
2025/10/04-18:23:28.344940 21270            Options.bottommost_compression_opts.window_bits: -14
2025/10/04-18:23:28.344941 21270                  Options.bottommost_compression_opts.level: 32767
2025/10/04-18:23:28.344942 21270               Options.bottommost_compression_opts.strategy: 0
2025/10/04-18:23:28.344943 21270         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.344944 21270         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.344945 21270         Options.bottommost_compression_opts.parallel_threads: 1
2025/10/04-18:23:28.344945 21270                  Options.bottommost_compression_opts.enabled: false
2025/10/04-18:23:28.344946 21270         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.344947 21270         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.344948 21270            Options.compression_opts.window_bits: -14
2025/10/04-18:23:28.344949 21270                  Options.compression_opts.level: 32767
2025/10/04-18:23:28.344949 21270               Options.compression_opts.strategy: 0
2025/10/04-18:23:28.344950 21270         Options.compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.344951 21270         Options.compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.344952 21270         Options.compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.344952 21270         Options.compression_opts.parallel_threads: 1
2025/10/04-18:23:28.344953 21270                  Options.compression_opts.enabled: false
2025/10/04-18:23:28.344954 21270         Options.compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.344955 21270      Options.level0_file_num_compaction_trigger: 4
2025/10/04-18:23:28.344956 21270          Options.level0_slowdown_writes_trigger: 20
2025/10/04-18:23:28.344957 21270              Options.level0_stop_writes_trigger: 36
2025/10/04-18:23:28.344957 21270                   Options.target_file_size_base: 67108864
2025/10/04-18:23:28.344958 21270             Options.target_file_size_multiplier: 1
2025/10/04-18:23:28.344959 21270                Options.max_bytes_for_level_base: 268435456
2025/10/04-18:23:28.344960 21270 Options.level_compaction_dynamic_level_bytes: 1
2025/10/04-18:23:28.344961 21270          Options.max_bytes_for_level_multiplier: 10.000000
2025/10/04-18:23:28.344962 21270 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/10/04-18:23:28.344962 21270 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/10/04-18:23:28.344963 21270 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/10/04-18:23:28.344964 21270 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/10/04-18:23:28.344965 21270 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/10/04-18:23:28.344965 21270 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/10/04-18:23:28.344966 21270 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/10/04-18:23:28.344967 21270       Options.max_sequential_skip_in_iterations: 8
2025/10/04-18:23:28.344968 21270                    Options.max_compaction_bytes: 1677721600
2025/10/04-18:23:28.344969 21270                        Options.arena_block_size: 1048576
2025/10/04-18:23:28.344969 21270   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/10/04-18:23:28.344970 21270   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/10/04-18:23:28.344971 21270                Options.disable_auto_compactions: 0
2025/10/04-18:23:28.344972 21270                        Options.compaction_style: kCompactionStyleLevel
2025/10/04-18:23:28.344973 21270                          Options.compaction_pri: kMinOverlappingRatio
2025/10/04-18:23:28.344974 21270 Options.compaction_options_universal.size_ratio: 1
2025/10/04-18:23:28.344974 21270 Options.compaction_options_universal.min_merge_width: 2
2025/10/04-18:23:28.344975 21270 Options.compaction_options_universal.max_merge_width: 4294967295
2025/10/04-18:23:28.344976 21270 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/10/04-18:23:28.344977 21270 Options.compaction_options_universal.compression_size_percent: -1
2025/10/04-18:23:28.344978 21270 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/10/04-18:23:28.344979 21270 Options.compaction_options_universal.max_read_amp: -1
2025/10/04-18:23:28.344980 21270 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/10/04-18:23:28.344980 21270 Options.compaction_options_fifo.allow_compaction: 0
2025/10/04-18:23:28.344981 21270                   Options.table_properties_collectors: 
2025/10/04-18:23:28.344982 21270                   Options.inplace_update_support: 0
2025/10/04-18:23:28.344983 21270                 Options.inplace_update_num_locks: 10000
2025/10/04-18:23:28.344984 21270               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/10/04-18:23:28.344985 21270               Options.memtable_whole_key_filtering: 0
2025/10/04-18:23:28.344985 21270   Options.memtable_huge_page_size: 0
2025/10/04-18:23:28.344986 21270                           Options.bloom_locality: 0
2025/10/04-18:23:28.344987 21270                    Options.max_successive_merges: 0
2025/10/04-18:23:28.344988 21270             Options.strict_max_successive_merges: 0
2025/10/04-18:23:28.344988 21270                Options.optimize_filters_for_hits: 0
2025/10/04-18:23:28.344989 21270                Options.paranoid_file_checks: 0
2025/10/04-18:23:28.344990 21270                Options.force_consistency_checks: 1
2025/10/04-18:23:28.344991 21270                Options.report_bg_io_stats: 0
2025/10/04-18:23:28.344992 21270                               Options.ttl: 2592000
2025/10/04-18:23:28.344993 21270          Options.periodic_compaction_seconds: 0
2025/10/04-18:23:28.344993 21270                        Options.default_temperature: kUnknown
2025/10/04-18:23:28.344994 21270  Options.preclude_last_level_data_seconds: 0
2025/10/04-18:23:28.344995 21270    Options.preserve_internal_time_seconds: 0
2025/10/04-18:23:28.344996 21270                       Options.enable_blob_files: false
2025/10/04-18:23:28.344996 21270                           Options.min_blob_size: 0
2025/10/04-18:23:28.344997 21270                          Options.blob_file_size: 268435456
2025/10/04-18:23:28.344998 21270                   Options.blob_compression_type: NoCompression
2025/10/04-18:23:28.344999 21270          Options.enable_blob_garbage_collection: false
2025/10/04-18:23:28.345000 21270      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/10/04-18:23:28.345001 21270 Options.blob_garbage_collection_force_threshold: 1.000000
2025/10/04-18:23:28.345001 21270          Options.blob_compaction_readahead_size: 0
2025/10/04-18:23:28.345002 21270                Options.blob_file_starting_level: 0
2025/10/04-18:23:28.345003 21270         Options.experimental_mempurge_threshold: 0.000000
2025/10/04-18:23:28.345004 21270            Options.memtable_max_range_deletions: 0
2025/10/04-18:23:28.345023 21270               Options.comparator: leveldb.BytewiseComparator
2025/10/04-18:23:28.345024 21270           Options.merge_operator: None
2025/10/04-18:23:28.345025 21270        Options.compaction_filter: None
2025/10/04-18:23:28.345025 21270        Options.compaction_filter_factory: None
2025/10/04-18:23:28.345026 21270  Options.sst_partitioner_factory: None
2025/10/04-18:23:28.345027 21270         Options.memtable_factory: SkipListFactory
2025/10/04-18:23:28.345028 21270            Options.table_factory: BlockBasedTable
2025/10/04-18:23:28.345034 21270            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8b660840)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8b60b7d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/10/04-18:23:28.345035 21270        Options.write_buffer_size: 10485760
2025/10/04-18:23:28.345036 21270  Options.max_write_buffer_number: 2
2025/10/04-18:23:28.345037 21270          Options.compression: LZ4
2025/10/04-18:23:28.345038 21270                  Options.bottommost_compression: Disabled
2025/10/04-18:23:28.345038 21270       Options.prefix_extractor: nullptr
2025/10/04-18:23:28.345039 21270   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/10/04-18:23:28.345040 21270             Options.num_levels: 7
2025/10/04-18:23:28.345041 21270        Options.min_write_buffer_number_to_merge: 1
2025/10/04-18:23:28.345042 21270     Options.max_write_buffer_number_to_maintain: 0
2025/10/04-18:23:28.345042 21270     Options.max_write_buffer_size_to_maintain: 0
2025/10/04-18:23:28.345043 21270            Options.bottommost_compression_opts.window_bits: -14
2025/10/04-18:23:28.345044 21270                  Options.bottommost_compression_opts.level: 32767
2025/10/04-18:23:28.345045 21270               Options.bottommost_compression_opts.strategy: 0
2025/10/04-18:23:28.345046 21270         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.345047 21270         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.345047 21270         Options.bottommost_compression_opts.parallel_threads: 1
2025/10/04-18:23:28.345048 21270                  Options.bottommost_compression_opts.enabled: false
2025/10/04-18:23:28.345049 21270         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.345050 21270         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.345051 21270            Options.compression_opts.window_bits: -14
2025/10/04-18:23:28.345052 21270                  Options.compression_opts.level: 32767
2025/10/04-18:23:28.345052 21270               Options.compression_opts.strategy: 0
2025/10/04-18:23:28.345053 21270         Options.compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.345054 21270         Options.compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.345055 21270         Options.compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.345055 21270         Options.compression_opts.parallel_threads: 1
2025/10/04-18:23:28.345056 21270                  Options.compression_opts.enabled: false
2025/10/04-18:23:28.345057 21270         Options.compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.345058 21270      Options.level0_file_num_compaction_trigger: 4
2025/10/04-18:23:28.345059 21270          Options.level0_slowdown_writes_trigger: 20
2025/10/04-18:23:28.345059 21270              Options.level0_stop_writes_trigger: 36
2025/10/04-18:23:28.345060 21270                   Options.target_file_size_base: 67108864
2025/10/04-18:23:28.345061 21270             Options.target_file_size_multiplier: 1
2025/10/04-18:23:28.345062 21270                Options.max_bytes_for_level_base: 268435456
2025/10/04-18:23:28.345062 21270 Options.level_compaction_dynamic_level_bytes: 1
2025/10/04-18:23:28.345063 21270          Options.max_bytes_for_level_multiplier: 10.000000
2025/10/04-18:23:28.345064 21270 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/10/04-18:23:28.345065 21270 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/10/04-18:23:28.345066 21270 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/10/04-18:23:28.345067 21270 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/10/04-18:23:28.345067 21270 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/10/04-18:23:28.345068 21270 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/10/04-18:23:28.345069 21270 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/10/04-18:23:28.345070 21270       Options.max_sequential_skip_in_iterations: 8
2025/10/04-18:23:28.345071 21270                    Options.max_compaction_bytes: 1677721600
2025/10/04-18:23:28.345072 21270                        Options.arena_block_size: 1048576
2025/10/04-18:23:28.345072 21270   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/10/04-18:23:28.345073 21270   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/10/04-18:23:28.345074 21270                Options.disable_auto_compactions: 0
2025/10/04-18:23:28.345075 21270                        Options.compaction_style: kCompactionStyleLevel
2025/10/04-18:23:28.345075 21270                          Options.compaction_pri: kMinOverlappingRatio
2025/10/04-18:23:28.345076 21270 Options.compaction_options_universal.size_ratio: 1
2025/10/04-18:23:28.345077 21270 Options.compaction_options_universal.min_merge_width: 2
2025/10/04-18:23:28.345078 21270 Options.compaction_options_universal.max_merge_width: 4294967295
2025/10/04-18:23:28.345079 21270 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/10/04-18:23:28.345079 21270 Options.compaction_options_universal.compression_size_percent: -1
2025/10/04-18:23:28.345080 21270 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/10/04-18:23:28.345081 21270 Options.compaction_options_universal.max_read_amp: -1
2025/10/04-18:23:28.345082 21270 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/10/04-18:23:28.345083 21270 Options.compaction_options_fifo.allow_compaction: 0
2025/10/04-18:23:28.345083 21270                   Options.table_properties_collectors: 
2025/10/04-18:23:28.345084 21270                   Options.inplace_update_support: 0
2025/10/04-18:23:28.345085 21270                 Options.inplace_update_num_locks: 10000
2025/10/04-18:23:28.345086 21270               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/10/04-18:23:28.345087 21270               Options.memtable_whole_key_filtering: 0
2025/10/04-18:23:28.345088 21270   Options.memtable_huge_page_size: 0
2025/10/04-18:23:28.345088 21270                           Options.bloom_locality: 0
2025/10/04-18:23:28.345089 21270                    Options.max_successive_merges: 0
2025/10/04-18:23:28.345090 21270             Options.strict_max_successive_merges: 0
2025/10/04-18:23:28.345091 21270                Options.optimize_filters_for_hits: 0
2025/10/04-18:23:28.345091 21270                Options.paranoid_file_checks: 0
2025/10/04-18:23:28.345092 21270                Options.force_consistency_checks: 1
2025/10/04-18:23:28.345093 21270                Options.report_bg_io_stats: 0
2025/10/04-18:23:28.345094 21270                               Options.ttl: 2592000
2025/10/04-18:23:28.345095 21270          Options.periodic_compaction_seconds: 0
2025/10/04-18:23:28.345095 21270                        Options.default_temperature: kUnknown
2025/10/04-18:23:28.345096 21270  Options.preclude_last_level_data_seconds: 0
2025/10/04-18:23:28.345097 21270    Options.preserve_internal_time_seconds: 0
2025/10/04-18:23:28.345098 21270                       Options.enable_blob_files: false
2025/10/04-18:23:28.345098 21270                           Options.min_blob_size: 0
2025/10/04-18:23:28.345099 21270                          Options.blob_file_size: 268435456
2025/10/04-18:23:28.345100 21270                   Options.blob_compression_type: NoCompression
2025/10/04-18:23:28.345101 21270          Options.enable_blob_garbage_collection: false
2025/10/04-18:23:28.345101 21270      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/10/04-18:23:28.345102 21270 Options.blob_garbage_collection_force_threshold: 1.000000
2025/10/04-18:23:28.345103 21270          Options.blob_compaction_readahead_size: 0
2025/10/04-18:23:28.345104 21270                Options.blob_file_starting_level: 0
2025/10/04-18:23:28.345105 21270         Options.experimental_mempurge_threshold: 0.000000
2025/10/04-18:23:28.345106 21270            Options.memtable_max_range_deletions: 0
2025/10/04-18:23:28.345124 21270               Options.comparator: leveldb.BytewiseComparator
2025/10/04-18:23:28.345125 21270           Options.merge_operator: None
2025/10/04-18:23:28.345126 21270        Options.compaction_filter: None
2025/10/04-18:23:28.345127 21270        Options.compaction_filter_factory: None
2025/10/04-18:23:28.345127 21270  Options.sst_partitioner_factory: None
2025/10/04-18:23:28.345128 21270         Options.memtable_factory: SkipListFactory
2025/10/04-18:23:28.345129 21270            Options.table_factory: BlockBasedTable
2025/10/04-18:23:28.345134 21270            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8b660840)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8b60b7d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/10/04-18:23:28.345136 21270        Options.write_buffer_size: 10485760
2025/10/04-18:23:28.345137 21270  Options.max_write_buffer_number: 2
2025/10/04-18:23:28.345138 21270          Options.compression: LZ4
2025/10/04-18:23:28.345139 21270                  Options.bottommost_compression: Disabled
2025/10/04-18:23:28.345140 21270       Options.prefix_extractor: nullptr
2025/10/04-18:23:28.345140 21270   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/10/04-18:23:28.345141 21270             Options.num_levels: 7
2025/10/04-18:23:28.345142 21270        Options.min_write_buffer_number_to_merge: 1
2025/10/04-18:23:28.345143 21270     Options.max_write_buffer_number_to_maintain: 0
2025/10/04-18:23:28.345144 21270     Options.max_write_buffer_size_to_maintain: 0
2025/10/04-18:23:28.345144 21270            Options.bottommost_compression_opts.window_bits: -14
2025/10/04-18:23:28.345145 21270                  Options.bottommost_compression_opts.level: 32767
2025/10/04-18:23:28.345146 21270               Options.bottommost_compression_opts.strategy: 0
2025/10/04-18:23:28.345147 21270         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.345148 21270         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.345148 21270         Options.bottommost_compression_opts.parallel_threads: 1
2025/10/04-18:23:28.345149 21270                  Options.bottommost_compression_opts.enabled: false
2025/10/04-18:23:28.345150 21270         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.345151 21270         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.345152 21270            Options.compression_opts.window_bits: -14
2025/10/04-18:23:28.345153 21270                  Options.compression_opts.level: 32767
2025/10/04-18:23:28.345153 21270               Options.compression_opts.strategy: 0
2025/10/04-18:23:28.345154 21270         Options.compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.345155 21270         Options.compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.345156 21270         Options.compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.345157 21270         Options.compression_opts.parallel_threads: 1
2025/10/04-18:23:28.345157 21270                  Options.compression_opts.enabled: false
2025/10/04-18:23:28.345158 21270         Options.compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.345159 21270      Options.level0_file_num_compaction_trigger: 4
2025/10/04-18:23:28.345160 21270          Options.level0_slowdown_writes_trigger: 20
2025/10/04-18:23:28.345160 21270              Options.level0_stop_writes_trigger: 36
2025/10/04-18:23:28.345161 21270                   Options.target_file_size_base: 67108864
2025/10/04-18:23:28.345162 21270             Options.target_file_size_multiplier: 1
2025/10/04-18:23:28.345163 21270                Options.max_bytes_for_level_base: 268435456
2025/10/04-18:23:28.345163 21270 Options.level_compaction_dynamic_level_bytes: 1
2025/10/04-18:23:28.345164 21270          Options.max_bytes_for_level_multiplier: 10.000000
2025/10/04-18:23:28.345165 21270 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/10/04-18:23:28.345166 21270 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/10/04-18:23:28.345167 21270 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/10/04-18:23:28.345168 21270 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/10/04-18:23:28.345168 21270 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/10/04-18:23:28.345169 21270 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/10/04-18:23:28.345170 21270 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/10/04-18:23:28.345171 21270       Options.max_sequential_skip_in_iterations: 8
2025/10/04-18:23:28.345172 21270                    Options.max_compaction_bytes: 1677721600
2025/10/04-18:23:28.345172 21270                        Options.arena_block_size: 1048576
2025/10/04-18:23:28.345173 21270   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/10/04-18:23:28.345174 21270   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/10/04-18:23:28.345175 21270                Options.disable_auto_compactions: 0
2025/10/04-18:23:28.345176 21270                        Options.compaction_style: kCompactionStyleLevel
2025/10/04-18:23:28.345177 21270                          Options.compaction_pri: kMinOverlappingRatio
2025/10/04-18:23:28.345177 21270 Options.compaction_options_universal.size_ratio: 1
2025/10/04-18:23:28.345178 21270 Options.compaction_options_universal.min_merge_width: 2
2025/10/04-18:23:28.345179 21270 Options.compaction_options_universal.max_merge_width: 4294967295
2025/10/04-18:23:28.345180 21270 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/10/04-18:23:28.345181 21270 Options.compaction_options_universal.compression_size_percent: -1
2025/10/04-18:23:28.345182 21270 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/10/04-18:23:28.345182 21270 Options.compaction_options_universal.max_read_amp: -1
2025/10/04-18:23:28.345183 21270 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/10/04-18:23:28.345184 21270 Options.compaction_options_fifo.allow_compaction: 0
2025/10/04-18:23:28.345185 21270                   Options.table_properties_collectors: 
2025/10/04-18:23:28.345186 21270                   Options.inplace_update_support: 0
2025/10/04-18:23:28.345186 21270                 Options.inplace_update_num_locks: 10000
2025/10/04-18:23:28.345187 21270               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/10/04-18:23:28.345188 21270               Options.memtable_whole_key_filtering: 0
2025/10/04-18:23:28.345189 21270   Options.memtable_huge_page_size: 0
2025/10/04-18:23:28.345190 21270                           Options.bloom_locality: 0
2025/10/04-18:23:28.345190 21270                    Options.max_successive_merges: 0
2025/10/04-18:23:28.345191 21270             Options.strict_max_successive_merges: 0
2025/10/04-18:23:28.345192 21270                Options.optimize_filters_for_hits: 0
2025/10/04-18:23:28.345193 21270                Options.paranoid_file_checks: 0
2025/10/04-18:23:28.345194 21270                Options.force_consistency_checks: 1
2025/10/04-18:23:28.345194 21270                Options.report_bg_io_stats: 0
2025/10/04-18:23:28.345195 21270                               Options.ttl: 2592000
2025/10/04-18:23:28.345196 21270          Options.periodic_compaction_seconds: 0
2025/10/04-18:23:28.345197 21270                        Options.default_temperature: kUnknown
2025/10/04-18:23:28.345197 21270  Options.preclude_last_level_data_seconds: 0
2025/10/04-18:23:28.345198 21270    Options.preserve_internal_time_seconds: 0
2025/10/04-18:23:28.345199 21270                       Options.enable_blob_files: false
2025/10/04-18:23:28.345200 21270                           Options.min_blob_size: 0
2025/10/04-18:23:28.345200 21270                          Options.blob_file_size: 268435456
2025/10/04-18:23:28.345201 21270                   Options.blob_compression_type: NoCompression
2025/10/04-18:23:28.345202 21270          Options.enable_blob_garbage_collection: false
2025/10/04-18:23:28.345203 21270      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/10/04-18:23:28.345204 21270 Options.blob_garbage_collection_force_threshold: 1.000000
2025/10/04-18:23:28.345204 21270          Options.blob_compaction_readahead_size: 0
2025/10/04-18:23:28.345205 21270                Options.blob_file_starting_level: 0
2025/10/04-18:23:28.345206 21270         Options.experimental_mempurge_threshold: 0.000000
2025/10/04-18:23:28.345207 21270            Options.memtable_max_range_deletions: 0
2025/10/04-18:23:28.345223 21270               Options.comparator: leveldb.BytewiseComparator
2025/10/04-18:23:28.345224 21270           Options.merge_operator: None
2025/10/04-18:23:28.345225 21270        Options.compaction_filter: None
2025/10/04-18:23:28.345225 21270        Options.compaction_filter_factory: None
2025/10/04-18:23:28.345226 21270  Options.sst_partitioner_factory: None
2025/10/04-18:23:28.345227 21270         Options.memtable_factory: SkipListFactory
2025/10/04-18:23:28.345228 21270            Options.table_factory: BlockBasedTable
2025/10/04-18:23:28.345233 21270            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8b660840)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8b60b7d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/10/04-18:23:28.345234 21270        Options.write_buffer_size: 10485760
2025/10/04-18:23:28.345235 21270  Options.max_write_buffer_number: 2
2025/10/04-18:23:28.345236 21270          Options.compression: LZ4
2025/10/04-18:23:28.345237 21270                  Options.bottommost_compression: Disabled
2025/10/04-18:23:28.345238 21270       Options.prefix_extractor: nullptr
2025/10/04-18:23:28.345238 21270   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/10/04-18:23:28.345239 21270             Options.num_levels: 7
2025/10/04-18:23:28.345240 21270        Options.min_write_buffer_number_to_merge: 1
2025/10/04-18:23:28.345241 21270     Options.max_write_buffer_number_to_maintain: 0
2025/10/04-18:23:28.345242 21270     Options.max_write_buffer_size_to_maintain: 0
2025/10/04-18:23:28.345243 21270            Options.bottommost_compression_opts.window_bits: -14
2025/10/04-18:23:28.345244 21270                  Options.bottommost_compression_opts.level: 32767
2025/10/04-18:23:28.345245 21270               Options.bottommost_compression_opts.strategy: 0
2025/10/04-18:23:28.345245 21270         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.345246 21270         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.345247 21270         Options.bottommost_compression_opts.parallel_threads: 1
2025/10/04-18:23:28.345248 21270                  Options.bottommost_compression_opts.enabled: false
2025/10/04-18:23:28.345249 21270         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.345249 21270         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.345250 21270            Options.compression_opts.window_bits: -14
2025/10/04-18:23:28.345251 21270                  Options.compression_opts.level: 32767
2025/10/04-18:23:28.345252 21270               Options.compression_opts.strategy: 0
2025/10/04-18:23:28.345253 21270         Options.compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.345253 21270         Options.compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.345254 21270         Options.compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.345255 21270         Options.compression_opts.parallel_threads: 1
2025/10/04-18:23:28.345256 21270                  Options.compression_opts.enabled: false
2025/10/04-18:23:28.345257 21270         Options.compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.345257 21270      Options.level0_file_num_compaction_trigger: 4
2025/10/04-18:23:28.345258 21270          Options.level0_slowdown_writes_trigger: 20
2025/10/04-18:23:28.345259 21270              Options.level0_stop_writes_trigger: 36
2025/10/04-18:23:28.345260 21270                   Options.target_file_size_base: 67108864
2025/10/04-18:23:28.345260 21270             Options.target_file_size_multiplier: 1
2025/10/04-18:23:28.345261 21270                Options.max_bytes_for_level_base: 268435456
2025/10/04-18:23:28.345262 21270 Options.level_compaction_dynamic_level_bytes: 1
2025/10/04-18:23:28.345263 21270          Options.max_bytes_for_level_multiplier: 10.000000
2025/10/04-18:23:28.345264 21270 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/10/04-18:23:28.345265 21270 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/10/04-18:23:28.345266 21270 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/10/04-18:23:28.345266 21270 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/10/04-18:23:28.345267 21270 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/10/04-18:23:28.345268 21270 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/10/04-18:23:28.345269 21270 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/10/04-18:23:28.345270 21270       Options.max_sequential_skip_in_iterations: 8
2025/10/04-18:23:28.345270 21270                    Options.max_compaction_bytes: 1677721600
2025/10/04-18:23:28.345271 21270                        Options.arena_block_size: 1048576
2025/10/04-18:23:28.345272 21270   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/10/04-18:23:28.345273 21270   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/10/04-18:23:28.345274 21270                Options.disable_auto_compactions: 0
2025/10/04-18:23:28.345274 21270                        Options.compaction_style: kCompactionStyleLevel
2025/10/04-18:23:28.345275 21270                          Options.compaction_pri: kMinOverlappingRatio
2025/10/04-18:23:28.345276 21270 Options.compaction_options_universal.size_ratio: 1
2025/10/04-18:23:28.345277 21270 Options.compaction_options_universal.min_merge_width: 2
2025/10/04-18:23:28.345278 21270 Options.compaction_options_universal.max_merge_width: 4294967295
2025/10/04-18:23:28.345279 21270 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/10/04-18:23:28.345280 21270 Options.compaction_options_universal.compression_size_percent: -1
2025/10/04-18:23:28.345280 21270 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/10/04-18:23:28.345281 21270 Options.compaction_options_universal.max_read_amp: -1
2025/10/04-18:23:28.345282 21270 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/10/04-18:23:28.345283 21270 Options.compaction_options_fifo.allow_compaction: 0
2025/10/04-18:23:28.345284 21270                   Options.table_properties_collectors: 
2025/10/04-18:23:28.345284 21270                   Options.inplace_update_support: 0
2025/10/04-18:23:28.345285 21270                 Options.inplace_update_num_locks: 10000
2025/10/04-18:23:28.345286 21270               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/10/04-18:23:28.345287 21270               Options.memtable_whole_key_filtering: 0
2025/10/04-18:23:28.345288 21270   Options.memtable_huge_page_size: 0
2025/10/04-18:23:28.345288 21270                           Options.bloom_locality: 0
2025/10/04-18:23:28.345289 21270                    Options.max_successive_merges: 0
2025/10/04-18:23:28.345290 21270             Options.strict_max_successive_merges: 0
2025/10/04-18:23:28.345291 21270                Options.optimize_filters_for_hits: 0
2025/10/04-18:23:28.345291 21270                Options.paranoid_file_checks: 0
2025/10/04-18:23:28.345292 21270                Options.force_consistency_checks: 1
2025/10/04-18:23:28.345293 21270                Options.report_bg_io_stats: 0
2025/10/04-18:23:28.345294 21270                               Options.ttl: 2592000
2025/10/04-18:23:28.345295 21270          Options.periodic_compaction_seconds: 0
2025/10/04-18:23:28.345295 21270                        Options.default_temperature: kUnknown
2025/10/04-18:23:28.345297 21270  Options.preclude_last_level_data_seconds: 0
2025/10/04-18:23:28.345297 21270    Options.preserve_internal_time_seconds: 0
2025/10/04-18:23:28.345298 21270                       Options.enable_blob_files: false
2025/10/04-18:23:28.345299 21270                           Options.min_blob_size: 0
2025/10/04-18:23:28.345300 21270                          Options.blob_file_size: 268435456
2025/10/04-18:23:28.345301 21270                   Options.blob_compression_type: NoCompression
2025/10/04-18:23:28.345302 21270          Options.enable_blob_garbage_collection: false
2025/10/04-18:23:28.345303 21270      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/10/04-18:23:28.345304 21270 Options.blob_garbage_collection_force_threshold: 1.000000
2025/10/04-18:23:28.345304 21270          Options.blob_compaction_readahead_size: 0
2025/10/04-18:23:28.345305 21270                Options.blob_file_starting_level: 0
2025/10/04-18:23:28.345306 21270         Options.experimental_mempurge_threshold: 0.000000
2025/10/04-18:23:28.345307 21270            Options.memtable_max_range_deletions: 0
2025/10/04-18:23:28.345331 21270               Options.comparator: leveldb.BytewiseComparator
2025/10/04-18:23:28.345332 21270           Options.merge_operator: None
2025/10/04-18:23:28.345333 21270        Options.compaction_filter: None
2025/10/04-18:23:28.345334 21270        Options.compaction_filter_factory: None
2025/10/04-18:23:28.345335 21270  Options.sst_partitioner_factory: None
2025/10/04-18:23:28.345336 21270         Options.memtable_factory: SkipListFactory
2025/10/04-18:23:28.345336 21270            Options.table_factory: BlockBasedTable
2025/10/04-18:23:28.345342 21270            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff8b660840)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0xffff8b60b7d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/10/04-18:23:28.345343 21270        Options.write_buffer_size: 10485760
2025/10/04-18:23:28.345344 21270  Options.max_write_buffer_number: 2
2025/10/04-18:23:28.345345 21270          Options.compression: LZ4
2025/10/04-18:23:28.345346 21270                  Options.bottommost_compression: Disabled
2025/10/04-18:23:28.345346 21270       Options.prefix_extractor: nullptr
2025/10/04-18:23:28.345347 21270   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/10/04-18:23:28.345348 21270             Options.num_levels: 7
2025/10/04-18:23:28.345349 21270        Options.min_write_buffer_number_to_merge: 1
2025/10/04-18:23:28.345349 21270     Options.max_write_buffer_number_to_maintain: 0
2025/10/04-18:23:28.345350 21270     Options.max_write_buffer_size_to_maintain: 0
2025/10/04-18:23:28.345351 21270            Options.bottommost_compression_opts.window_bits: -14
2025/10/04-18:23:28.345352 21270                  Options.bottommost_compression_opts.level: 32767
2025/10/04-18:23:28.345353 21270               Options.bottommost_compression_opts.strategy: 0
2025/10/04-18:23:28.345354 21270         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.345355 21270         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.345356 21270         Options.bottommost_compression_opts.parallel_threads: 1
2025/10/04-18:23:28.345357 21270                  Options.bottommost_compression_opts.enabled: false
2025/10/04-18:23:28.345358 21270         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.345359 21270         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.345360 21270            Options.compression_opts.window_bits: -14
2025/10/04-18:23:28.345361 21270                  Options.compression_opts.level: 32767
2025/10/04-18:23:28.345362 21270               Options.compression_opts.strategy: 0
2025/10/04-18:23:28.345363 21270         Options.compression_opts.max_dict_bytes: 0
2025/10/04-18:23:28.345364 21270         Options.compression_opts.zstd_max_train_bytes: 0
2025/10/04-18:23:28.345364 21270         Options.compression_opts.use_zstd_dict_trainer: true
2025/10/04-18:23:28.345365 21270         Options.compression_opts.parallel_threads: 1
2025/10/04-18:23:28.345366 21270                  Options.compression_opts.enabled: false
2025/10/04-18:23:28.345367 21270         Options.compression_opts.max_dict_buffer_bytes: 0
2025/10/04-18:23:28.345367 21270      Options.level0_file_num_compaction_trigger: 4
2025/10/04-18:23:28.345368 21270          Options.level0_slowdown_writes_trigger: 20
2025/10/04-18:23:28.345369 21270              Options.level0_stop_writes_trigger: 36
2025/10/04-18:23:28.345370 21270                   Options.target_file_size_base: 67108864
2025/10/04-18:23:28.345371 21270             Options.target_file_size_multiplier: 1
2025/10/04-18:23:28.345372 21270                Options.max_bytes_for_level_base: 268435456
2025/10/04-18:23:28.345373 21270 Options.level_compaction_dynamic_level_bytes: 1
2025/10/04-18:23:28.345374 21270          Options.max_bytes_for_level_multiplier: 10.000000
2025/10/04-18:23:28.345375 21270 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/10/04-18:23:28.345376 21270 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/10/04-18:23:28.345377 21270 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/10/04-18:23:28.345377 21270 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/10/04-18:23:28.345378 21270 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/10/04-18:23:28.345379 21270 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/10/04-18:23:28.345380 21270 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/10/04-18:23:28.345381 21270       Options.max_sequential_skip_in_iterations: 8
2025/10/04-18:23:28.345382 21270                    Options.max_compaction_bytes: 1677721600
2025/10/04-18:23:28.345382 21270                        Options.arena_block_size: 1048576
2025/10/04-18:23:28.345383 21270   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/10/04-18:23:28.345384 21270   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/10/04-18:23:28.345385 21270                Options.disable_auto_compactions: 0
2025/10/04-18:23:28.345385 21270                        Options.compaction_style: kCompactionStyleLevel
2025/10/04-18:23:28.345386 21270                          Options.compaction_pri: kMinOverlappingRatio
2025/10/04-18:23:28.345387 21270 Options.compaction_options_universal.size_ratio: 1
2025/10/04-18:23:28.345388 21270 Options.compaction_options_universal.min_merge_width: 2
2025/10/04-18:23:28.345389 21270 Options.compaction_options_universal.max_merge_width: 4294967295
2025/10/04-18:23:28.345389 21270 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/10/04-18:23:28.345390 21270 Options.compaction_options_universal.compression_size_percent: -1
2025/10/04-18:23:28.345391 21270 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/10/04-18:23:28.345392 21270 Options.compaction_options_universal.max_read_amp: -1
2025/10/04-18:23:28.345393 21270 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/10/04-18:23:28.345394 21270 Options.compaction_options_fifo.allow_compaction: 0
2025/10/04-18:23:28.345395 21270                   Options.table_properties_collectors: 
2025/10/04-18:23:28.345395 21270                   Options.inplace_update_support: 0
2025/10/04-18:23:28.345396 21270                 Options.inplace_update_num_locks: 10000
2025/10/04-18:23:28.345397 21270               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/10/04-18:23:28.345398 21270               Options.memtable_whole_key_filtering: 0
2025/10/04-18:23:28.345399 21270   Options.memtable_huge_page_size: 0
2025/10/04-18:23:28.345400 21270                           Options.bloom_locality: 0
2025/10/04-18:23:28.345400 21270                    Options.max_successive_merges: 0
2025/10/04-18:23:28.345401 21270             Options.strict_max_successive_merges: 0
2025/10/04-18:23:28.345402 21270                Options.optimize_filters_for_hits: 0
2025/10/04-18:23:28.345403 21270                Options.paranoid_file_checks: 0
2025/10/04-18:23:28.345403 21270                Options.force_consistency_checks: 1
2025/10/04-18:23:28.345404 21270                Options.report_bg_io_stats: 0
2025/10/04-18:23:28.345405 21270                               Options.ttl: 2592000
2025/10/04-18:23:28.345406 21270          Options.periodic_compaction_seconds: 0
2025/10/04-18:23:28.345407 21270                        Options.default_temperature: kUnknown
2025/10/04-18:23:28.345407 21270  Options.preclude_last_level_data_seconds: 0
2025/10/04-18:23:28.345408 21270    Options.preserve_internal_time_seconds: 0
2025/10/04-18:23:28.345409 21270                       Options.enable_blob_files: false
2025/10/04-18:23:28.345410 21270                           Options.min_blob_size: 0
2025/10/04-18:23:28.345410 21270                          Options.blob_file_size: 268435456
2025/10/04-18:23:28.345411 21270                   Options.blob_compression_type: NoCompression
2025/10/04-18:23:28.345412 21270          Options.enable_blob_garbage_collection: false
2025/10/04-18:23:28.345413 21270      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/10/04-18:23:28.345416 21270 Options.blob_garbage_collection_force_threshold: 1.000000
2025/10/04-18:23:28.345417 21270          Options.blob_compaction_readahead_size: 0
2025/10/04-18:23:28.345418 21270                Options.blob_file_starting_level: 0
2025/10/04-18:23:28.345419 21270         Options.experimental_mempurge_threshold: 0.000000
2025/10/04-18:23:28.345419 21270            Options.memtable_max_range_deletions: 0
2025/10/04-18:23:28.366380 21270 DB pointer 0xffff7012c000
