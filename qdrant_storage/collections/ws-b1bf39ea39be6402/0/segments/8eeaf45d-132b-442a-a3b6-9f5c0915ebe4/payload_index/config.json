{"indexed_fields": {"pathSegments.4": "keyword", "pathSegments.1": "keyword", "pathSegments.0": "keyword", "pathSegments.3": "keyword", "pathSegments.2": "keyword"}, "indexed_types": {"pathSegments.1": [{"index_type": "keyword_index", "mutability": "immutable", "storage_type": {"mmap": {"is_on_disk": false}}}, {"index_type": "null_index", "mutability": "mutable", "storage_type": {"mmap": {"is_on_disk": false}}}], "pathSegments.4": [{"index_type": "keyword_index", "mutability": "immutable", "storage_type": {"mmap": {"is_on_disk": false}}}, {"index_type": "null_index", "mutability": "mutable", "storage_type": {"mmap": {"is_on_disk": false}}}], "pathSegments.3": [{"index_type": "keyword_index", "mutability": "immutable", "storage_type": {"mmap": {"is_on_disk": false}}}, {"index_type": "null_index", "mutability": "mutable", "storage_type": {"mmap": {"is_on_disk": false}}}], "pathSegments.0": [{"index_type": "keyword_index", "mutability": "immutable", "storage_type": {"mmap": {"is_on_disk": false}}}, {"index_type": "null_index", "mutability": "mutable", "storage_type": {"mmap": {"is_on_disk": false}}}], "pathSegments.2": [{"index_type": "keyword_index", "mutability": "immutable", "storage_type": {"mmap": {"is_on_disk": false}}}, {"index_type": "null_index", "mutability": "mutable", "storage_type": {"mmap": {"is_on_disk": false}}}]}, "skip_rocksdb": true}