// Run this in browser console to inspect chart SVG elements
console.log('🔍 Inspecting Temperature Chart SVG...');

// Find the chart container
const chart = document.querySelector('.recharts-wrapper');
if (!chart) {
  console.error('❌ Chart not found');
} else {
  console.log('✅ Chart found:', chart);

  // Find all Line elements
  const lines = chart.querySelectorAll('.recharts-line path.recharts-line-curve');
  console.log(`📊 Found ${lines.length} line paths`);

  lines.forEach((line, index) => {
    const d = line.getAttribute('d');
    const stroke = line.getAttribute('stroke');
    const strokeWidth = line.getAttribute('stroke-width');
    const display = window.getComputedStyle(line).display;
    const visibility = window.getComputedStyle(line).visibility;

    console.log(`Line ${index}:`, {
      hasPath: !!d,
      pathLength: d?.length || 0,
      pathPreview: d?.substring(0, 50),
      stroke,
      strokeWidth,
      display,
      visibility,
      element: line
    });
  });

  // Find all dots
  const dots = chart.querySelectorAll('.recharts-line .recharts-line-dots circle');
  console.log(`🔵 Found ${dots.length} dots`);

  // Check if animation is preventing rendering
  const lineGroups = chart.querySelectorAll('.recharts-line');
  console.log(`📈 Found ${lineGroups.length} line groups`);

  lineGroups.forEach((group, index) => {
    const transform = group.getAttribute('transform');
    const opacity = window.getComputedStyle(group).opacity;
    console.log(`Line group ${index}:`, { transform, opacity });
  });

  // Log the data being passed to the chart
  console.log('\n💾 Chart Data Sample:');
  const dataScript = Array.from(document.querySelectorAll('script'))
    .find(s => s.textContent.includes('chartData'));
  if (dataScript) {
    console.log('Found data script');
  }
}
