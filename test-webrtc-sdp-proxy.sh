#!/bin/bash

# Test WebRTC SDP Proxy
# This tests if the server correctly proxies SDP offers to OpenAI

echo "🧪 Testing WebRTC SDP Proxy"
echo

# Create a sample SDP offer (minimal valid SDP)
SDP_OFFER='v=0
o=- 0 0 IN IP4 127.0.0.1
s=-
t=0 0
m=audio 9 UDP/TLS/RTP/SAVPF 111
c=IN IP4 0.0.0.0
a=rtcp:9 IN IP4 0.0.0.0
a=ice-ufrag:test
a=ice-pwd:testpassword
a=fingerprint:sha-256 00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:00
a=setup:actpass
a=mid:0
a=sendrecv
a=rtpmap:111 opus/48000/2
'

echo "1️⃣ Testing SDP proxy endpoint..."
echo

RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" \
  -X POST \
  -H "Content-Type: application/sdp" \
  -H "Accept: application/sdp" \
  -d "$SDP_OFFER" \
  http://localhost:3001/api/openai/realtime/calls?model=gpt-4o-realtime-preview-2024-12-17)

HTTP_STATUS=$(echo "$RESPONSE" | grep "HTTP_STATUS" | cut -d: -f2)
BODY=$(echo "$RESPONSE" | sed '/HTTP_STATUS/d')

echo "HTTP Status: $HTTP_STATUS"
echo
echo "Response body (first 200 chars):"
echo "$BODY" | head -c 200
echo
echo

if [[ $HTTP_STATUS == "200" ]]; then
  if [[ $BODY == v=* ]]; then
    echo "✅ SDP proxy working - got valid SDP answer"
    echo "   Answer starts with: $(echo "$BODY" | head -1)"
  else
    echo "❌ SDP proxy returned 200 but invalid SDP"
    echo "   Expected to start with 'v=' but got:"
    echo "   $BODY" | head -1
  fi
else
  echo "❌ SDP proxy failed with HTTP $HTTP_STATUS"
  echo "   Response: $BODY"
fi

echo
echo "2️⃣ Checking server logs..."
echo "   (Check terminal running 'npm run dev' for SDP proxy logs)"
