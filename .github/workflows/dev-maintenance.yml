name: Development Maintenance

on:
  schedule:
    - cron: '0 2 * * 0'
  workflow_dispatch:
    inputs:
      cleanup_level:
        description: 'Cleanup level'
        required: true
        default: 'medium'
        type: choice
        options:
          - light
          - medium
          - deep

jobs:
  maintenance:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm install
      - name: Analyze Resources
        run: npm run maintenance:analyze
      - name: Run Maintenance
        run: npm run maintenance:${{ inputs.cleanup_level || 'medium' }}
      - name: Generate Report
        run: npm run monitor:report
      - name: Upload Report
        uses: actions/upload-artifact@v4
        with:
          name: maintenance-report
          path: tmp/resource-report-*.json
