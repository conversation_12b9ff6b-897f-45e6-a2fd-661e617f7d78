<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/ApplicationStub.icns</key>
		<data>
		RYTqh+7iocnEIV8iTs9EgJjEkO4=
		</data>
		<key>Resources/Assets.car</key>
		<data>
		cFSlaz6t1U8mOc1HiDmfBPxM8Xg=
		</data>
		<key>Resources/InfoPlist.loctable</key>
		<data>
		KEQC0DFC9lrETWe0E5eVGGsPylc=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Resources/ApplicationStub.icns</key>
		<dict>
			<key>hash</key>
			<data>
			RYTqh+7iocnEIV8iTs9EgJjEkO4=
			</data>
			<key>hash2</key>
			<data>
			odOqeBevxysHIbR5V5qgZz11qTuV9cL5jKaIcUw3R0I=
			</data>
		</dict>
		<key>Resources/Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			cFSlaz6t1U8mOc1HiDmfBPxM8Xg=
			</data>
			<key>hash2</key>
			<data>
			/IwGAaIGsQovzPJ9zqPsvAMK6JNg49UgGl+9JvAULCs=
			</data>
		</dict>
		<key>Resources/InfoPlist.loctable</key>
		<dict>
			<key>hash</key>
			<data>
			KEQC0DFC9lrETWe0E5eVGGsPylc=
			</data>
			<key>hash2</key>
			<data>
			3cSIcj64rHY2k+pLrnrgd1Li6hmbquwgX94QcefajJ8=
			</data>
		</dict>
		<key>document.wflow</key>
		<dict>
			<key>cdhash</key>
			<data>
			V7CW2R+C37aU6uZRrT4ggNNpfoE=
			</data>
			<key>requirement</key>
			<string>cdhash H"f6b4c32b8107a7531ce8976ffb84964d649685c6" or cdhash H"57b096d91f82dfb694eae651ad3e2080d3697e81"</string>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
