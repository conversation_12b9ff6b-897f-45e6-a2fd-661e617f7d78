<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AMIsApplet</key>
	<true/>
	<key>AMStayOpen</key>
	<false/>
	<key>BuildMachineOSBuild</key>
	<string>23A344014</string>
	<key>CFBundleAllowMixedLocalizations</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>English</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>*</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>Automator workflow file</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>****</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
		</dict>
	</array>
	<key>CFBundleExecutable</key>
	<string>Automator Application Stub</string>
	<key>CFBundleIconFile</key>
	<string>ApplicationStub</string>
	<key>CFBundleIdentifier</key>
	<string>com.apple.automator.Start-Seafood-Servers</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Start Seafood Servers</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.3</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>CFBundleURLTypes</key>
	<array/>
	<key>CFBundleVersion</key>
	<string>528</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string></string>
	<key>DTPlatformName</key>
	<string>macosx</string>
	<key>DTPlatformVersion</key>
	<string>15.6</string>
	<key>DTSDKBuild</key>
	<string>24G78</string>
	<key>DTSDKName</key>
	<string>macosx15.6.internal</string>
	<key>DTXcode</key>
	<string>1630</string>
	<key>DTXcodeBuild</key>
	<string>16E6052g</string>
	<key>LSMinimumSystemVersion</key>
	<string>10.9</string>
	<key>LSUIElement</key>
	<true/>
	<key>NSAppleEventsUsageDescription</key>
	<string>This workflow needs to control other applications to run.</string>
	<key>NSAppleMusicUsageDescription</key>
	<string>This workflow needs access to your music to run.</string>
	<key>NSAppleScriptEnabled</key>
	<string>YES</string>
	<key>NSCalendarsUsageDescription</key>
	<string>This workflow needs access to your calendars to run.</string>
	<key>NSCameraUsageDescription</key>
	<string>This workflow needs access to your camera to run.</string>
	<key>NSContactsUsageDescription</key>
	<string>This workflow needs access to your contacts to run.</string>
	<key>NSHomeKitUsageDescription</key>
	<string>This workflow needs access to your HomeKit Home to run.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This workflow needs access to your microphone to run.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This workflow needs access to your photos to run.</string>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
	<key>NSRemindersUsageDescription</key>
	<string>This workflow needs access to your reminders to run.</string>
	<key>NSServices</key>
	<array/>
	<key>NSSiriUsageDescription</key>
	<string>This workflow needs access to Siri to run.</string>
	<key>NSSystemAdministrationUsageDescription</key>
	<string>This workflow needs access to administer this system in order to run.</string>
	<key>UTExportedTypeDeclarations</key>
	<array/>
	<key>UTImportedTypeDeclarations</key>
	<array/>
</dict>
</plist>
