#!/usr/bin/env node

/**
 * Import TempStick CSV data into Supabase temperature_readings table
 *
 * Usage: node import-tempstick-csv.mjs <csv_file_path>
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import { parse } from 'csv-parse/sync';

// CSV file path from command line argument
const csvFilePath = process.argv[2] || '/Users/<USER>/Documents/01_Pacific_Cloud_Seafoods/_05 Fresh Fix Migration/FF Seafood Division/Seafood_Business/Compliance/Monitoring/2550380--2025-10-02--24_hours.csv';

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'http://127.0.0.1:54321';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

// Create Supabase client with service role key (bypasses RLS)
const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Parse TempStick CSV file
 * Expected format:
 * - Line 1: Sensor info header (Sensor: Name (ID: sensor_id))
 * - Line 2: Empty
 * - Line 3: Column headers (Timestamp,Temperature,Humidity,...)
 * - Line 4+: Data rows
 */
async function importTempStickCSV(filePath) {
  console.log(`📂 Reading CSV file: ${filePath}`);

  // Read the file
  const fileContent = fs.readFileSync(filePath, 'utf-8');
  const lines = fileContent.split('\n');

  // Extract sensor ID from first line
  // Format: "Sensor: Downstairs Walk in Freezer (ID: 2550380),..."
  const headerLine = lines[0];
  const sensorIdMatch = headerLine.match(/ID:\s*(\d+)/);

  if (!sensorIdMatch) {
    throw new Error('Could not extract sensor ID from CSV header');
  }

  const externalSensorId = sensorIdMatch[1];
  console.log(`🔍 Found sensor ID: ${externalSensorId}`);

  // Find the sensor in the database
  const { data: sensors, error: sensorError } = await supabase
    .from('sensors')
    .select('id, sensor_id, name, user_id')
    .eq('sensor_id', externalSensorId)
    .limit(1);

  if (sensorError) {
    throw new Error(`Failed to query sensors: ${sensorError.message}`);
  }

  if (!sensors || sensors.length === 0) {
    throw new Error(`Sensor with ID ${externalSensorId} not found in database`);
  }

  const sensor = sensors[0];
  console.log(`✅ Found sensor: ${sensor.name} (Internal ID: ${sensor.id})`);

  // Parse CSV data (skip first 3 lines: header, empty, column names)
  const csvData = lines.slice(3).join('\n');

  const records = parse(csvData, {
    columns: ['timestamp', 'temperature', 'humidity', 'heat_index', 'dew_point', 'battery', 'rssi', 'seconds_to_connect', 'extra'],
    skip_empty_lines: true,
    trim: true,
    cast: true,
    cast_date: false, // We'll handle dates manually
    relax_column_count: true, // Allow variable column counts due to trailing commas
  });

  console.log(`📊 Parsed ${records.length} temperature readings from CSV`);

  // Transform records to match temperature_readings schema
  const readings = records.map(record => ({
    user_id: sensor.user_id, // Required user_id from sensor
    sensor_id: sensor.id, // Internal UUID, not external sensor_id
    temp_celsius: parseFloat(record.temperature),
    temp_fahrenheit: (parseFloat(record.temperature) * 9 / 5) + 32, // Convert to Fahrenheit
    humidity: parseFloat(record.humidity),
    recorded_at: new Date(record.timestamp).toISOString(),
    data_source: 'csv_import',
    sync_status: 'imported',
    metadata: {
      heat_index: parseFloat(record.heat_index),
      dew_point: parseFloat(record.dew_point),
      battery: parseInt(record.battery),
      rssi: parseInt(record.rssi),
      seconds_to_connect: parseInt(record.seconds_to_connect),
      imported_from: filePath,
      imported_at: new Date().toISOString(),
    },
  })).filter(reading => {
    // Filter out invalid readings
    return (
      !isNaN(reading.temp_celsius) &&
      !isNaN(reading.humidity) &&
      reading.recorded_at !== 'Invalid Date'
    );
  });

  console.log(`✨ Prepared ${readings.length} valid readings for import`);

  if (readings.length === 0) {
    console.warn('⚠️  No valid readings to import');
    return { imported: 0, errors: 0 };
  }

  // Insert readings into database (batch insert)
  console.log('💾 Inserting readings into database...');

  const { data, error } = await supabase
    .from('temperature_readings')
    .insert(readings)
    .select('id, recorded_at, temp_celsius');

  if (error) {
    console.error('❌ Error inserting readings:', error);
    throw new Error(`Failed to insert readings: ${error.message}`);
  }

  console.log(`✅ Successfully imported ${data?.length || readings.length} temperature readings`);

  // Show sample of imported data
  if (data && data.length > 0) {
    console.log('\n📋 Sample imported readings:');
    data.slice(0, 3).forEach((reading, idx) => {
      console.log(`   ${idx + 1}. ${reading.recorded_at}: ${reading.temp_celsius}°C`);
    });

    if (data.length > 3) {
      console.log(`   ... and ${data.length - 3} more`);
    }
  }

  return { imported: data?.length || readings.length, errors: 0 };
}

// Main execution
(async () => {
  try {
    console.log('🚀 TempStick CSV Import Tool\n');

    if (!fs.existsSync(csvFilePath)) {
      throw new Error(`CSV file not found: ${csvFilePath}`);
    }

    const result = await importTempStickCSV(csvFilePath);

    console.log(`\n🎉 Import complete!`);
    console.log(`   Imported: ${result.imported} readings`);
    console.log(`   Errors: ${result.errors}`);

    process.exit(0);
  } catch (error) {
    console.error('\n❌ Import failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
})();
