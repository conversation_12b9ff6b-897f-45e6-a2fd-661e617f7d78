#!/usr/bin/env node

/**
 * Test script to verify product click navigation query works
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testProductClick() {
  console.log('🧪 Testing Product Click Query\n');

  // Test the exact query used in handleProductClick
  const productName = 'Atlantic Cod';

  console.log(`Testing query for: "${productName}"`);
  console.log('Query: supabase.from("Products").select("*").eq("name", productName).single()\n');

  const { data, error } = await supabase
    .from('Products')
    .select('*')
    .eq('name', productName)
    .single();

  if (error) {
    console.error('❌ Error fetching product:', error);
    return;
  }

  if (data) {
    console.log('✅ Product fetched successfully!');
    console.log('\nProduct Details:');
    console.log('  ID:', data.id);
    console.log('  Name:', data.name);
    console.log('  Category:', data.category || 'N/A');
    console.log('  Sub-category:', data.sub_category || 'N/A');
    console.log('  Stock:', data.stock || 0);
    console.log('  Price:', data.price ? `$${data.price}` : 'N/A');
    console.log('  Supplier:', data.supplier || 'N/A');
    console.log('  Images:', data.images?.length || 0, 'image(s)');
    console.log('\n✅ Query works correctly!');
    console.log('   The fixed query successfully retrieves product details from the "Products" table.');
  } else {
    console.log('⚠️  No product found with that name');
  }
}

testProductClick().catch(console.error);
