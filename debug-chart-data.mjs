#!/usr/bin/env node

/**
 * Debug script to understand chart data structure
 */

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'http://127.0.0.1:54321';
const SERVICE_ROLE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;
const USER_ID = '8ef6a32c-e34c-46cc-bbb8-2e534fa64dcc';

if (!SERVICE_ROLE_KEY) {
  console.error('❌ SERVICE_ROLE_KEY required');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SERVICE_ROLE_KEY);

async function debugChartData() {
  // Get sensors
  const { data: sensors } = await supabase
    .from('sensors')
    .select('id, sensor_id, name')
    .eq('user_id', USER_ID)
    .eq('is_active', true)
    .ilike('name', '%freezer%');

  console.log('\n📡 Sensors:');
  sensors.forEach(s => {
    console.log(`  ${s.name}`);
    console.log(`    Internal ID: ${s.id}`);
    console.log(`    External ID: ${s.sensor_id}`);
  });

  // Get recent readings
  const { data: readings } = await supabase
    .from('temperature_readings')
    .select('sensor_id, temp_fahrenheit, recorded_at')
    .eq('user_id', USER_ID)
    .order('recorded_at', { ascending: false })
    .limit(20);

  console.log(`\n🌡️  Recent Readings (${readings.length} total):`);
  const sensorMap = new Map(sensors.map(s => [s.id, s]));

  readings.forEach(r => {
    const sensor = sensorMap.get(r.sensor_id);
    console.log(`  ${new Date(r.recorded_at).toLocaleTimeString()}`);
    console.log(`    Sensor ID (FK): ${r.sensor_id}`);
    console.log(`    Sensor Name: ${sensor?.name || 'UNKNOWN'}`);
    console.log(`    Temp: ${r.temp_fahrenheit}°F`);
  });

  // Simulate chart data structure
  console.log('\n📊 Chart Data Structure:');
  const dataMap = new Map();

  readings.forEach(reading => {
    const timestamp = new Date(reading.recorded_at).getTime();
    const key = timestamp.toString();

    if (!dataMap.has(key)) {
      dataMap.set(key, {
        timestamp,
        time: new Date(reading.recorded_at).toLocaleTimeString(),
      });
    }

    const sensor = sensorMap.get(reading.sensor_id);
    const entry = dataMap.get(key);
    // Use internal sensor ID as the key
    entry[`temp_${reading.sensor_id}`] = reading.temp_fahrenheit;
    entry[`sensor_name`] = sensor?.name;
  });

  const chartData = Array.from(dataMap.values());
  console.log(`  ${chartData.length} data points`);
  console.log('\n  Sample data point:');
  console.log(JSON.stringify(chartData[0], null, 2));

  console.log('\n  Keys in data points:');
  const allKeys = new Set();
  chartData.forEach(point => {
    Object.keys(point).forEach(k => allKeys.add(k));
  });
  console.log(`  ${Array.from(allKeys).join(', ')}`);
}

debugChartData();
