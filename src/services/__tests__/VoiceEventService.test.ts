import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import type { SupabaseClient } from '@supabase/supabase-js';
import { VoiceEventService, __testing__ } from '../../modules/voice-event-storage/VoiceEventService';
import { supabase } from '../../lib/supabase';
import { VoiceEventData, VoiceEventFilters } from '../../types/schema';

// Mock the supabase client
vi.mock('../../lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          order: vi.fn(() => ({
            gte: vi.fn(),
            lte: vi.fn(),
            in: vi.fn(),
            or: vi.fn(),
            lt: vi.fn(),
            single: vi.fn(),
          })),
        })),
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(),
          })),
        })),
      })),
      delete: vi.fn(() => ({
        eq: vi.fn(),
      })),
    })),
  },
}));

describe('VoiceEventService', () => {
  let service: VoiceEventService;
  let mockSupabase: any;
  let findProductSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    service = new VoiceEventService();
    mockSupabase = supabase as any;
    vi.clearAllMocks();
    __testing__.resetServiceRoleClient();

    findProductSpy = vi
      .spyOn(VoiceEventService.prototype as unknown as { findOrCreateProduct: () => Promise<string> }, 'findOrCreateProduct')
      .mockResolvedValue('mock-product-id');
  });

  afterEach(() => {
    findProductSpy.mockRestore();
  });

  describe('createVoiceEvent', () => {
    it('should create a voice event successfully', async () => {
      const mockEventData: VoiceEventData & {
        voice_confidence_score: number;
        voice_confidence_breakdown: any;
        raw_transcript: string;
      } = {
        event_type: 'receiving',
        product_name: 'Salmon Fillet',
        quantity: 25,
        unit: 'lbs',
        vendor_name: 'Pacific Seafoods',
        voice_confidence_score: 0.85,
        voice_confidence_breakdown: {
          product_match: 0.9,
          quantity_extraction: 0.8,
          vendor_match: 0.85,
          overall: 0.85,
        },
        raw_transcript: 'Received 25 pounds of salmon fillet from Pacific Seafoods',
      };

      const mockDbResponse = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        event_type: 'receiving',
        name: 'Salmon Fillet',
        quantity: 25,
        unit: 'lbs',
        vendor_name: 'Pacific Seafoods',
        voice_confidence_score: 0.85,
        voice_confidence_breakdown: mockEventData.voice_confidence_breakdown,
        raw_transcript: 'Received 25 pounds of salmon fillet from Pacific Seafoods',
        created_by_voice: true,
        created_at: '2025-08-15T10:00:00Z',
        updated_at: '2025-08-15T10:00:00Z',
      };

      // Mock the chain of method calls
      const mockSingle = vi.fn().mockResolvedValue({ data: mockDbResponse, error: null });
      const mockSelect = vi.fn().mockReturnValue({ single: mockSingle });
      const mockInsert = vi.fn().mockReturnValue({ select: mockSelect });
      const mockFrom = vi.fn().mockReturnValue({ insert: mockInsert });

      mockSupabase.from = mockFrom;

      const result = await service.createVoiceEvent(mockEventData);

      expect(mockFrom).toHaveBeenCalledWith('inventory_events');
      expect(mockInsert).toHaveBeenCalledWith(
        expect.objectContaining({
          event_type: 'receiving',
          name: 'Salmon Fillet',
          quantity: 25,
          unit: 'lbs',
          voice_confidence_score: 0.85,
          created_by_voice: true,
        })
      );
      expect(result.id).toBe('123e4567-e89b-12d3-a456-426614174000');
      expect(result.product_name).toBe('Salmon Fillet');
      expect(result.voice_confidence_score).toBe(0.85);
    });

    it('should handle database errors when creating voice event', async () => {
      const mockEventData: VoiceEventData & {
        voice_confidence_score: number;
        voice_confidence_breakdown: any;
        raw_transcript: string;
      } = {
        event_type: 'receiving',
        product_name: 'Salmon Fillet',
        quantity: 25,
        unit: 'lbs',
        voice_confidence_score: 0.85,
        voice_confidence_breakdown: {
          product_match: 0.9,
          quantity_extraction: 0.8,
          vendor_match: 0.85,
          overall: 0.85,
        },
        raw_transcript: 'Test transcript',
      };

      const mockError = { message: 'Database connection failed' };
      const mockSingle = vi.fn().mockResolvedValue({ data: null, error: mockError });
      const mockSelect = vi.fn().mockReturnValue({ single: mockSingle });
      const mockInsert = vi.fn().mockReturnValue({ select: mockSelect });
      const mockFrom = vi.fn().mockReturnValue({ insert: mockInsert });

      mockSupabase.from = mockFrom;

      await expect(service.createVoiceEvent(mockEventData)).rejects.toThrow(
        'Failed to create voice event: Database connection failed'
      );
    });

    it('should fallback to service role when RLS blocks inserts', async () => {
      const originalWindow = (globalThis as Record<string, unknown>).window;
      // @ts-expect-error - emulate server environment for test
      delete (globalThis as Record<string, unknown>).window;

      const mockEventData: VoiceEventData & {
        voice_confidence_score: number;
        voice_confidence_breakdown: any;
        raw_transcript: string;
      } = {
        event_type: 'receiving',
        product_name: 'Dungeness Crab',
        quantity: 12,
        unit: 'lbs',
        voice_confidence_score: 0.9,
        voice_confidence_breakdown: {
          product_match: 0.93,
          quantity_extraction: 0.88,
          vendor_match: 0.9,
          overall: 0.9,
        },
        raw_transcript: 'Received 12 pounds of Dungeness crab',
      };

      const rlsError = { code: '42501', message: 'violates row-level security policy' };
      const primarySingle = vi.fn().mockResolvedValue({ data: null, error: rlsError });
      const primarySelect = vi.fn().mockReturnValue({ single: primarySingle });
      const primaryInsert = vi.fn().mockReturnValue({ select: primarySelect });
      const primaryFrom = vi.fn().mockReturnValue({ insert: primaryInsert });

      mockSupabase.from = primaryFrom;

      const fallbackResponse = {
        id: 'service-role-event',
        event_type: 'receiving',
        name: 'Dungeness Crab',
        quantity: 12,
        unit: 'lbs',
        created_by_voice: true,
        metadata: {
          fallback_source: 'voice_service_role_fallback',
        },
      };

      const fallbackSingle = vi.fn().mockResolvedValue({ data: fallbackResponse, error: null });
      const fallbackSelect = vi.fn().mockReturnValue({ single: fallbackSingle });
      const fallbackInsert = vi.fn().mockReturnValue({ select: fallbackSelect });
      const fallbackFrom = vi.fn().mockReturnValue({ insert: fallbackInsert });

      __testing__.setServiceRoleClient({
        from: fallbackFrom,
      } as unknown as SupabaseClient);

      try {
        const result = await service.createVoiceEvent(mockEventData);

        expect(primaryInsert).toHaveBeenCalled();
        expect(fallbackFrom).toHaveBeenCalledWith('inventory_events');
        expect(result.id).toBe('service-role-event');
        expect(result.product_name).toBe('Dungeness Crab');
      } finally {
        if (originalWindow !== undefined) {
          (globalThis as Record<string, unknown>).window = originalWindow;
        } else {
          // @ts-expect-error restore undefined window
          delete (globalThis as Record<string, unknown>).window;
        }
      }
    });
  });

  describe('getVoiceEvents', () => {
    it('should fetch voice events without filters', async () => {
      const mockDbEvents = [
        {
          id: '123e4567-e89b-12d3-a456-426614174000',
          event_type: 'receiving',
          name: 'Salmon Fillet',
          quantity: 25,
          unit: 'lbs',
          voice_confidence_score: 0.85,
          voice_confidence_breakdown: {
            overall: 0.85,
            product_match: 0.9,
            quantity_extraction: 0.8,
            vendor_match: 0.85,
          },
          raw_transcript: 'Test transcript',
          created_by_voice: true,
          created_at: '2025-08-15T10:00:00Z',
        },
      ];

      // Mock the chain of method calls for fetching
      const mockOrder = vi.fn().mockResolvedValue({ data: mockDbEvents, error: null });
      const mockEq = vi.fn().mockReturnValue({ order: mockOrder });
      const mockSelect = vi.fn().mockReturnValue({ eq: mockEq });
      const mockFrom = vi.fn().mockReturnValue({ select: mockSelect });

      mockSupabase.from = mockFrom;

      const result = await service.getVoiceEvents();

      expect(mockFrom).toHaveBeenCalledWith('inventory_events');
      expect(mockEq).toHaveBeenCalledWith('created_by_voice', true);
      expect(result).toHaveLength(1);
      expect(result[0].product_name).toBe('Salmon Fillet');
      expect(result[0].voice_confidence_score).toBe(0.85);
    });

    it('should apply filters when fetching voice events', async () => {
      const filters: VoiceEventFilters = {
        dateRange: {
          start: '2025-08-01T00:00:00Z',
          end: '2025-08-31T23:59:59Z',
        },
        eventType: ['receiving'],
        confidenceThreshold: 0.7,
        searchQuery: 'salmon',
      };

      const mockDbEvents = [
        {
          id: '123e4567-e89b-12d3-a456-426614174000',
          event_type: 'receiving',
          name: 'Salmon Fillet',
          quantity: 25,
          unit: 'lbs',
          voice_confidence_score: 0.85,
          voice_confidence_breakdown: {
            overall: 0.85,
            product_match: 0.9,
            quantity_extraction: 0.8,
            vendor_match: 0.85,
          },
          raw_transcript: 'Test transcript',
          created_by_voice: true,
          created_at: '2025-08-15T10:00:00Z',
        },
      ];

      // Mock the complex chain for filtered query
      const mockOr = vi.fn().mockResolvedValue({ data: mockDbEvents, error: null });
      const mockGte2 = vi.fn().mockReturnValue({ or: mockOr });
      const mockIn = vi.fn().mockReturnValue({ gte: mockGte2 });
      const mockLte = vi.fn().mockReturnValue({ in: mockIn });
      const mockGte1 = vi.fn().mockReturnValue({ lte: mockLte });
      const mockOrder = vi.fn().mockReturnValue({ gte: mockGte1 });
      const mockEq = vi.fn().mockReturnValue({ order: mockOrder });
      const mockSelect = vi.fn().mockReturnValue({ eq: mockEq });
      const mockFrom = vi.fn().mockReturnValue({ select: mockSelect });

      mockSupabase.from = mockFrom;

      const result = await service.getVoiceEvents(filters);

      expect(mockFrom).toHaveBeenCalledWith('inventory_events');
      expect(result).toHaveLength(1);
      expect(result[0].product_name).toBe('Salmon Fillet');
    });
  });

  describe('updateVoiceEvent', () => {
    it('should update a voice event and create audit trail', async () => {
      const eventId = '123e4567-e89b-12d3-a456-426614174000';
      const updates = {
        product_name: 'Updated Salmon Fillet',
        quantity: 30,
        notes: 'Updated notes',
      };

      const mockCurrentEvent = {
        id: eventId,
        name: 'Salmon Fillet',
        quantity: 25,
        notes: 'Original notes',
        voice_confidence_score: 0.85,
      };

      const mockUpdatedEvent = {
        ...mockCurrentEvent,
        name: 'Updated Salmon Fillet',
        quantity: 30,
        notes: 'Updated notes',
      };

      // Mock fetch current event
      const mockSingleFetch = vi.fn().mockResolvedValue({ data: mockCurrentEvent, error: null });
      const mockEqFetch = vi.fn().mockReturnValue({ single: mockSingleFetch });
      const mockSelectFetch = vi.fn().mockReturnValue({ eq: mockEqFetch });

      // Mock update event
      const mockSingleUpdate = vi.fn().mockResolvedValue({ data: mockUpdatedEvent, error: null });
      const mockSelectUpdate = vi.fn().mockReturnValue({ single: mockSingleUpdate });
      const mockEqUpdate = vi.fn().mockReturnValue({ select: mockSelectUpdate });
      const mockUpdate = vi.fn().mockReturnValue({ eq: mockEqUpdate });

      // Mock audit trail insert
      const mockInsert = vi.fn().mockResolvedValue({ error: null });

      const mockFrom = vi
        .fn()
        .mockReturnValueOnce({ select: mockSelectFetch }) // First call for fetch
        .mockReturnValueOnce({ update: mockUpdate }) // Second call for update
        .mockReturnValueOnce({ insert: mockInsert }); // Third call for audit trail

      mockSupabase.from = mockFrom;

      const result = await service.updateVoiceEvent(
        eventId,
        updates,
        'user123',
        'Manual correction'
      );

      expect(mockFrom).toHaveBeenCalledWith('inventory_events');
      expect(result.product_name).toBe('Updated Salmon Fillet');
      expect(result.quantity).toBe(30);
    });
  });

  describe('getEventsForQualityReview', () => {
    it('should fetch events with low confidence scores', async () => {
      const mockLowConfidenceEvents = [
        {
          id: '123e4567-e89b-12d3-a456-426614174000',
          event_type: 'receiving',
          name: 'Unclear Product',
          quantity: 10,
          unit: 'lbs',
          voice_confidence_score: 0.6,
          voice_confidence_breakdown: {
            overall: 0.6,
            product_match: 0.5,
            quantity_extraction: 0.7,
            vendor_match: 0.6,
          },
          raw_transcript: 'Unclear transcript',
          created_by_voice: true,
          created_at: '2025-08-15T10:00:00Z',
        },
      ];

      const mockOrder = vi.fn().mockResolvedValue({ data: mockLowConfidenceEvents, error: null });
      const mockLt = vi.fn().mockReturnValue({ order: mockOrder });
      const mockEq = vi.fn().mockReturnValue({ lt: mockLt });
      const mockSelect = vi.fn().mockReturnValue({ eq: mockEq });
      const mockFrom = vi.fn().mockReturnValue({ select: mockSelect });

      mockSupabase.from = mockFrom;

      const result = await service.getEventsForQualityReview(0.7);

      expect(mockFrom).toHaveBeenCalledWith('inventory_events');
      expect(mockLt).toHaveBeenCalledWith('voice_confidence_score', 0.7);
      expect(result).toHaveLength(1);
      expect(result[0].voice_confidence_score).toBe(0.6);
    });
  });

  describe('getVoiceEventStatistics', () => {
    it('should calculate statistics correctly', async () => {
      const mockEvents = [
        { voice_confidence_score: 0.95 }, // High
        { voice_confidence_score: 0.85 }, // Medium
        { voice_confidence_score: 0.75 }, // Medium
        { voice_confidence_score: 0.65 }, // Low
        { voice_confidence_score: 0.55 }, // Low
      ];

      const mockEq = vi.fn().mockResolvedValue({ data: mockEvents, error: null });
      const mockSelect = vi.fn().mockReturnValue({ eq: mockEq });
      const mockFrom = vi.fn().mockReturnValue({ select: mockSelect });

      mockSupabase.from = mockFrom;

      const result = await service.getVoiceEventStatistics();

      expect(result.totalEvents).toBe(5);
      expect(result.highConfidenceEvents).toBe(1);
      expect(result.mediumConfidenceEvents).toBe(2);
      expect(result.lowConfidenceEvents).toBe(2);
      expect(result.eventsNeedingReview).toBe(2);
      expect(result.averageConfidence).toBe(0.75); // (0.95 + 0.85 + 0.75 + 0.65 + 0.55) / 5
    });

    it('should handle empty results', async () => {
      const mockEq = vi.fn().mockResolvedValue({ data: [], error: null });
      const mockSelect = vi.fn().mockReturnValue({ eq: mockEq });
      const mockFrom = vi.fn().mockReturnValue({ select: mockSelect });

      mockSupabase.from = mockFrom;

      const result = await service.getVoiceEventStatistics();

      expect(result.totalEvents).toBe(0);
      expect(result.highConfidenceEvents).toBe(0);
      expect(result.mediumConfidenceEvents).toBe(0);
      expect(result.lowConfidenceEvents).toBe(0);
      expect(result.eventsNeedingReview).toBe(0);
      expect(result.averageConfidence).toBe(0);
    });
  });

  describe('deleteVoiceEvent', () => {
    it('should delete a voice event successfully', async () => {
      const eventId = '123e4567-e89b-12d3-a456-426614174000';

      const mockEq = vi.fn().mockResolvedValue({ error: null });
      const mockDelete = vi.fn().mockReturnValue({ eq: mockEq });
      const mockFrom = vi.fn().mockReturnValue({ delete: mockDelete });

      mockSupabase.from = mockFrom;

      await expect(service.deleteVoiceEvent(eventId)).resolves.not.toThrow();
      expect(mockFrom).toHaveBeenCalledWith('inventory_events');
      expect(mockEq).toHaveBeenCalledWith('id', eventId);
    });

    it('should handle delete errors', async () => {
      const eventId = '123e4567-e89b-12d3-a456-426614174000';
      const mockError = { message: 'Delete failed' };

      const mockEq = vi.fn().mockResolvedValue({ error: mockError });
      const mockDelete = vi.fn().mockReturnValue({ eq: mockEq });
      const mockFrom = vi.fn().mockReturnValue({ delete: mockDelete });

      mockSupabase.from = mockFrom;

      await expect(service.deleteVoiceEvent(eventId)).rejects.toThrow(
        'Failed to delete voice event: Delete failed'
      );
    });
  });
});
