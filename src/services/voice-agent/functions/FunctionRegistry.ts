/**
 * Function Registry
 * 
 * Central registry for all voice agent functions that can be called by OpenAI.
 */

import { logger } from '../utils/logger';
import { AuthenticationService } from '../AuthenticationService';
import { ValidationError, AuthorizationError } from '../middleware/errorHandler';
import { performanceOptimizer } from '../PerformanceOptimizer';
import { errorRecoveryManager } from '../ErrorRecoveryManager';
import { inventoryFunctions } from './InventoryFunctions';
import { OrderFunctions } from './OrderFunctions';
import { TemperatureFunctions } from './TemperatureFunctions';
import { LotTemperatureFunctions } from './LotTemperatureFunctions';
import { systemFunctions } from '../../../modules/conversation-management/functions/system';
import type {
  ActionResult,
  DatabaseFunction,
  FunctionContext as ContractsFunctionContext,
  FunctionParameter as ContractsFunctionParameter,
} from '../types/contracts';

type ParameterType = 'string' | 'number' | 'boolean' | 'array' | 'object';

interface ParameterSchemaProperty {
  type: ParameterType;
  description: string;
}

interface ParameterSchema {
  type: 'object';
  properties: Record<string, ParameterSchemaProperty>;
  required?: string[];
}

type FunctionResult = ActionResult;
type FunctionContext = ContractsFunctionContext;

interface FunctionDefinition {
  name: string;
  description: string;
  parameters: ParameterSchema;
  permissions: string[];
  rateLimit?: {
    requests: number;
    windowMs: number;
  };
  handler: (args: Record<string, unknown>, context: FunctionContext) => Promise<FunctionResult>;
}

export interface FunctionRegistryOptions {
  userId: string;
  userRoles: string[];
}

export class FunctionRegistry {
  private functions: Map<string, FunctionDefinition> = new Map();
  private authService: AuthenticationService;
  private userId: string;
  private userRoles: string[];
  private logger: typeof logger;

  constructor(options: FunctionRegistryOptions) {
    this.userId = options.userId;
    this.userRoles = options.userRoles;
    this.authService = new AuthenticationService();
    
    this.logger = logger.child({
      userId: this.userId,
      component: 'FunctionRegistry'
    });

    // Register all available functions
    this.registerAllFunctions();
  }

  private registerAllFunctions(): void {
    // Import and register function modules
    this.registerInventoryFunctions();
    this.registerOrderFunctions();
    this.registerTemperatureFunctions();
    this.registerComplianceFunctions();
    this.registerSystemFunctions();

    this.logger.info(`Registered ${this.functions.size} functions`);
  }

  private registerInventoryFunctions(): void {
    // Use the imported inventory functions implementation

    // Get current inventory levels
    this.registerFunction({
      name: 'get_inventory_levels',
      description: 'Get current inventory levels for products with optional filtering',
      parameters: {
        type: 'object',
        properties: {
          product_name: {
            type: 'string',
            description: 'Optional product name to filter by (partial match supported)'
          },
          category: {
            type: 'string',
            description: 'Optional category to filter by (Finfish, Shellfish, Crustaceans, Specialty)'
          },
          location: {
            type: 'string',
            description: 'Optional storage location to filter by'
          },
          min_quantity: {
            type: 'number',
            description: 'Optional minimum quantity threshold'
          }
        }
      },
      permissions: ['inventory:read'],
      handler: inventoryFunctions.get_inventory_levels.handler
    });

    // Search products
    this.registerFunction({
      name: 'search_products',
      description: 'Search for products by name, category, or other attributes',
      parameters: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: 'Search query for product name or description'
          },
          category: {
            type: 'string',
            description: 'Filter by product category'
          },
          limit: {
            type: 'number',
            description: 'Maximum number of results to return (default: 10)'
          }
        },
        required: ['query']
      },
      permissions: ['inventory:read'],
      handler: inventoryFunctions.search_products.handler
    });

    // Create inventory event
    this.registerFunction({
      name: 'create_inventory_event',
      description: 'Create a new inventory event (receiving, sale, disposal, etc.)',
      parameters: {
        type: 'object',
        properties: {
          event_type: {
            type: 'string',
            description: 'Type of event (received, sale, disposal, physical_count)'
          },
          product_name: {
            type: 'string',
            description: 'Name of the product'
          },
          quantity: {
            type: 'number',
            description: 'Quantity involved in the event'
          },
          unit: {
            type: 'string',
            description: 'Unit of measurement (lbs, kg, cases, units)'
          },
          unit_price: {
            type: 'number',
            description: 'Price per unit (optional)'
          },
          supplier: {
            type: 'string',
            description: 'Supplier name (for receiving events)'
          },
          notes: {
            type: 'string',
            description: 'Additional notes about the event'
          }
        },
        required: ['event_type', 'product_name', 'quantity', 'unit']
      },
      permissions: ['inventory:write'],
      handler: inventoryFunctions.create_inventory_event.handler
    });
  }

  private registerOrderFunctions(): void {
    // Import the actual order functions implementation
    const orderFunctions = new OrderFunctions();

    // Get recent orders
    this.registerFunction({
      name: 'get_recent_orders',
      description: 'Get recent orders with optional filtering by customer, status, or date range',
      parameters: {
        type: 'object',
        properties: {
          customer_name: {
            type: 'string',
            description: 'Optional customer name to filter by'
          },
          status: {
            type: 'string',
            description: 'Optional order status to filter by'
          },
          days: {
            type: 'number',
            description: 'Number of days to look back (default: 7)'
          },
          limit: {
            type: 'number',
            description: 'Maximum number of orders to return (default: 10)'
          }
        }
      },
      permissions: ['orders:read'],
      handler: orderFunctions.getRecentOrders.bind(orderFunctions)
    });

    // Get order status
    this.registerFunction({
      name: 'get_order_status',
      description: 'Get the status of a specific order by order number or customer name',
      parameters: {
        type: 'object',
        properties: {
          order_number: {
            type: 'string',
            description: 'Specific order number to look up'
          },
          customer_name: {
            type: 'string',
            description: 'Customer name to find orders for'
          }
        }
      },
      permissions: ['orders:read'],
      handler: orderFunctions.getOrderStatus.bind(orderFunctions)
    });
  }

  private registerTemperatureFunctions(): void {
    // Import the actual temperature functions implementation
    const temperatureFunctions = new TemperatureFunctions();
    const lotTemperatureFunctions = new LotTemperatureFunctions();

    // Get temperature readings
    this.registerFunction({
      name: 'get_temperature_readings',
      description: 'Get current and recent temperature readings from sensors',
      parameters: {
        type: 'object',
        properties: {
          sensor_id: {
            type: 'string',
            description: 'Optional specific sensor ID to query'
          },
          location: {
            type: 'string',
            description: 'Optional location name to filter sensors'
          },
          hours: {
            type: 'number',
            description: 'Number of hours to look back (default: 24)'
          },
          alerts_only: {
            type: 'boolean',
            description: 'Only return readings with temperature alerts'
          }
        }
      },
      permissions: ['temperature:read'],
      handler: temperatureFunctions.getTemperatureReadings.bind(temperatureFunctions)
    });

    // Freezer status summary
    this.registerFunction({
      name: 'get_freezer_status',
      description: 'Summarize freezer temperatures, alerts, and lot impacts for a storage area',
      parameters: {
        type: 'object',
        properties: {
          storage_area: {
            type: 'string',
            description: 'Storage area name to focus on (e.g. "Downstairs Walk in Freezer")'
          },
          sensor_id: {
            type: 'string',
            description: 'Optional specific sensor ID to include in the summary'
          },
          hours: {
            type: 'number',
            description: 'Number of hours of history to include (default: 24)'
          },
          include_violations: {
            type: 'boolean',
            description: 'Whether to include recent temperature violations in the response'
          }
        }
      },
      permissions: ['temperature:read'],
      handler: temperatureFunctions.getFreezerStatus.bind(temperatureFunctions)
    });

    // Storage area temperature with lot context
    this.registerFunction({
      name: 'get_storage_area_temperature',
      description: 'Retrieve current storage area temperatures with lot context and aging alerts',
      parameters: {
        type: 'object',
        properties: {
          storage_area: {
            type: 'string',
            description: 'Optional storage area name filter (e.g., downstairs freezer)'
          },
          sensor_location: {
            type: 'string',
            description: 'Optional sensor location label filter'
          },
          unit: {
            type: 'string',
            description: 'Preferred temperature unit (F or C)'
          },
          include_aging: {
            type: 'boolean',
            description: 'Include lot aging alerts in the response'
          }
        }
      },
      permissions: ['temperature:read'],
      handler: lotTemperatureFunctions.getStorageAreaTemperature.bind(lotTemperatureFunctions)
    });

    // Lot aging alert summaries
    this.registerFunction({
      name: 'get_lot_aging_alerts',
      description: 'Identify lots approaching aging thresholds with temperature context',
      parameters: {
        type: 'object',
        properties: {
          threshold_days: {
            type: 'number',
            description: 'Aging threshold in days (default 150)'
          },
          storage_area: {
            type: 'string',
            description: 'Optional storage area filter'
          },
          include_temperature: {
            type: 'boolean',
            description: 'Include the latest temperature reading in alert context'
          },
          lot_codes: {
            type: 'array',
            description: 'Optional list of lot codes to filter by'
          }
        }
      },
      permissions: ['temperature:read'],
      handler: lotTemperatureFunctions.getLotAgingAlerts.bind(lotTemperatureFunctions)
    });

    // Temperature violations impacting lots
    this.registerFunction({
      name: 'get_temperature_violations_for_lots',
      description: 'Find temperature violations impacting specific lots or batches',
      parameters: {
        type: 'object',
        properties: {
          lot_codes: {
            type: 'array',
            description: 'Lot codes to investigate for temperature violations'
          },
          batch_ids: {
            type: 'array',
            description: 'Batch IDs to include in the search'
          },
          include_storage_context: {
            type: 'boolean',
            description: 'Include storage area information in the response'
          }
        }
      },
      permissions: ['temperature:read'],
      handler: lotTemperatureFunctions.getTemperatureViolationsForLots.bind(lotTemperatureFunctions)
    });
  }

  private registerComplianceFunctions(): void {
    // Check HACCP compliance
    this.registerFunction({
      name: 'check_haccp_compliance',
      description: 'Check HACCP compliance status for specified areas or overall',
      parameters: {
        type: 'object',
        properties: {
          area: {
            type: 'string',
            description: 'Optional specific area to check (e.g., "freezer", "processing")'
          },
          check_type: {
            type: 'string',
            description: 'Type of compliance check (temperature, cleaning, documentation)'
          }
        }
      },
      permissions: ['compliance:read'],
      handler: this.handleCheckHACCPCompliance.bind(this)
    });
  }

  private registerFunction(definition: FunctionDefinition): void {
    this.functions.set(definition.name, definition);
    this.logger.debug(`Registered function: ${definition.name}`);
  }

  async getAllFunctions(): Promise<Record<string, FunctionDefinition>> {
    const result: Record<string, FunctionDefinition> = {};
    
    for (const [name, definition] of this.functions) {
      // Check if user has permission for this function
      if (this.hasPermission(definition.permissions)) {
        result[name] = definition;
      }
    }
    
    return result;
  }

  async executeFunction(name: string, args: Record<string, unknown> = {}): Promise<FunctionResult> {
    const startTime = Date.now();
    const requestId = this.generateRequestId();
    
    this.logger.info(`Executing function: ${name}`, { args, requestId });

    let context: FunctionContext | null = null;

    try {
      const definition = this.functions.get(name);
      if (!definition) {
        throw new ValidationError(`Unknown function: ${name}`);
      }

      // Check permissions
      if (!this.hasPermission(definition.permissions)) {
        throw new AuthorizationError(`Insufficient permissions for function: ${name}`);
      }

      // Validate arguments
      this.validateArguments(definition, args);

      // Create function context
      context = {
        userId: this.userId,
        userRoles: this.userRoles,
        permissions: this.getUserPermissions(),
        sessionId: 'current', // TODO: Get from session
        timestamp: new Date(),
        requestId
      };

      // Create cache key for read operations
      const cacheKey = this.generateCacheKey(name, args);
      const isReadOperation = definition.permissions.some(p => p.endsWith(':read'));

      // Use performance optimizer for caching and query optimization
      const result = await performanceOptimizer.optimizeQuery(
        cacheKey,
        () => definition.handler(args, context as FunctionContext),
        {
          enableCache: isReadOperation,
          cacheTtl: this.getCacheTtl(name),
          timeout: 15000 // 15 second timeout
        }
      );
      
      const duration = Date.now() - startTime;
      this.logger.info(`Function executed successfully: ${name}`, { 
        duration, 
        requestId,
        success: result.success 
      });

      return result;
    } catch (error: unknown) {
      const duration = Date.now() - startTime;
      const normalizedError = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Function execution failed: ${name}`, { 
        error: normalizedError, 
        duration, 
        requestId 
      });

      // Use error recovery for function execution failures
      const recoveryResult = await errorRecoveryManager.handleError(normalizedError, {
        operation: `function_${name}`,
        userId: this.userId,
        sessionId: context?.sessionId ?? 'unknown',
        timestamp: new Date(),
        attemptNumber: 1,
        originalError: normalizedError,
        metadata: { functionName: name, args, duration }
      });

      return recoveryResult ?? {
        success: false,
        message: 'Function execution failed',
        formattedForVoice: 'Sorry, I encountered an error while processing your request.',
        error: {
          code: 'FUNCTION_EXECUTION_ERROR',
          message: normalizedError.message,
          details: normalizedError.stack
        }
      };
    }
  }

  private hasPermission(requiredPermissions: string[]): boolean {
    // Admin role has all permissions
    if (this.userRoles.includes('admin')) {
      return true;
    }

    // Check if user has any of the required permissions
    // This is a simplified check - in a real implementation,
    // you'd query the user's actual permissions from the database
    const userPermissions = this.getUserPermissions();
    if (userPermissions.includes('*')) {
      return true;
    }
    return requiredPermissions.some(permission => 
      userPermissions.includes(permission)
    );
  }

  private getUserPermissions(): string[] {
    // Simplified permission mapping based on roles
    const permissions: string[] = ['voice:use'];
    
    if (this.userRoles.includes('admin')) {
      return ['*']; // All permissions
    }
    
    if (this.userRoles.includes('manager')) {
      permissions.push(
        'inventory:read', 'inventory:write',
        'orders:read', 'orders:write',
        'temperature:read',
        'compliance:read'
      );
    }
    
    if (this.userRoles.includes('user')) {
      permissions.push(
        'inventory:read',
        'orders:read',
        'temperature:read'
      );
    }
    
    return permissions;
  }

  private validateArguments(definition: FunctionDefinition, args: Record<string, unknown>): void {
    const { parameters } = definition;
    
    // Check required parameters
    if (parameters.required) {
      for (const required of parameters.required) {
        if (!(required in args) || args[required] === undefined || args[required] === null) {
          throw new ValidationError(`Missing required parameter: ${required}`);
        }
      }
    }

    // Validate parameter types and constraints
    for (const [paramName, paramDef] of Object.entries(parameters.properties)) {
      if (paramName in args) {
        this.validateParameter(paramName, args[paramName], paramDef);
      }
    }
  }

  private validateParameter(name: string, value: unknown, definition: ParameterSchemaProperty): void {
    // Type validation
    const expectedType = definition.type;

    switch (expectedType) {
      case 'number':
        if (typeof value !== 'number') {
          throw new ValidationError(`Parameter ${name} must be a number`);
        }
        break;
      case 'string':
        if (typeof value !== 'string') {
          throw new ValidationError(`Parameter ${name} must be a string`);
        }
        break;
      case 'boolean':
        if (typeof value !== 'boolean') {
          throw new ValidationError(`Parameter ${name} must be a boolean`);
        }
        break;
      case 'array':
        if (!Array.isArray(value)) {
          throw new ValidationError(`Parameter ${name} must be an array`);
        }
        break;
      case 'object':
        if (typeof value !== 'object' || value === null || Array.isArray(value)) {
          throw new ValidationError(`Parameter ${name} must be an object`);
        }
        break;
      default:
        break;
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCacheKey(functionName: string, args: Record<string, unknown>): string {
    // Create a stable cache key from function name and arguments
    const argsHash = JSON.stringify(args, Object.keys(args).sort());
    return `func_${functionName}_${Buffer.from(argsHash).toString('base64').slice(0, 16)}`;
  }

  private getCacheTtl(functionName: string): number {
    // Different TTL based on function type
    if (functionName.includes('temperature')) {
      return 30000; // 30 seconds for temperature data
    } else if (functionName.includes('inventory')) {
      return 60000; // 1 minute for inventory data
    } else if (functionName.includes('order')) {
      return 120000; // 2 minutes for order data
    } else if (functionName.includes('product') || functionName.includes('compliance')) {
      return 300000; // 5 minutes for relatively stable data
    }
    return 60000; // Default 1 minute
  }

  private registerSystemFunctions(): void {
    // Import system functions from the conversation management module
    const conversationFunctions = Object.values(systemFunctions) as DatabaseFunction[];

    conversationFunctions.forEach((conversationFunction) => {
      if (this.isDatabaseFunction(conversationFunction)) {
        const definition = this.mapDatabaseFunction(conversationFunction);
        this.registerFunction(definition);
      }
    });

    // Additional system functions
    this.registerFunction({
      name: 'get_system_status',
      description: 'Get current system status and health information',
      parameters: {
        type: 'object',
        properties: {
          include_performance: {
            type: 'boolean',
            description: 'Include performance metrics in the response'
          }
        }
      },
      permissions: ['system:read'],
      handler: this.handleGetSystemStatus.bind(this)
    });
  }

  private async handleGetSystemStatus(args: Record<string, unknown>, _context: FunctionContext): Promise<FunctionResult> {
    try {
      const includePerformanceRaw = args['include_performance'];
      const includePerformance = typeof includePerformanceRaw === 'boolean' ? includePerformanceRaw : false;
      const status: Record<string, unknown> = {
        timestamp: new Date().toISOString(),
        status: 'operational',
        services: {
          voiceAgent: 'active',
          database: 'connected',
          authentication: 'active'
        }
      };

      if (includePerformance) {
        const metrics = performanceOptimizer.getCurrentMetrics();
        const cacheStats = performanceOptimizer.getCacheStats();
        
        status.performance = {
          responseTime: metrics?.responseTime ?? 0,
          cacheHitRate: cacheStats.hitRate,
          activeConnections: metrics?.activeConnections ?? 0,
          memoryUsage: metrics?.memoryUsage ?? null
        };
      }

      return {
        success: true,
        message: 'System status retrieved',
        formattedForVoice: `System is operational. All services are running normally.`,
        data: status
      };
    } catch (error: unknown) {
      const normalizedError = error instanceof Error ? error : new Error(String(error));
      return {
        success: false,
        message: 'Failed to get system status',
        formattedForVoice: 'Sorry, I cannot retrieve the system status right now.',
        error: {
          code: 'SYSTEM_STATUS_ERROR',
          message: normalizedError.message
        }
      };
    }
  }

  private async handleCheckHACCPCompliance(args: Record<string, unknown>, _context: FunctionContext): Promise<FunctionResult> {
    try {
      // This is a simplified HACCP compliance check
      // In a real implementation, this would query actual compliance data
      const areaRaw = args['area'];
      const checkTypeRaw = args['check_type'];
      const area = typeof areaRaw === 'string' ? areaRaw : 'all';
      const checkType = typeof checkTypeRaw === 'string' ? checkTypeRaw : 'all';
      
      const complianceStatus = {
        area,
        checkType,
        status: 'compliant',
        lastCheck: new Date().toISOString(),
        issues: [],
        nextCheck: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      };

      return {
        success: true,
        message: 'HACCP compliance check completed',
        formattedForVoice: `HACCP compliance for ${area} is currently compliant with no outstanding issues.`,
        data: complianceStatus
      };
    } catch (error: unknown) {
      const normalizedError = error instanceof Error ? error : new Error(String(error));
      return {
        success: false,
        message: 'Failed to check HACCP compliance',
        formattedForVoice: 'Sorry, I cannot check HACCP compliance right now.',
        error: {
          code: 'COMPLIANCE_CHECK_ERROR',
          message: normalizedError.message
        }
      };
    }
  }

  private isDatabaseFunction(functionCandidate: unknown): functionCandidate is DatabaseFunction {
    return (
      typeof functionCandidate === 'object' &&
      functionCandidate !== null &&
      'name' in functionCandidate &&
      'description' in functionCandidate &&
      'permissions' in functionCandidate &&
      'handler' in functionCandidate &&
      'parameters' in functionCandidate &&
      Array.isArray((functionCandidate as { parameters: unknown }).parameters)
    );
  }

  private mapDatabaseFunction(fn: DatabaseFunction): FunctionDefinition {
    const properties: Record<string, ParameterSchemaProperty> = {};
    const required: string[] = [];

    fn.parameters.forEach((parameter: ContractsFunctionParameter) => {
      properties[parameter.name] = {
        type: parameter.type,
        description: parameter.description,
      };

      if (parameter.required) {
        required.push(parameter.name);
      }
    });

    const parameters: ParameterSchema = {
      type: 'object',
      properties,
    };

    if (required.length > 0) {
      parameters.required = required;
    }

    return {
      name: fn.name,
      description: fn.description,
      parameters,
      permissions: fn.permissions,
      rateLimit: fn.rateLimit
        ? {
            requests: fn.rateLimit.requests,
            windowMs: fn.rateLimit.windowMs,
          }
        : undefined,
      handler: fn.handler,
    };
  }
}
