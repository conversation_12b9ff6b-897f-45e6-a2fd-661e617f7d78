/**
 * Order Functions for Voice Agent
 *
 * Handles order queries and management operations.
 */

import { createClient } from '@supabase/supabase-js';
import { config } from '../config';
import { logger } from '../utils/logger';
import { FunctionContext, FunctionResult } from './FunctionRegistry';

interface RecentOrdersArgs {
  customer_name?: string;
  status?: string;
  days?: number;
  limit?: number;
}

interface OrderStatusArgs {
  order_number?: string;
  customer_name?: string;
}

interface OrderProduct {
  name: string;
  category?: string;
}

interface OrderRecord {
  id: string;
  order_number: string;
  fulfillment_date: string;
  fulfillment_status: string;
  quantity?: number;
  unit_price?: number;
  total_price?: number;
  customer_name?: string;
  notes?: string;
  Products: OrderProduct[];
}

interface OrdersSummary {
  totalValue: number;
  statusCounts: Record<string, number>;
  daysBack: number;
}

const supabaseUrl = config.supabase.url;
const supabaseAnonKey = config.supabase.anonKey;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase URL or anon key in config');
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

export class OrderFunctions {
  private logger = logger.child({ component: 'OrderFunctions' });

  async getRecentOrders(args: RecentOrdersArgs, context: FunctionContext): Promise<FunctionResult> {
    try {
      this.logger.info('Getting recent orders', { args, userId: context.userId });

      let query = supabase
        .from('fulfillments')
        .select(
          `
          id,
          order_number,
          fulfillment_date,
          fulfillment_status,
          quantity,
          unit_price,
          total_price,
          customer_name,
          notes,
          Products!inner(
            name,
            category
          )
        `
        )
        .order('fulfillment_date', { ascending: false });

      if (args.customer_name) {
        query = query.ilike('customer_name', `%${args.customer_name}%`);
      }

      if (args.status) {
        query = query.eq('fulfillment_status', args.status);
      }

      const daysBack = args.days ?? 7;
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - daysBack);
      query = query.gte('fulfillment_date', startDate.toISOString());

      const limit = args.limit ?? 10;
      const { data: orders, error } = await query.limit(limit);

      if (error) {
        throw error;
      }

      const typedOrders: OrderRecord[] = (orders ?? []) as OrderRecord[];
      const totalValue = typedOrders.reduce((sum, order) => sum + (order.total_price ?? 0), 0);
      const statusCounts = typedOrders.reduce<Record<string, number>>((counts, order) => {
        const status = order.fulfillment_status;
        counts[status] = (counts[status] ?? 0) + 1;
        return counts;
      }, {});

      const voiceResponse = this.formatOrdersForVoice(typedOrders, args, {
        totalValue,
        statusCounts,
        daysBack,
      });

      return {
        success: true,
        data: {
          orders: typedOrders,
          summary: {
            totalOrders: typedOrders.length,
            totalValue,
            statusCounts,
            timeRange: `Last ${daysBack} days`,
          },
        },
        message: `Found ${typedOrders.length} orders in the last ${daysBack} days`,
        formattedForVoice: voiceResponse,
      };
    } catch (error) {
      this.logger.error('Error getting recent orders', { error });
      const errMsg = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        message: `Error retrieving orders: ${errMsg}`,
        formattedForVoice:
          'Sorry, I encountered an error retrieving order information. Please try again.',
        error: {
          code: 'ORDER_QUERY_ERROR',
          message: errMsg,
        },
      };
    }
  }

  async getOrderStatus(args: OrderStatusArgs, context: FunctionContext): Promise<FunctionResult> {
    try {
      this.logger.info('Getting order status', { args, userId: context.userId });

      let query = supabase
        .from('fulfillments')
        .select(
          `
          id,
          order_number,
          fulfillment_date,
          fulfillment_status,
          quantity,
          unit_price,
          total_price,
          customer_name,
          notes,
          Products!inner(name, category)
        `
        );

      if (args.order_number) {
        query = query.eq('order_number', args.order_number);
      } else if (args.customer_name) {
        query = query.ilike('customer_name', `%${args.customer_name}%`);
      } else {
        return {
          success: false,
          message: 'Please provide either an order number or customer name',
          formattedForVoice:
            'I need either an order number or customer name to look up the order status.',
          followUpQuestions: ['What is the order number?', 'What is the customer name?'],
        };
      }

      const { data: orders, error } = await query.limit(5);

      if (error) {
        throw error;
      }

      const typedOrders: OrderRecord[] = (orders ?? []) as OrderRecord[];

      if (typedOrders.length === 0) {
        return {
          success: true,
          data: [],
          message: 'No orders found matching the criteria',
          formattedForVoice: args.order_number
            ? `I couldn't find an order with number ${args.order_number}.`
            : `I couldn't find any orders for customer ${args.customer_name}.`,
        };
      }

      const voiceResponse = this.formatOrderStatusForVoice(typedOrders, args);

      return {
        success: true,
        data: typedOrders,
        message: `Found ${typedOrders.length} order(s)`,
        formattedForVoice: voiceResponse,
      };
    } catch (error) {
      this.logger.error('Error getting order status', { error });
      const errMsg = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        message: `Error retrieving order status: ${errMsg}`,
        formattedForVoice:
          'Sorry, I encountered an error looking up the order status. Please try again.',
        error: {
          code: 'ORDER_STATUS_ERROR',
          message: errMsg,
        },
      };
    }
  }

  private formatOrdersForVoice(
    orders: OrderRecord[],
    args: RecentOrdersArgs,
    summary: OrdersSummary,
  ): string {
    if (orders.length === 0) {
      return `No orders found in the last ${summary.daysBack} days${
        args.customer_name ? ` for customer ${args.customer_name}` : ''
      }${args.status ? ` with status ${args.status}` : ''}.`;
    }

    const valueText = summary.totalValue > 0 ? ` worth $${Math.round(summary.totalValue).toLocaleString()}` : '';
    let response = `Found ${orders.length} orders in the last ${summary.daysBack} days${valueText}. `;

    const statusEntries = Object.entries(summary.statusCounts);
    if (statusEntries.length > 1) {
      const statusText = statusEntries.map(([status, count]) => `${count} ${status}`).join(', ');
      response += `Status breakdown: ${statusText}. `;
    }

    const recentOrders = orders.slice(0, 3);
    if (recentOrders.length > 0) {
      const recentText = recentOrders
        .map((order) => {
          const date = new Date(order.fulfillment_date).toLocaleDateString();
          const price = order.total_price ? `$${Math.round(order.total_price)}` : '';
          const customerName = order.customer_name ?? 'Unknown customer';
          return `${customerName} on ${date}${price ? ` for ${price}` : ''}`;
        })
        .join(', ');
      response += `Most recent orders: ${recentText}.`;
    }

    return response.trim();
  }

  private formatOrderStatusForVoice(orders: OrderRecord[], args: OrderStatusArgs): string {
    if (orders.length === 1) {
      const order = orders[0];
      const date = new Date(order.fulfillment_date).toLocaleDateString();
      const price = order.total_price ? ` for $${Math.round(order.total_price)}` : '';
      const quantity = order.quantity ?? 'the ordered';
      const productNames = order.Products?.map((product) => product.name).join(', ') ?? 'products';

      return `Order ${order.order_number} for ${order.customer_name ?? 'Unknown customer'} is ${
        order.fulfillment_status
      }. It was placed on ${date}${price} for ${quantity} units of ${productNames}.`;
    }

    const summary = orders
      .map((order) => {
        const date = new Date(order.fulfillment_date).toLocaleDateString();
        return `Order ${order.order_number} is ${order.fulfillment_status} from ${date}`;
      })
      .join(', ');

    return `Found ${orders.length} orders. ${summary}.`;
  }
}
