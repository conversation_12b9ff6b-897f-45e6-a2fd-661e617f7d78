/**
 * Temperature Functions for Voice Agent
 * 
 * Handles temperature sensor queries and monitoring operations.
 */
import { logger } from '../utils/logger';
import { FunctionContext, FunctionResult } from './FunctionRegistry';
import { getTemperatureReadings } from './temperature-adapter';
import type {
  TemperatureSummarySensor,
  TemperatureWithLotResponse,
  StorageAreaTemperatureSummary,
  LotAgingAlert,
} from './types';

interface TemperatureHandlerArgs {
  sensor_id?: string;
  location?: string;
  storage_area?: string;
  lot_code?: string;
  hours?: number;
  alerts_only?: boolean;
  include_lot_context?: boolean;
  include_storage_summary?: boolean;
  include_aging?: boolean;
  include_violations?: boolean;
}

interface FreezerStatusArgs {
  storage_area?: string;
  sensor_id?: string;
  hours?: number;
  include_violations?: boolean;
}

interface SummaryMessageParams {
  totalReadings: number;
  sensorCount: number;
  alertsFound: number;
  alertsOnly: boolean;
  storageAreaCount: number;
  lotAlertCount: number;
}

export class TemperatureFunctions {
  private logger = logger.child({ component: 'TemperatureFunctions' });

  async getTemperatureReadings(
    args: TemperatureHandlerArgs,
    context: FunctionContext,
  ): Promise<FunctionResult> {
    try {
      this.logger.info('Getting temperature readings', { args, userId: context.userId });

      const hoursBack = args.hours ?? 24;
      const response = await this.fetchAdapterData({
        ...args,
        hours: hoursBack,
        include_lot_context: args.include_lot_context ?? true,
      });

      const sensors = response.summary?.sensors ?? [];
      const totalReadings = response.summary?.totalReadings ?? response.readings.length;
      const alertCount = response.summary?.alertCount ?? 0;
      const timeRange = `Last ${hoursBack} hours`;

      const storageSummaries = response.storageSummaries ?? [];
      const lotAlerts = response.lotAgingAlerts ?? [];
      const temperatureViolations = response.temperatureViolations ?? [];

      const voiceResponse = this.formatTemperatureForVoice({
        sensors,
        storageSummaries,
        lotAlerts,
        alertsOnly: Boolean(args.alerts_only),
      });

      return {
        success: true,
        data: {
          sensors,
          readings: response.readings,
          totalReadings,
          timeRange,
          alertsFound: alertCount,
          generatedAt: response.summary?.generatedAt ?? new Date().toISOString(),
          alertsOnly: Boolean(args.alerts_only),
          sensorId: args.sensor_id ?? null,
          location: args.location ?? null,
          storageSummaries,
          lotAgingAlerts: lotAlerts,
          temperatureViolations,
        },
        message: this.buildSummaryMessage({
          totalReadings,
          sensorCount: sensors.length,
          alertsFound: alertCount,
          alertsOnly: Boolean(args.alerts_only),
          storageAreaCount: storageSummaries.length,
          lotAlertCount: lotAlerts.length,
        }),
        formattedForVoice: voiceResponse
      };

    } catch (error) {
      const err = error instanceof Error ? error : new Error('Unknown temperature adapter failure');
      this.logger.error('Error getting temperature readings', err);
      return {
        success: false,
        message: `Error retrieving temperature readings: ${err.message}`,
        formattedForVoice: 'Sorry, I encountered an error retrieving temperature data. Please try again.',
        error: {
          code: 'TEMPERATURE_QUERY_ERROR',
          message: err.message,
          details: err.stack,
        },
      };
    }
  }

  async getFreezerStatus(
    args: FreezerStatusArgs,
    context: FunctionContext,
  ): Promise<FunctionResult> {
    try {
      this.logger.info('Getting freezer status', { args, userId: context.userId });

      const hoursBack = args.hours ?? 24;
      const response = await this.fetchAdapterData({
        sensor_id: args.sensor_id,
        storage_area: args.storage_area,
        hours: hoursBack,
        include_lot_context: true,
        include_storage_summary: true,
        include_aging: true,
        include_violations: args.include_violations ?? true,
      });

      const storageSummaries = this.filterStorageSummaries(
        response.storageSummaries ?? [],
        args.storage_area,
      );
      const sensors = response.summary?.sensors ?? [];
      const timeRange = `Last ${hoursBack} hours`;
      const temperatureViolations = response.temperatureViolations ?? [];
      const lotAlerts = response.lotAgingAlerts ?? [];

      const voiceResponse = this.formatFreezerStatusVoice({
        storageSummaries,
        sensors,
        requestedArea: args.storage_area,
        timeRange,
        violations: temperatureViolations,
        lotAlerts,
      });

      return {
        success: true,
        data: {
          storageSummaries,
          sensors,
          timeRange,
          requestedArea: args.storage_area ?? null,
          temperatureViolations,
          lotAgingAlerts: lotAlerts,
        },
        message: voiceResponse,
        formattedForVoice: voiceResponse,
      };
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Unknown freezer status failure');
      this.logger.error('Error getting freezer status', err);
      return {
        success: false,
        message: `Error retrieving freezer status: ${err.message}`,
        formattedForVoice: 'Sorry, I could not load the freezer data. Please try again shortly.',
        error: {
          code: 'FREEZER_STATUS_ERROR',
          message: err.message,
          details: err.stack,
        },
      };
    }
  }

  private async fetchAdapterData(args: TemperatureHandlerArgs): Promise<TemperatureWithLotResponse> {
    return getTemperatureReadings({
      sensorId: args.sensor_id,
      location: args.location,
      storageAreaName: args.storage_area,
      lotCode: args.lot_code,
      hours: args.hours,
      alertsOnly: args.alerts_only,
      includeLotContext: args.include_lot_context ?? true,
      includeStorageSummary: args.include_storage_summary ?? true,
      includeAging: args.include_aging ?? true,
      includeViolations: args.include_violations ?? true,
    });
  }

  private formatTemperatureForVoice(params: {
    sensors: TemperatureSummarySensor[];
    storageSummaries: StorageAreaTemperatureSummary[];
    lotAlerts: LotAgingAlert[];
    alertsOnly: boolean;
  }): string {
    const { sensors, storageSummaries, lotAlerts, alertsOnly } = params;

    if (alertsOnly) {
      const temperatureAlertCount = sensors.reduce((sum, sensor) => sum + sensor.alertCount, 0);
      const lotAlertSnippet = this.describeLotAlerts(lotAlerts);

      if (temperatureAlertCount === 0 && !lotAlertSnippet) {
        return 'No temperature alerts or lot issues found. All monitored areas are within normal ranges.';
      }

      const sensorSnippet = temperatureAlertCount > 0
        ? sensors
            .filter((sensor) => sensor.alertCount > 0)
            .map((sensor) => {
              const reading = sensor.latestReading != null ? `${Math.round(sensor.latestReading)}°${sensor.unit}` : 'no recent reading';
              return `${sensor.sensorName ?? sensor.sensorId}: ${reading}`;
            })
            .join('; ')
        : '';

      const prefix = temperatureAlertCount > 0
        ? `Found ${temperatureAlertCount} temperature alert${temperatureAlertCount === 1 ? '' : 's'}`
        : 'Temperature readings are stable';

      const combined = [sensorSnippet, lotAlertSnippet].filter(Boolean).join('. ');
      return `${prefix}.${combined ? ` ${combined}.` : ''}`.trim();
    }

    if (storageSummaries.length > 0) {
      const areaSummaries = storageSummaries
        .slice(0, 3)
        .map((summary) => this.describeStorageArea(summary))
        .filter(Boolean)
        .join(' ');

      const additionalAreas = storageSummaries.length > 3
        ? `Plus ${storageSummaries.length - 3} more storage area${storageSummaries.length - 3 === 1 ? '' : 's'} monitored.`
        : '';

      const lotAlertSnippet = this.describeLotAlerts(lotAlerts);

      return [areaSummaries, additionalAreas, lotAlertSnippet].filter(Boolean).join(' ').trim() ||
        'Temperature monitoring is active, but no recent readings are available.';
    }

    if (sensors.length === 0) {
      return 'No temperature readings found for the specified criteria.';
    }

    const summaryText = sensors
      .slice(0, 5)
      .map((sensor) => {
        const reading = sensor.latestReading != null ? `${Math.round(sensor.latestReading)}°${sensor.unit}` : 'no recent reading';
        return `${sensor.sensorName ?? sensor.sensorId}: ${reading}, ${sensor.status}`;
      })
      .join(', ');

    const moreCount = sensors.length - 5;
    const moreText = moreCount > 0 ? ` and ${moreCount} more sensor${moreCount === 1 ? '' : 's'}` : '';

    return `Current temperature readings: ${summaryText}${moreText}.`;
  }

  private formatFreezerStatusVoice(params: {
    storageSummaries: StorageAreaTemperatureSummary[];
    sensors: TemperatureSummarySensor[];
    requestedArea?: string;
    timeRange: string;
    violations: TemperatureWithLotResponse['temperatureViolations'];
    lotAlerts: LotAgingAlert[] | undefined;
  }): string {
    const { storageSummaries, sensors, requestedArea, timeRange, violations, lotAlerts } = params;

    if (storageSummaries.length === 0 && sensors.length === 0) {
      return requestedArea
        ? `I could not find any recent readings for the ${requestedArea} storage area.`
        : 'No recent freezer readings are available.';
    }

    const summarySnippet = storageSummaries
      .slice(0, 2)
      .map((summary) => this.describeStorageArea(summary))
      .filter((snippet): snippet is string => Boolean(snippet))
      .join(' ');

    const sensorSnippet = sensors
      .slice(0, 3)
      .map((sensor) => {
        const reading = sensor.latestReading != null
          ? `${Math.round(sensor.latestReading)}°${sensor.unit}`
          : 'no recent reading';
        return `${sensor.sensorName ?? sensor.sensorId}: ${reading}`;
      })
      .join('; ');

    const violationCount = violations?.length ?? 0;
    const lotAlertSnippet = this.describeLotAlerts(lotAlerts ?? []);

    const parts = [
      requestedArea ? `Status for ${requestedArea}:` : 'Freezer status update:',
      summarySnippet,
      sensorSnippet ? `Sensor snapshot (${timeRange}): ${sensorSnippet}.` : '',
      violationCount > 0
        ? `${violationCount} temperature violation${violationCount === 1 ? '' : 's'} flagged recently.`
        : '',
      lotAlertSnippet,
    ];

    return parts.filter(Boolean).join(' ').replace(/\s+/g, ' ').trim();
  }

  private filterStorageSummaries(
    summaries: StorageAreaTemperatureSummary[],
    requestedArea?: string,
  ): StorageAreaTemperatureSummary[] {
    if (!requestedArea) {
      return summaries;
    }

    const normalizedRequest = requestedArea.trim().toLowerCase();
    return summaries.filter((summary) => {
      const name = summary.storageArea?.name ?? '';
      const location = summary.sensor?.location_label ?? '';
      return (
        name.toLowerCase().includes(normalizedRequest) ||
        location.toLowerCase().includes(normalizedRequest)
      );
    });
  }

  private describeStorageArea(summary: StorageAreaTemperatureSummary): string | null {
    const name = summary.storageArea?.name ?? 'Storage area';
    const temperatureF = summary.latestTemperatureF;
    const temperatureText = typeof temperatureF === 'number'
      ? `${Math.round(temperatureF)}°F`
      : 'no recent temperature';

    const lotCount = summary.lots?.length ?? 0;
    const alert = summary.agingAlerts?.[0];

    let lotText = '';
    if (alert) {
      const lotName = alert.lotCode ?? alert.productName ?? 'a lot';
      const descriptor = alert.status === 'critical'
        ? 'needs immediate inspection'
        : alert.status === 'expired'
        ? 'is past its hold date'
        : `is approaching ${alert.thresholdDays} days`;
      lotText = ` ${lotName} ${descriptor}.`;
    } else if (lotCount > 0) {
      lotText = ` ${lotCount} lot${lotCount === 1 ? '' : 's'} stored.`;
    }

    return `${name} is at ${temperatureText}.${lotText}`.trim();
  }

  private describeLotAlerts(alerts: LotAgingAlert[]): string | null {
    if (!alerts.length) {
      return null;
    }

    const primaryAlerts = alerts.slice(0, 2).map((alert) => {
      const lotName = alert.lotCode ?? alert.productName ?? 'A lot';
      const area = alert.storageAreaName ? ` in ${alert.storageAreaName}` : '';
      const action = alert.recommendedAction ?? (
        alert.status === 'critical'
          ? 'requires immediate inspection'
          : alert.status === 'expired'
          ? 'has passed its hold date'
          : 'needs review'
      );
      return `${lotName}${area} ${action.toLowerCase()}`;
    });

    const additional = alerts.length > 2
      ? ` ${alerts.length - 2} more lot${alerts.length - 2 === 1 ? '' : 's'} require attention.`
      : '';

    return `${primaryAlerts.join('. ')}.${additional}`.trim();
  }

  private buildSummaryMessage(params: SummaryMessageParams): string {
    if (params.alertsOnly) {
      return params.alertsFound > 0
        ? `Found ${params.alertsFound} temperature alerts across ${params.sensorCount} sensor${params.sensorCount === 1 ? '' : 's'}.`
        : 'No temperature alerts found for the specified criteria.';
    }

    const storageSummary = params.storageAreaCount > 0
      ? ` Monitoring ${params.storageAreaCount} storage area${params.storageAreaCount === 1 ? '' : 's'}.`
      : '';

    const lotSummary = params.lotAlertCount > 0
      ? ` ${params.lotAlertCount} lot${params.lotAlertCount === 1 ? '' : 's'} need attention.`
      : '';

    return `Found ${params.totalReadings} temperature readings across ${params.sensorCount} sensor${params.sensorCount === 1 ? '' : 's'}.${storageSummary}${lotSummary}`.trim();
  }
}
