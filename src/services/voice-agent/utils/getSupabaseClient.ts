import { createClient, type SupabaseClient } from '@supabase/supabase-js';
import { config } from '../config';
import { appEnv } from '@/lib/config/env';

const globalForVoiceAgent = globalThis as typeof globalThis & {
  __voiceAgentSupabase?: SupabaseClient | null;
};

let client: SupabaseClient | null = null;

/**
 * Create or get the cached Supabase client for voice agent services
 * Uses the same configuration validation and patterns as the main Supabase client
 */
export const getSupabaseClient = (): SupabaseClient => {
  if (client) {
    return client;
  }

  const { url, anonKey, serviceRoleKey } = config.supabase;
  const isServer = typeof window === 'undefined';

  if (isServer && globalForVoiceAgent.__voiceAgentSupabase) {
    client = globalForVoiceAgent.__voiceAgentSupabase;
    return client;
  }

  // Enhanced validation with better error messages
  if (!url || !anonKey) {
    throw new Error(
      'Supabase configuration is incomplete for voice-agent adapters. ' +
      'Ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set.'
    );
  }

  try {
    const useServiceRole = isServer && serviceRoleKey;
    const supabaseKey = useServiceRole ? serviceRoleKey! : anonKey;

    if (isServer && !serviceRoleKey) {
      console.warn(
        '[Voice Agent] Service role key not configured – falling back to anon key. ' +
        'Voice functions may fail due to RLS policies.'
      );
    }

    // Create client with environment-aware options
    client = createClient(url, supabaseKey, {
      db: { schema: 'public' },
      auth: {
        persistSession: !isServer,
        autoRefreshToken: !isServer,
      },
    });

    if (isServer) {
      globalForVoiceAgent.__voiceAgentSupabase = client;
    }

    // Log connection info for debugging in development
    if (appEnv.isDevelopment) {
      const host = new URL(url).host;
      const isLocal = url.includes('127.0.0.1') || url.includes('localhost');
      console.info('[Voice Agent] Supabase client initialized:', {
        host,
        isLocalDocker: isLocal,
        environment: appEnv.mode,
        mode: useServiceRole ? 'service-role' : 'anon'
      });
    }

    return client;
  } catch (error) {
    console.error('[Voice Agent] Failed to create Supabase client:', error);
    throw new Error('Failed to initialize Supabase client for voice agent services');
  }
};

/**
 * Reset the cached client (useful for testing or configuration changes)
 */
export const resetSupabaseClient = (): void => {
  client = null;
};
