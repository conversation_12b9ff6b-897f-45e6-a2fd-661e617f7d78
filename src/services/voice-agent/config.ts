/**
 * Voice Agent Configuration
 *
 * Centralized configuration for the voice agent service.
 */

import { appEnv } from '@/lib/config/env';

// Server-specific environment variables not covered by appEnv
const serverEnv = {
  voiceAgentPort: parseInt(process.env.VOICE_AGENT_PORT ?? '3002', 10),
  logLevel: process.env.LOG_LEVEL ?? 'info',
};

export const config = {
  // Server configuration
  port: serverEnv.voiceAgentPort,
  nodeEnv: appEnv.mode,

  // OpenAI configuration using centralized appEnv
  openai: {
    apiKey: appEnv.openAi.apiKey,
    model: appEnv.realtime.model,
    voice: appEnv.realtime.voice as 'alloy' | 'echo' | 'fable' | 'onyx' | 'nova' | 'shimmer',
    temperature: 0.3,
    maxTokens: 4096
  },

  // Supabase configuration using centralized appEnv
  supabase: {
    url: appEnv.supabase.url,
    anonKey: appEnv.supabase.anon<PERSON>ey,
    serviceRoleKey: appEnv.supabase.serviceRoleKey
  },

  // Voice agent specific settings
  voiceAgent: {
    maxSessionDuration: 30 * 60 * 1000, // 30 minutes
    maxConcurrentSessions: 100,
    responseTimeout: 30000, // 30 seconds
    maxRetries: 3,
    retryDelay: 1000 // 1 second
  },

  // Rate limiting
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100, // per window
    skipSuccessfulRequests: false
  },

  // Logging configuration
  logging: {
    level: serverEnv.logLevel,
    format: appEnv.isProduction ? 'json' : 'simple'
  },

  // Performance monitoring
  monitoring: {
    enabled: appEnv.isProduction,
    metricsInterval: 60000, // 1 minute
    healthCheckInterval: 30000 // 30 seconds
  }
};

// Validation using centralized configuration
export function validateConfig(): void {
  const errors: string[] = [];

  // Validate OpenAI configuration
  if (!config.openai.apiKey) {
    errors.push('OpenAI API key is required (OPENAI_API_KEY or VITE_OPENAI_API_KEY)');
  }

  // Validate Supabase configuration
  if (!config.supabase.url) {
    errors.push('Supabase URL is required (VITE_SUPABASE_URL)');
  }

  if (!config.supabase.anonKey) {
    errors.push('Supabase anon key is required (VITE_SUPABASE_ANON_KEY)');
  }

  // Validate voice agent port
  if (isNaN(config.port) || config.port <= 0) {
    errors.push('Invalid voice agent port configuration');
  }

  // Log validation warnings for development
  if (appEnv.isDevelopment) {
    const warnings: string[] = [];

    if (!config.supabase.serviceRoleKey) {
      warnings.push('Service role key not configured - some privileged operations may not work');
    }

    if (warnings.length > 0) {
      console.warn('[Voice Agent] Configuration warnings:', warnings);
    }
  }

  if (errors.length > 0) {
    throw new Error(`Voice Agent configuration validation failed: ${errors.join(', ')}`);
  }
}
