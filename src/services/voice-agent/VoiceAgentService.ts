/**
 * Voice Agent Service
 * 
 * Core service for managing OpenAI Realtime API connections and voice interactions.
 */

import { WebSocket } from 'ws';
import { RealtimeAPI } from '@openai/realtime-api-beta';
import { config } from './config';
import { logger } from './utils/logger';
import { ConversationManager } from './ConversationManager';
import { FunctionRegistry } from './functions/FunctionRegistry';
import { performanceOptimizer } from './PerformanceOptimizer';
import { errorRecoveryManager } from './ErrorRecoveryManager';
import { deviceOptimizer } from './DeviceOptimizer';

export interface VoiceAgentOptions {
  userId: string;
  sessionId: string;
  userRoles: string[];
  websocket: WebSocket;
}

type AudioChunkPayload = ArrayBuffer | Uint8Array;

interface TextInputPayload {
  text: string;
}

type VoiceAgentMessageData = AudioChunkPayload | TextInputPayload | Record<string, unknown> | undefined;

export interface VoiceAgentMessage {
  type: string;
  data?: VoiceAgentMessageData;
  timestamp?: string;
  sessionId?: string;
}

const isAudioChunkPayload = (data: VoiceAgentMessageData): data is AudioChunkPayload => {
  return data instanceof Uint8Array || data instanceof ArrayBuffer;
};

const isTextInputPayload = (data: VoiceAgentMessageData): data is TextInputPayload => {
  return typeof data === 'object' && data !== null && 'text' in data && typeof (data as TextInputPayload).text === 'string';
};

export class VoiceAgentService {
  private userId: string;
  private sessionId: string;
  private userRoles: string[];
  private websocket: WebSocket;
  private realtimeAPI: RealtimeAPI | null = null;
  private conversationManager: ConversationManager;
  private functionRegistry: FunctionRegistry;
  private logger: typeof logger;
  private isInitialized = false;
  private isConnected = false;

  constructor(options: VoiceAgentOptions) {
    this.userId = options.userId;
    this.sessionId = options.sessionId;
    this.userRoles = options.userRoles;
    this.websocket = options.websocket;

    // Add to performance monitoring
    performanceOptimizer.addConnection(this.websocket);
    
    // Create logger with context
    this.logger = logger.child({
      userId: this.userId,
      sessionId: this.sessionId
    });

    // Initialize managers
    this.conversationManager = new ConversationManager({
      userId: this.userId,
      sessionId: this.sessionId
    });

    this.functionRegistry = new FunctionRegistry({
      userId: this.userId,
      userRoles: this.userRoles
    });
  }

  async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing voice agent service');

      // Initialize OpenAI Realtime API
      this.realtimeAPI = new RealtimeAPI({
        apiKey: config.openai.apiKey,
        dangerouslyAllowBrowser: false
      });

      // Configure the session
      await this.configureRealtimeSession();

      // Register functions
      await this.registerFunctions();

      // Set up event handlers
      this.setupEventHandlers();

      // Initialize conversation manager
      await this.conversationManager.initialize();

      this.isInitialized = true;
      this.logger.info('Voice agent service initialized successfully');

      // Send initialization success to client
      this.sendToClient({
        type: 'initialized',
        data: {
          sessionId: this.sessionId,
          status: 'ready'
        }
      });

    } catch (error) {
      this.logger.error('Failed to initialize voice agent service', error);
      
      // Use error recovery for initialization failures
      const recoveryResult = await errorRecoveryManager.handleError(error, {
        operation: 'voice_agent_initialization',
        userId: this.userId,
        sessionId: this.sessionId,
        timestamp: new Date(),
        attemptNumber: 1,
        originalError: error
      });

      this.sendToClient({
        type: 'error',
        data: recoveryResult.error ?? {
          code: 'INITIALIZATION_FAILED',
          message: 'Failed to initialize voice agent'
        }
      });
      throw error;
    }
  }

  private async configureRealtimeSession(): Promise<void> {
    if (!this.realtimeAPI) {
      throw new Error('Realtime API not initialized');
    }

    // Get device-optimized configuration
    const deviceConfig = deviceOptimizer.getConfiguration();
    const isMobile = deviceOptimizer.isMobile();

    const sessionConfig = {
      model: config.openai.model,
      voice: config.openai.voice,
      instructions: this.getSystemInstructions(),
      modalities: ['text', 'audio'],
      temperature: config.openai.temperature,
      max_response_output_tokens: config.openai.maxTokens,
      turn_detection: {
        type: 'server_vad',
        threshold: isMobile ? 0.6 : 0.5, // Higher threshold for mobile
        prefix_padding_ms: isMobile ? 200 : 300,
        silence_duration_ms: isMobile ? 800 : 500 // Longer silence for mobile
      },
      input_audio_transcription: {
        model: 'whisper-1'
      },
      // Device-optimized audio settings
      audio_format: deviceConfig?.optimization.useCompression ? 'pcm16' : 'g711_ulaw',
      sample_rate: deviceConfig?.audio.sampleRate ?? 16000
    };

    await this.realtimeAPI.updateSession(sessionConfig);
    this.logger.debug('Realtime session configured with device optimization', { 
      sessionConfig,
      deviceType: deviceOptimizer.getDeviceInfo()?.type,
      isMobile
    });
  }

  private getSystemInstructions(): string {
    return `You are a helpful voice assistant for a seafood distribution system. You can help users with:

1. Inventory queries - Check current stock levels, product information, and availability
2. Order management - Retrieve order information, status updates, and history
3. Temperature monitoring - Check sensor readings, alerts, and compliance status
4. Compliance checking - HACCP compliance status, FDA requirements, and audit information
5. General assistance - Answer questions about the system and guide users

Guidelines:
- Be concise but informative in your responses
- Always confirm actions before executing them
- If you need clarification, ask specific questions
- For sensitive operations, verify user permissions
- Provide voice-optimized responses that are easy to understand when spoken
- Use natural, conversational language
- If you encounter errors, explain them clearly and suggest alternatives

Current user roles: ${this.userRoles.join(', ')}
Session ID: ${this.sessionId}`;
  }

  private async registerFunctions(): Promise<void> {
    if (!this.realtimeAPI) {
      throw new Error('Realtime API not initialized');
    }

    const functions = await this.functionRegistry.getAllFunctions();
    
    for (const [name, functionDef] of Object.entries(functions)) {
      await this.realtimeAPI.addTool({
        name,
        description: functionDef.description,
        parameters: functionDef.parameters
      }, async (args: Record<string, unknown>) => {
        return await this.functionRegistry.executeFunction(name, args);
      });
    }

    this.logger.info(`Registered ${Object.keys(functions).length} functions`);
  }

  private setupEventHandlers(): void {
    if (!this.realtimeAPI) {
      throw new Error('Realtime API not initialized');
    }

    // Connection events
    this.realtimeAPI.on('open', () => {
      this.isConnected = true;
      this.logger.info('OpenAI Realtime API connected');
      this.sendToClient({
        type: 'connected',
        data: { status: 'connected' }
      });
    });

    this.realtimeAPI.on('close', () => {
      this.isConnected = false;
      this.logger.info('OpenAI Realtime API disconnected');
      this.sendToClient({
        type: 'disconnected',
        data: { status: 'disconnected' }
      });
    });

    this.realtimeAPI.on('error', async (error: unknown) => {
      const normalizedError = error instanceof Error ? error : new Error(String(error));
      this.logger.error('OpenAI Realtime API error', normalizedError);
      
      // Use error recovery for OpenAI API errors
      const recoveryResult = await errorRecoveryManager.handleError(normalizedError, {
        operation: 'openai_realtime_api',
        userId: this.userId,
        sessionId: this.sessionId,
        timestamp: new Date(),
        attemptNumber: 1,
        originalError: normalizedError
      });

      this.sendToClient({
        type: 'error',
        data: recoveryResult.error ?? {
          code: 'REALTIME_API_ERROR',
          message: recoveryResult.formattedForVoice ?? 'Voice processing error occurred'
        }
      });
    });

    // Conversation events
    this.realtimeAPI.on('conversation.item.created', (event: unknown) => {
      this.logger.debug('Conversation item created', event);
      this.conversationManager.addTurn(event);
    });

    this.realtimeAPI.on('response.audio_transcript.done', (event: { transcript: string }) => {
      this.logger.debug('Audio transcript completed', event);
      this.sendToClient({
        type: 'transcript',
        data: {
          text: event.transcript,
          type: 'response'
        }
      });
    });

    this.realtimeAPI.on('input_audio_buffer.speech_started', () => {
      this.sendToClient({
        type: 'speech_started',
        data: { status: 'listening' }
      });
    });

    this.realtimeAPI.on('input_audio_buffer.speech_stopped', () => {
      this.sendToClient({
        type: 'speech_stopped',
        data: { status: 'processing' }
      });
    });

    // Function call events
    this.realtimeAPI.on('response.function_call_arguments.done', (event: unknown) => {
      this.logger.debug('Function call completed', event);
      // Function execution is handled by the registered callback
    });
  }

  async handleMessage(message: VoiceAgentMessage): Promise<void> {
    if (!this.isInitialized || !this.realtimeAPI) {
      throw new Error('Voice agent not initialized');
    }

    const startTime = Date.now();
    this.logger.debug('Handling client message', { type: message.type });

    try {
      switch (message.type) {
        case 'audio_chunk':
          if (isAudioChunkPayload(message.data)) {
            await this.handleAudioChunk(message.data as AudioChunkPayload);
          } else {
            this.logger.warn('Invalid audio chunk payload received', {
              payloadType: typeof message.data
            });
          }
          break;

        case 'text_input':
          if (isTextInputPayload(message.data)) {
            await this.handleTextInput(message.data);
          } else {
            this.logger.warn('Invalid text input payload received', {
              payloadType: typeof message.data
            });
          }
          break;

        case 'start_conversation':
          await this.startConversation();
          break;

        case 'end_conversation':
          await this.endConversation();
          break;

        case 'get_status':
          await this.sendStatus();
          break;

        default:
          this.logger.warn('Unknown message type', { type: message.type });
          this.sendToClient({
            type: 'error',
            data: {
              code: 'UNKNOWN_MESSAGE_TYPE',
              message: `Unknown message type: ${message.type}`
            }
          });
      }
    } catch (error) {
      const normalizedError = error instanceof Error ? error : new Error(String(error));
      this.logger.error('Error handling message', normalizedError);
      
      // Use error recovery for message handling failures
      const recoveryResult = await errorRecoveryManager.handleError(normalizedError, {
        operation: 'message_handling',
        userId: this.userId,
        sessionId: this.sessionId,
        timestamp: new Date(),
        attemptNumber: 1,
        originalError: normalizedError,
        metadata: { messageType: message.type }
      });

      this.sendToClient({
        type: 'error',
        data: recoveryResult.error ?? {
          code: 'MESSAGE_HANDLING_ERROR',
          message: 'Failed to process message'
        }
      });
    } finally {
      // Track response time for performance monitoring
      const responseTime = Date.now() - startTime;
      this.logger.debug('Message processing completed', {
        type: message.type,
        responseTime
      });
    }
  }

  private async handleAudioChunk(audioData: AudioChunkPayload): Promise<void> {
    if (!this.realtimeAPI) return;

    await this.realtimeAPI.appendInputAudio(audioData);
  }

  private async handleTextInput(textData: TextInputPayload): Promise<void> {
    if (!this.realtimeAPI) return;

    await this.realtimeAPI.sendUserMessage(textData.text);
  }

  private async startConversation(): Promise<void> {
    this.logger.info('Starting conversation');
    
    this.sendToClient({
      type: 'conversation_started',
      data: { status: 'active' }
    });
  }

  private async endConversation(): Promise<void> {
    this.logger.info('Ending conversation');
    await this.conversationManager.endSession();
    
    this.sendToClient({
      type: 'conversation_ended',
      data: { status: 'ended' }
    });
  }

  private async sendStatus(): Promise<void> {
    const status = {
      initialized: this.isInitialized,
      connected: this.isConnected,
      sessionId: this.sessionId,
      userId: this.userId,
      conversationActive: this.conversationManager.isActive()
    };

    this.sendToClient({
      type: 'status',
      data: status
    });
  }

  private sendToClient(message: VoiceAgentMessage): void {
    if (this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify({
        ...message,
        timestamp: new Date().toISOString(),
        sessionId: this.sessionId
      }));
    }
  }

  async cleanup(): Promise<void> {
    this.logger.info('Cleaning up voice agent service');

    try {
      // End conversation if active
      if (this.conversationManager.isActive()) {
        await this.conversationManager.endSession();
      }

      // Close OpenAI connection
      if (this.realtimeAPI && this.isConnected) {
        await this.realtimeAPI.disconnect();
      }

      // Remove from performance monitoring
      performanceOptimizer.removeConnection(this.websocket);

      // Cleanup managers
      await this.conversationManager.cleanup();

      this.isInitialized = false;
      this.isConnected = false;

      this.logger.info('Voice agent service cleanup completed');
    } catch (error) {
      this.logger.error('Error during cleanup', error);
    }
  }
}
