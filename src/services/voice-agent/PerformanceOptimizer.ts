/**
 * Performance Optimizer for Voice Agent
 * 
 * Implements real-time processing optimizations including:
 * - Response latency optimization
 * - Caching strategies
 * - Performance monitoring
 * - Database query optimization
 * - Connection pooling
 */

import { logger } from './utils/logger';
import { config } from './config';

export interface PerformanceMetrics {
  responseTime: number;
  cacheHitRate: number;
  activeConnections: number;
  memoryUsage: NodeJS.MemoryUsage;
  timestamp: Date;
}

export interface CacheEntry<T = unknown> {
  data: T;
  timestamp: number;
  ttl: number;
  hits: number;
}

export class PerformanceOptimizer {
  private cache: Map<string, CacheEntry<unknown>> = new Map();
  private connectionPool: Set<unknown> = new Set();
  private metrics: PerformanceMetrics[] = [];
  private metricsInterval: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;

  // Performance thresholds
  private readonly RESPONSE_TIME_TARGET = 500; // 500ms target
  private readonly CACHE_TTL_DEFAULT = 300000; // 5 minutes
  private readonly CACHE_TTL_QUICK = 60000; // 1 minute for frequently changing data
  private readonly CACHE_TTL_LONG = 3600000; // 1 hour for stable data
  private readonly MAX_CACHE_SIZE = 1000;
  private readonly MAX_METRICS_HISTORY = 100;

  constructor() {
    this.startPerformanceMonitoring();
    this.startCacheCleanup();
  }

  /**
   * Start performance monitoring
   */
  private startPerformanceMonitoring(): void {
    if (!config.monitoring.enabled) return;

    this.metricsInterval = setInterval(() => {
      this.collectMetrics();
    }, config.monitoring.metricsInterval);

    logger.info('Performance monitoring started');
  }

  /**
   * Start cache cleanup process
   */
  private startCacheCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredCache();
    }, 60000); // Cleanup every minute
  }

  /**
   * Collect current performance metrics
   */
  private collectMetrics(): void {
    const cacheHits = Array.from(this.cache.values()).reduce((sum, entry) => sum + entry.hits, 0);
    const cacheRequests = Math.max(cacheHits, 1); // Avoid division by zero
    
    const metrics: PerformanceMetrics = {
      responseTime: this.getAverageResponseTime(),
      cacheHitRate: (cacheHits / cacheRequests) * 100,
      activeConnections: this.connectionPool.size,
      memoryUsage: process.memoryUsage(),
      timestamp: new Date()
    };

    this.metrics.push(metrics);

    // Keep only recent metrics
    if (this.metrics.length > this.MAX_METRICS_HISTORY) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS_HISTORY);
    }

    // Log performance warnings
    if (metrics.responseTime > this.RESPONSE_TIME_TARGET) {
      logger.warn('Response time exceeds target', {
        responseTime: metrics.responseTime,
        target: this.RESPONSE_TIME_TARGET
      });
    }

    if (metrics.memoryUsage.heapUsed > 500 * 1024 * 1024) { // 500MB
      logger.warn('High memory usage detected', {
        heapUsed: metrics.memoryUsage.heapUsed,
        heapTotal: metrics.memoryUsage.heapTotal
      });
    }
  }

  /**
   * Get cached data with performance optimization
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key) as CacheEntry<T> | undefined;
    
    if (!entry) {
      return null;
    }

    // Check if expired
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    // Increment hit count for metrics
    entry.hits++;
    
    return entry.data as T;
  }

  /**
   * Set cached data with optimized TTL based on data type
   */
  set<T>(key: string, data: T, customTtl?: number): void {
    // Determine optimal TTL based on key pattern
    let ttl = customTtl ?? this.CACHE_TTL_DEFAULT;
    
    if (key.includes('inventory')) {
      ttl = this.CACHE_TTL_QUICK; // Inventory changes frequently
    } else if (key.includes('product') || key.includes('vendor')) {
      ttl = this.CACHE_TTL_LONG; // Product data is relatively stable
    } else if (key.includes('temperature')) {
      ttl = this.CACHE_TTL_QUICK; // Temperature data changes frequently
    }

    // Implement cache eviction if at capacity
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      this.evictLeastUsedCache();
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      hits: 0
    };

    this.cache.set(key, entry as CacheEntry<unknown>);
  }

  /**
   * Evict least used cache entries
   */
  private evictLeastUsedCache(): void {
    // Find entries with lowest hit count
    const entries = Array.from(this.cache.entries());
    entries.sort((a, b) => a[1].hits - b[1].hits);
    
    // Remove 10% of entries
    const removeCount = Math.floor(this.cache.size * 0.1);
    for (let i = 0; i < removeCount && i < entries.length; i++) {
      this.cache.delete(entries[i][0]);
    }

    logger.debug('Cache eviction completed', { removedEntries: removeCount });
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupExpiredCache(): void {
    const now = Date.now();
    let removedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.timestamp + entry.ttl) {
        this.cache.delete(key);
        removedCount++;
      }
    }

    if (removedCount > 0) {
      logger.debug('Cache cleanup completed', { removedEntries: removedCount });
    }
  }

  /**
   * Optimize database query with caching and performance tracking
   */
  async optimizeQuery<T>(
    queryKey: string,
    queryFn: () => Promise<T>,
    options: {
      cacheTtl?: number;
      enableCache?: boolean;
      timeout?: number;
    } = {}
  ): Promise<T> {
    const startTime = Date.now();
    const { cacheTtl, enableCache = true, timeout = 10000 } = options;

    try {
      // Check cache first
      if (enableCache) {
        const cached = this.get<T>(queryKey);
        if (cached !== null) {
          const responseTime = Date.now() - startTime;
          logger.debug('Cache hit for query', { queryKey, responseTime });
          return cached;
        }
      }

      // Execute query with timeout
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Query timeout')), timeout);
      });

      const result = await Promise.race([queryFn(), timeoutPromise]);
      
      // Cache result
      if (enableCache) {
        this.set(queryKey, result, cacheTtl);
      }

      const responseTime = Date.now() - startTime;
      logger.debug('Query executed', { queryKey, responseTime, cached: false });

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error('Query optimization failed', { queryKey, responseTime, error });
      throw error;
    }
  }

  /**
   * Manage connection pool for better resource utilization
   */
  addConnection(connection: unknown): void {
    this.connectionPool.add(connection);
    logger.debug('Connection added to pool', { totalConnections: this.connectionPool.size });
  }

  removeConnection(connection: unknown): void {
    this.connectionPool.delete(connection);
    logger.debug('Connection removed from pool', { totalConnections: this.connectionPool.size });
  }

  /**
   * Get current performance metrics
   */
  getCurrentMetrics(): PerformanceMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null;
  }

  /**
   * Get performance history
   */
  getMetricsHistory(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  /**
   * Get average response time from recent metrics
   */
  private getAverageResponseTime(): number {
    if (this.metrics.length === 0) return 0;
    
    const recentMetrics = this.metrics.slice(-10); // Last 10 measurements
    const sum = recentMetrics.reduce((acc, metric) => acc + metric.responseTime, 0);
    return sum / recentMetrics.length;
  }

  /**
   * Clear cache (useful for testing or manual refresh)
   */
  clearCache(): void {
    this.cache.clear();
    logger.info('Cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    size: number;
    hitRate: number;
    totalHits: number;
    oldestEntry: Date | null;
    newestEntry: Date | null;
  } {
    const entries = Array.from(this.cache.values());
    const totalHits = entries.reduce((sum, entry) => sum + entry.hits, 0);
    const hitRate = entries.length > 0 ? (totalHits / Math.max(entries.length, 1)) : 0;
    
    const timestamps = entries.map(entry => entry.timestamp);
    const oldestEntry = timestamps.length > 0 ? new Date(Math.min(...timestamps)) : null;
    const newestEntry = timestamps.length > 0 ? new Date(Math.max(...timestamps)) : null;

    return {
      size: this.cache.size,
      hitRate,
      totalHits,
      oldestEntry,
      newestEntry
    };
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
      this.metricsInterval = null;
    }

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    this.cache.clear();
    this.connectionPool.clear();
    this.metrics = [];

    logger.info('Performance optimizer cleaned up');
  }
}

// Singleton instance for global use
export const performanceOptimizer = new PerformanceOptimizer();