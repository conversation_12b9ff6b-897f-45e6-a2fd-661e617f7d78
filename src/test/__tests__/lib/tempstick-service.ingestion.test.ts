import { beforeEach, describe, expect, it, vi } from 'vitest';

import { TempStickService } from '@/lib/tempstick-service';

const supabaseAuthGetSession = vi.fn();
const supabaseMock = {
  from: vi.fn(),
  auth: {
    getSession: supabaseAuthGetSession,
  },
};

vi.mock('@/lib/supabase', () => ({
  supabase: supabaseMock,
}));

const supabaseServiceMock = {
  from: vi.fn(),
};

vi.mock('@/lib/supabase-service', () => ({
  supabaseService: supabaseServiceMock,
}));

describe('TempStickService ingestion', () => {
  let service: TempStickService;
  const originalWindow = globalThis.window;

  beforeEach(() => {
    vi.clearAllMocks();
    delete (globalThis as Record<string, unknown>).window;
    service = new TempStickService({
      apiKey: 'test-key',
      enableHealthChecks: false,
      syncUserId: 'sync-user',
    });
    // @ts-expect-error override private client for deterministic tests
    service.apiClient = {
      getLatestReadings: vi.fn(),
      getSensors: vi.fn(),
    };
  });

  afterAll(() => {
    if (originalWindow) {
      (globalThis as Record<string, unknown>).window = originalWindow;
    }
  });

  it('uses service role client when running server-side', async () => {
    const client = await (service as any).resolveWriteClient();
    expect(client).toBe(supabaseServiceMock);
  });

  it('uses authenticated client when browser session is available', async () => {
    (globalThis as Record<string, unknown>).window = {};
    supabaseAuthGetSession.mockResolvedValue({ data: { session: { user: { id: 'user-1' } } } });

    const client = await (service as any).resolveWriteClient();
    expect(client).toBe(supabaseMock);
  });

  it('writes readings with tempstick_api data source', async () => {
    const writeClient = createWriteClient();
    (service as any).resolveSyncContext = vi.fn().mockResolvedValue({
      writeClient: writeClient.client,
      userId: 'sync-user',
    });
    const timestamp = new Date().toISOString();

    // @ts-expect-error accessing test double
    service.apiClient.getLatestReadings.mockResolvedValue([
      { temperature: 4.5, humidity: 70, timestamp },
    ]);

    const result = await (service as any).syncTemperatureReadingsForSensor({
      externalSensorId: 'TS-1',
      internalSensorId: 'sensor-internal-1',
      sensorName: 'Line Cooler',
    });

    expect(result.newReadings).toBe(1);
    expect(writeClient.upsertSpy).toHaveBeenCalledTimes(1);
    const payload = writeClient.upsertSpy.mock.calls[0][0];
    expect(payload.data_source).toBe('tempstick_api');
    expect(payload.recorded_at).toBe(timestamp);
    expect(payload.user_id).toBe('sync-user');
    expect(payload.temp_fahrenheit).toBeCloseTo((4.5 * 9) / 5 + 32, 6);
  });

  it('handles constraint violations without crashing', async () => {
    const writeClient = createWriteClient({
      response: Promise.resolve({
        data: null,
        error: { code: '23514', details: 'data_source violation', message: 'invalid data_source' },
      }),
    });
    (service as any).resolveSyncContext = vi.fn().mockResolvedValue({
      writeClient: writeClient.client,
      userId: 'sync-user',
    });

    // @ts-expect-error accessing test double
    service.apiClient.getLatestReadings.mockResolvedValue([
      { temperature: 3.1, humidity: 66, timestamp: new Date().toISOString() },
    ]);

    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    const result = await (service as any).syncTemperatureReadingsForSensor({
      externalSensorId: 'TS-2',
      internalSensorId: 'sensor-internal-2',
      sensorName: 'Prep Freezer',
    });

    expect(result.newReadings).toBe(0);
    expect(consoleSpy).toHaveBeenCalledWith(
      expect.stringContaining('data_source constraint')
    );

    consoleSpy.mockRestore();
  });

  it('logs RLS rejections and skips blocked readings', async () => {
    const writeClient = createWriteClient({
      response: Promise.resolve({
        data: null,
        error: { code: '42501', message: 'permission denied' },
      }),
    });
    (service as any).resolveSyncContext = vi.fn().mockResolvedValue({
      writeClient: writeClient.client,
      userId: 'sync-user',
    });

    // @ts-expect-error accessing test double
    service.apiClient.getLatestReadings.mockResolvedValue([
      { temperature: 2.9, humidity: 54, timestamp: new Date().toISOString() },
    ]);

    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    const result = await (service as any).syncTemperatureReadingsForSensor({
      externalSensorId: 'TS-3',
      internalSensorId: 'sensor-internal-3',
      sensorName: 'Walk-in Freezer',
    });

    expect(result.newReadings).toBe(0);
    expect(consoleSpy).toHaveBeenCalledWith(
      expect.stringContaining('TempStick reading rejected by RLS')
    );

    consoleSpy.mockRestore();
  });

  it('treats upsert results with empty data as duplicates', async () => {
    const writeClient = createWriteClient({
      response: Promise.resolve({ data: [], error: null }),
    });
    (service as any).resolveSyncContext = vi.fn().mockResolvedValue({
      writeClient: writeClient.client,
      userId: 'sync-user',
    });

    // @ts-expect-error accessing test double
    service.apiClient.getLatestReadings.mockResolvedValue([
      { temperature: 1.2, humidity: 40, timestamp: new Date().toISOString() },
    ]);

    const result = await (service as any).syncTemperatureReadingsForSensor({
      externalSensorId: 'TS-4',
      internalSensorId: 'sensor-internal-4',
      sensorName: 'Dry Storage',
    });

    expect(result.newReadings).toBe(0);
    expect(writeClient.upsertSpy).toHaveBeenCalled();
  });

  it('preserves timezone information in recorded timestamps', async () => {
    const writeClient = createWriteClient();
    (service as any).resolveSyncContext = vi.fn().mockResolvedValue({
      writeClient: writeClient.client,
      userId: 'sync-user',
    });
    const timestamp = '2024-11-01T05:00:00-07:00';

    // @ts-expect-error accessing test double
    service.apiClient.getLatestReadings.mockResolvedValue([
      { temperature: 6, humidity: 55, timestamp },
    ]);

    await (service as any).syncTemperatureReadingsForSensor({
      externalSensorId: 'TS-5',
      internalSensorId: 'sensor-internal-5',
      sensorName: 'Seafood Case',
    });

    const payload = writeClient.upsertSpy.mock.calls[0][0];
    expect(new Date(payload.recorded_at).toISOString()).toBe(new Date(timestamp).toISOString());
  });

  it('executes full sync pipeline end-to-end with service role writes', async () => {
    const syncClient = createSyncPipelineClient();
    (service as any).resolveSyncContext = vi.fn().mockResolvedValue({
      writeClient: syncClient.client,
      userId: 'sync-user',
    });

    // @ts-expect-error accessing test double
    service.apiClient.getSensors.mockResolvedValue([
      { sensor_id: 'S-1', id: 'T-1', sensor_name: 'Main Fridge', offline: '0', battery_pct: 85 },
    ]);

    const iso = new Date().toISOString();
    // @ts-expect-error accessing test double
    service.apiClient.getLatestReadings.mockResolvedValue([
      { temperature: -4, humidity: 62, timestamp: iso },
    ]);

    const result = await service.syncSensors();

    expect(result.success).toBe(true);
    expect(result.syncedSensors).toBe(1);
    expect(result.newReadings).toBe(1);
    expect(syncClient.sensors.upsert).toHaveBeenCalledTimes(1);
    expect(syncClient.readings.upsert).toHaveBeenCalledTimes(1);
    expect(syncClient.sensors.lastPayload?.user_id).toBe('sync-user');
    expect(syncClient.readings.lastPayload?.user_id).toBe('sync-user');
    expect(syncClient.readings.lastPayload?.data_source).toBe('tempstick_api');
  });

  it('handles bulk reading ingestion within a reasonable duration', async () => {
    const writeClient = createWriteClient({
      response: Promise.resolve({
        data: Array.from({ length: 75 }, (_, idx) => ({
          id: `reading-${idx}`,
          recorded_at: new Date(Date.now() - idx * 1000).toISOString(),
        })),
        error: null,
      }),
    });
    (service as any).resolveSyncContext = vi.fn().mockResolvedValue({
      writeClient: writeClient.client,
      userId: 'sync-user',
    });

    const readings = Array.from({ length: 75 }, (_, idx) => ({
      temperature: 1 + idx * 0.1,
      humidity: 45 + idx,
      timestamp: new Date(Date.now() - idx * 60_000).toISOString(),
    }));

    // @ts-expect-error accessing test double
    service.apiClient.getLatestReadings.mockResolvedValue(readings);

    const start = performance.now();
    await (service as any).syncTemperatureReadingsForSensor({
      externalSensorId: 'TS-6',
      internalSensorId: 'sensor-internal-6',
      sensorName: 'Bulk Cooler',
    });
    const duration = performance.now() - start;

    expect(duration).toBeLessThan(200);
    expect(writeClient.upsertSpy).toHaveBeenCalledTimes(readings.length);
  });
});

function createWriteClient(options?: { response?: Promise<{ data: unknown; error: unknown }> }) {
  const upsertSpy = vi.fn().mockImplementation(() => options?.response ?? Promise.resolve({
    data: [{ id: 'reading-1', recorded_at: new Date().toISOString() }],
    error: null,
  }));

  const client = {
    from: vi.fn(() => ({
      upsert: upsertSpy,
    })),
  } as const;

  return { client, upsertSpy };
}

type SyncPipelineClient = {
  client: {
    from: (table: string) => any;
  };
  sensors: {
    upsert: ReturnType<typeof vi.fn>;
  };
  readings: {
    upsert: ReturnType<typeof vi.fn>;
  };
};

function createSyncPipelineClient(): SyncPipelineClient {
  const sensorsTable = {
    lastPayload: undefined as Record<string, unknown> | undefined,
    upsert: vi.fn((payload: Record<string, unknown>) => {
      sensorsTable.lastPayload = payload;
      return {
        select: vi.fn(() => ({
          single: vi.fn().mockResolvedValue({ data: { id: 'internal-1', sensor_id: 'S-1' }, error: null }),
        })),
      };
    }),
  };

  const readingsTable = {
    lastPayload: undefined as Record<string, unknown> | undefined,
    upsert: vi.fn((payload: Record<string, unknown>) => {
      readingsTable.lastPayload = payload;
      return Promise.resolve({
        data: [{ id: 'reading-1', recorded_at: new Date().toISOString() }],
        error: null,
      });
    }),
  };

  const client = {
    from: vi.fn((table: string) => {
      if (table === 'sensors') return sensorsTable;
      if (table === 'temperature_readings') return readingsTable;
      return {
        upsert: vi.fn(() => Promise.resolve({ data: null, error: null })),
      };
    }),
  };

  return { client, sensors: sensorsTable, readings: readingsTable };
}
