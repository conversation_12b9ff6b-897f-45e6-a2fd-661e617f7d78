import { renderHook, act, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import useLotLocationData from '@/hooks/useLotLocationData';
import { supabase } from '@/lib/supabase';
import type { AgingThresholds, LotLocationFilters } from '@/types/tempstick';

vi.mock('@/lib/supabase', () => {
  const from = vi.fn();
  const channel = vi.fn();
  const removeChannel = vi.fn();

  return {
    supabase: {
      from,
      channel,
      removeChannel,
    },
  };
});

const mockSupabase = vi.mocked(supabase);

const mockRealtimeChannel = () => ({
  on: vi.fn().mockReturnThis(),
  subscribe: vi.fn().mockReturnValue({ unsubscribe: vi.fn() }),
});

describe('useLotLocationData hook', () => {
  const mockThresholds: AgingThresholds = { warningDays: 150, criticalDays: 180 };

  const now = Date.now();
  const inventoryEvents = [
    {
      id: 'event-1',
      event_type: 'receiving',
      name: 'Halibut Fillets',
      quantity: 12,
      unit: 'lbs',
      metadata: {
        lot_number: 'LOT-001',
        lot_id: 'lot-1',
        product_name: 'Halibut Fillets',
        storage_area_id: 'storage-1',
        storage_area_name: 'Downstairs Freezer',
        sensor_internal_id: 'sensor-internal-1',
        expiration_date: new Date(now + 10 * 24 * 60 * 60 * 1000).toISOString(),
      },
      occurred_at: new Date(now - 160 * 24 * 60 * 60 * 1000).toISOString(),
      created_at: new Date(now - 160 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(now - 140 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'event-2',
      event_type: 'receiving',
      name: 'Salmon Roe',
      quantity: 4,
      unit: 'lbs',
      metadata: {
        lot_number: 'LOT-002',
        lot_id: 'lot-2',
        product_name: 'Salmon Roe',
        storage_area_id: 'storage-2',
        storage_area_name: 'Prep Cooler',
      },
      occurred_at: new Date(now - 30 * 24 * 60 * 60 * 1000).toISOString(),
      created_at: new Date(now - 30 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(now - 5 * 24 * 60 * 60 * 1000).toISOString(),
    },
  ];

  const storageAreas = [
    { id: 'storage-1', name: 'Downstairs Freezer', area_type: 'walk_in_freezer', level: null, location: null, temp_min_celsius: null, temp_max_celsius: null, temp_min_fahrenheit: null, temp_max_fahrenheit: null, temp_unit: 'fahrenheit' },
    { id: 'storage-2', name: 'Prep Cooler', area_type: 'reach_in_cooler', level: null, location: null, temp_min_celsius: null, temp_max_celsius: null, temp_min_fahrenheit: null, temp_max_fahrenheit: null, temp_unit: 'fahrenheit' },
  ];

  const sensors = [
    { id: 'sensor-internal-1', sensor_id: 'sensor-1', name: 'Freezer Sensor', storage_area_id: 'storage-1' },
  ];

  const createInventoryEventsBuilder = () => {
    const limit = vi.fn().mockResolvedValue({ data: inventoryEvents, error: null });
    const order = vi.fn().mockReturnValue({ limit });
    const select = vi.fn().mockReturnValue({ order, limit });
    return { select } as unknown as ReturnType<typeof supabase.from>;
  };

  const createStorageAreasBuilder = () => {
    const inMethod = vi.fn().mockResolvedValue({ data: storageAreas, error: null });
    const select = vi.fn().mockReturnValue({ in: inMethod });
    return { select } as unknown as ReturnType<typeof supabase.from>;
  };

  const createSensorsBuilder = () => {
    const inMethod = vi.fn().mockResolvedValue({ data: sensors, error: null });
    const select = vi.fn().mockReturnValue({ in: inMethod });
    return { select } as unknown as ReturnType<typeof supabase.from>;
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockSupabase.channel.mockReturnValue(mockRealtimeChannel() as any);
    mockSupabase.from.mockImplementation((table: string) => {
      switch (table) {
        case 'inventory_events':
          return createInventoryEventsBuilder();
        case 'storage_areas':
          return createStorageAreasBuilder();
        case 'sensors':
          return createSensorsBuilder();
        default:
          throw new Error(`Unexpected table requested in test: ${table}`);
      }
    });
  });

  it('maps Supabase responses into lot location data with aging details', async () => {
    const { result } = renderHook(() => useLotLocationData({}, mockThresholds));

    await waitFor(() => expect(result.current.loading).toBe(false));

    expect(result.current.lots).toHaveLength(2);
    const agingLot = result.current.lots.find((lot) => lot.lotId === 'lot-1');
    expect(agingLot).toMatchObject({
      lotNumber: 'LOT-001',
      storageAreaName: 'Downstairs Freezer',
      ageStatus: 'warning',
      isApproachingExpiry: true,
    });
  });

  it('filters lots by storage area and thresholds', async () => {
    const { result } = renderHook(() => useLotLocationData({}, mockThresholds));
    await waitFor(() => expect(result.current.loading).toBe(false));

    act(() => {
      const filters: LotLocationFilters = { storageAreaIds: ['storage-2'] };
      result.current.setFilters(filters);
    });

    await waitFor(() => {
      expect(result.current.lots).toHaveLength(1);
      expect(result.current.lots[0]).toMatchObject({ lotId: 'lot-2' });
    });
  });

  it('returns approaching expiry lots via helper', async () => {
    const { result } = renderHook(() => useLotLocationData({}, mockThresholds));
    await waitFor(() => expect(result.current.loading).toBe(false));

    const alerts = result.current.getLotsApproachingExpiry();
    expect(alerts).toHaveLength(1);
    expect(alerts[0].lotId).toBe('lot-1');
  });
});
