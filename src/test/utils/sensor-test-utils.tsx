/**
 * Sensor Testing Utilities
 *
 * Specialized testing utilities for TempStick sensor dashboard components,
 * including mock data generation, theme testing, and real-time update simulation.
 */

import React, { ReactNode } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { vi } from 'vitest';
import { ThemeProvider } from '../../contexts/ThemeContext';
import type {
  Sensor,
  SensorStatus,
  TemperatureReading,
  TemperatureAlert,
  DashboardSummary,
  TemperatureTrendData,
} from '../../types/tempstick';

// Mock theme context with controllable theme state
export const createMockThemeContext = (isDark = false) => ({
  theme: isDark ? 'dark' : ('light' as const),
  resolvedTheme: isDark ? 'dark' : ('light' as const),
  setTheme: vi.fn(),
  toggleTheme: vi.fn(),
});

// Enhanced test wrapper with theme provider
interface SensorTestWrapperProps {
  children: ReactNode;
  theme?: 'light' | 'dark';
  mockThemeContext?: any;
}

const SensorTestWrapper: React.FC<SensorTestWrapperProps> = ({
  children,
  theme = 'light',
  mockThemeContext,
}) => {
  if (mockThemeContext) {
    // Use mock context for precise control
    const MockThemeProvider = ({ children }: { children: ReactNode }) => (
      <div data-theme={mockThemeContext.resolvedTheme}>{children}</div>
    );
    return <MockThemeProvider>{children}</MockThemeProvider>;
  }

  return (
    <ThemeProvider defaultTheme={theme}>
      <div data-testid="sensor-test-wrapper" className={theme}>
        {children}
      </div>
    </ThemeProvider>
  );
};

// Custom render for sensor components with theme support
interface SensorRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  theme?: 'light' | 'dark';
  mockThemeContext?: any;
}

export const renderWithTheme = (ui: React.ReactElement, options: SensorRenderOptions = {}) => {
  const { theme, mockThemeContext, ...renderOptions } = options;

  const Wrapper: React.FC<{ children: ReactNode }> = ({ children }) => (
    <SensorTestWrapper theme={theme} mockThemeContext={mockThemeContext}>
      {children}
    </SensorTestWrapper>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// Test data generators for sensor components
export const generateMockSensor = (overrides: Partial<Sensor> = {}): Sensor => ({
  id: `sensor-${Date.now()}`,
  name: 'Test Sensor',
  location: 'Test Location',
  tempstick_id: 'ts-123',
  temp_min_threshold: 32,
  temp_max_threshold: 40,
  humidity_min_threshold: 30,
  humidity_max_threshold: 70,
  storage_area_id: 'area-1',
  alert_email: '<EMAIL>',
  is_active: true,
  last_reading_at: new Date().toISOString(),
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  storage_areas: {
    id: 'area-1',
    name: 'Cold Storage',
    area_type: 'refrigerated',
    required_temp_min: 32,
    required_temp_max: 38,
    haccp_control_point: true,
  },
  ...overrides,
});

export const generateMockTemperatureReading = (
  overrides: Partial<TemperatureReading> = {}
): TemperatureReading => ({
  id: `reading-${Date.now()}`,
  sensor_id: 'sensor-1',
  temperature: 35.5,
  humidity: 65.2,
  battery_level: 85,
  signal_strength: 75,
  reading_timestamp: new Date().toISOString(),
  alert_triggered: false,
  created_at: new Date().toISOString(),
  ...overrides,
});

export const generateMockSensorStatus = (overrides: Partial<SensorStatus> = {}): SensorStatus => {
  const sensor = generateMockSensor();
  const latestReading = generateMockTemperatureReading({ sensor_id: sensor.id });

  return {
    sensor,
    latestReading,
    status: 'online',
    activeAlerts: [],
    ...overrides,
  };
};

export const generateMockTemperatureAlert = (
  overrides: Partial<TemperatureAlert> = {}
): TemperatureAlert => ({
  id: `alert-${Date.now()}`,
  sensor_id: 'sensor-1',
  alert_type: 'temperature_high',
  severity: 'high',
  threshold_value: 40,
  actual_value: 45.2,
  message: 'Temperature exceeded maximum threshold',
  resolved_at: null,
  acknowledged_at: null,
  created_at: new Date().toISOString(),
  sensors: generateMockSensor(),
  ...overrides,
});

export const generateMockDashboardSummary = (
  overrides: Partial<DashboardSummary> = {}
): DashboardSummary => ({
  totalSensors: 5,
  onlineSensors: 4,
  offlineSensors: 1,
  activeAlerts: 2,
  criticalAlerts: 1,
  averageTemperature: 36.8,
  temperatureRange: { min: 32.1, max: 39.2 },
  averageHumidity: 62.5,
  sensorsWithIssues: 1,
  ...overrides,
});

// Mock trend data with realistic temperature variations
export const generateMockTrendData = (
  hours: number = 24,
  sensorIds: string[] = ['sensor-1']
): TemperatureTrendData[] => {
  const data: TemperatureTrendData[] = [];
  const now = new Date();

  for (let i = hours; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);

    sensorIds.forEach((sensorId, index) => {
      // Generate realistic temperature variations
      const baseTemp = 35 + index * 2; // Different base temps per sensor
      const variation = Math.sin(i * 0.1) * 2 + Math.random() * 0.5;
      const temperature = baseTemp + variation;

      const baseHumidity = 60 + index * 5;
      const humidityVariation = Math.cos(i * 0.15) * 5 + Math.random() * 2;
      const humidity = baseHumidity + humidityVariation;

      data.push({
        timestamp: timestamp.toISOString(),
        sensor_id: sensorId,
        sensor_name: `Sensor ${index + 1}`,
        temperature: Math.round(temperature * 10) / 10,
        humidity: Math.round(humidity * 10) / 10,
        alert_triggered: temperature > 40 ?? temperature < 32,
      });
    });
  }

  return data;
};

// Mock real-time subscription
export const createMockRealtimeSubscription = () => {
  const callbacks: Array<(payload: any) => void> = [];

  return {
    subscribe: vi.fn((callback) => {
      callbacks.push(callback);
      return {
        unsubscribe: vi.fn(() => {
          const index = callbacks.indexOf(callback);
          if (index > -1) callbacks.splice(index, 1);
        }),
      };
    }),
    emit: (payload: any) => {
      callbacks.forEach((callback) => callback(payload));
    },
    getCallbackCount: () => callbacks.length,
  };
};

// Mock TempStick service
export const createMockTempStickService = () => ({
  syncSensorData: vi.fn().mockResolvedValue({
    success: true,
    synced: 5,
    updated: 3,
    errors: [],
  }),
  getSensorStatus: vi.fn().mockResolvedValue({
    online: 4,
    offline: 1,
    warning: 0,
    critical: 1,
  }),
  getTemperatureReadings: vi.fn().mockResolvedValue([]),
  resolveAlert: vi.fn().mockResolvedValue({ success: true }),
  acknowledgeAlert: vi.fn().mockResolvedValue({ success: true }),
});

// Mock hooks for sensor components
export const createMockSensorHooks = () => ({
  useSensorStatuses: vi.fn(() => ({
    sensorStatuses: [generateMockSensorStatus()],
    summary: generateMockDashboardSummary(),
    loading: false,
    error: null,
    refetch: vi.fn(),
  })),
  useTemperatureAlerts: vi.fn(() => ({
    alerts: [generateMockTemperatureAlert()],
    loading: false,
    error: null,
    refetch: vi.fn(),
  })),
  useTemperatureSync: vi.fn(() => ({
    sync: vi.fn(),
    syncing: false,
    lastSyncTime: new Date(),
    error: null,
  })),
  useSensors: vi.fn(() => ({
    sensors: [generateMockSensor()],
    loading: false,
    error: null,
    refetch: vi.fn(),
  })),
  useTemperatureTrends: vi.fn(() => ({
    data: generateMockTrendData(),
    loading: false,
    error: null,
    refetch: vi.fn(),
  })),
});

// Performance testing utilities
export const measureRenderTime = async (renderFn: () => void) => {
  const startTime = performance.now();
  renderFn();
  const endTime = performance.now();
  return endTime - startTime;
};

export const generateLargeSensorDataset = (count: number) => {
  const sensors: SensorStatus[] = [];
  for (let i = 0; i < count; i++) {
    sensors.push(
      generateMockSensorStatus({
        sensor: generateMockSensor({
          id: `sensor-${i}`,
          name: `Sensor ${i + 1}`,
          location: `Location ${i + 1}`,
        }),
      })
    );
  }
  return sensors;
};

// Theme testing utilities
export const testBothThemes = (
  component: React.ReactElement,
  testFn: (container: HTMLElement, theme: 'light' | 'dark') => void
) => {
  // Test light theme
  const { container: lightContainer } = renderWithTheme(component, { theme: 'light' });
  testFn(lightContainer, 'light');

  // Test dark theme
  const { container: darkContainer } = renderWithTheme(component, { theme: 'dark' });
  testFn(darkContainer, 'dark');
};

// HACCP compliance testing
export const validateHACCPCompliance = {
  temperatureInRange: (temperature: number, min: number, max: number) =>
    temperature >= min && temperature <= max,
  alertSeverityCorrect: (temperature: number, thresholds: { min: number; max: number }) => {
    if (temperature < thresholds.min - 5 ?? temperature > thresholds.max + 5) return 'critical';
    if (temperature < thresholds.min ?? temperature > thresholds.max) return 'high';
    return 'normal';
  },
  requiresImmediateAction: (alert: TemperatureAlert) =>
    alert.severity === 'critical' && !alert.acknowledged_at,
};

// Export controls testing utilities
export const mockReportGeneration = {
  createMockPDFBuffer: () => new ArrayBuffer(1024),
  createMockDownloadUrl: () => 'blob:test-url',
  simulateReportProgress: (callback: (progress: number) => void) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      callback(progress);
      if (progress >= 100) {
        clearInterval(interval);
      }
    }, 100);
    return interval;
  },
};

// Accessibility testing helpers
export const accessibilityHelpers = {
  checkKeyboardNavigation: (container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button, input, select, textarea, a[href], [tabindex]:not([tabindex="-1"])'
    );
    return Array.from(focusableElements).every((el) => (el as HTMLElement).tabIndex >= 0);
  },
  checkAriaLabels: (container: HTMLElement) => {
    const interactiveElements = container.querySelectorAll('button, input, select');
    return Array.from(interactiveElements).every(
      (el) =>
        el.getAttribute('aria-label') ||
        el.getAttribute('aria-labelledby') ||
        (el as HTMLElement).innerText.trim().length > 0
    );
  },
};
