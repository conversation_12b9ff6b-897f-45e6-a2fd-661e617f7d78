import React, { ReactElement, ReactNode } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { vi } from 'vitest';
import { MemoryRouter } from 'react-router-dom';

// Mock Supabase client for testing
export const createMockSupabaseClient = () => {
  const mockClient = {
    auth: {
      getSession: vi.fn().mockResolvedValue({
        data: { session: null },
        error: null,
      }),
      getUser: vi.fn().mockResolvedValue({
        data: { user: null },
        error: null,
      }),
      signInWithPassword: vi.fn().mockResolvedValue({
        data: { user: null, session: null },
        error: null,
      }),
      signOut: vi.fn().mockResolvedValue({
        error: null,
      }),
      onAuthStateChange: vi.fn().mockReturnValue({
        data: { subscription: { unsubscribe: vi.fn() } },
      }),
    },
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({
        data: null,
        error: null,
      }),
      then: vi.fn().mockResolvedValue({
        data: [],
        error: null,
      }),
    })),
    storage: {
      from: vi.fn(() => ({
        upload: vi.fn().mockResolvedValue({
          data: { path: 'test-path' },
          error: null,
        }),
        createSignedUrl: vi.fn().mockResolvedValue({
          data: { signedUrl: 'test-url' },
          error: null,
        }),
      })),
    },
    rpc: vi.fn().mockResolvedValue({
      data: null,
      error: null,
    }),
  };

  return mockClient as any;
};

// Enhanced test wrapper component with providers
interface TestWrapperProps {
  children: ReactNode;
  initialEntries?: string[];
  supabaseClient?: any;
  authUser?: any;
  enableRouter?: boolean;
}

const TestWrapper: React.FC<TestWrapperProps> = ({
  children,
  initialEntries = ['/'],
  supabaseClient,
  authUser,
  enableRouter = true,
}) => {
  const mockClient = supabaseClient ?? createMockSupabaseClient();

  // Set auth user if provided
  if (authUser && mockClient.setConfig) {
    mockClient.setConfig({ authUser });
  }

  const content = <div data-testid="test-wrapper">{children}</div>;

  if (enableRouter) {
    return <MemoryRouter initialEntries={initialEntries}>{content}</MemoryRouter>;
  }

  return content;
};

// Enhanced render options
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[];
  supabaseClient?: any;
  authUser?: any;
  enableRouter?: boolean;
}

// Custom render function with enhanced providers
const customRender = (ui: ReactElement, options: CustomRenderOptions = {}) => {
  const { initialEntries, supabaseClient, authUser, enableRouter, ...renderOptions } = options;

  const Wrapper: React.FC<{ children: ReactNode }> = ({ children }) => (
    <TestWrapper
      initialEntries={initialEntries}
      supabaseClient={supabaseClient}
      authUser={authUser}
      enableRouter={enableRouter}
    >
      {children}
    </TestWrapper>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// Re-export everything
export * from '@testing-library/react';
export { customRender as render };

// Custom matchers for seafood-specific testing
export const seafoodMatchers = {
  toBeValidBatchNumber: (received: string) => {
    const batchNumberPattern = /^[A-Z]{2,4}-\d{3}-\d{4}$/;
    const pass = batchNumberPattern.test(received);

    return {
      message: () =>
        `expected ${received} ${pass ? 'not ' : ''}to be a valid batch number (format: XXX-000-0000)`,
      pass,
    };
  },

  toBeValidTemperature: (received: number, unit: 'C' | 'F' = 'F') => {
    let pass = false;

    if (unit === 'F') {
      pass = received >= 32 && received <= 40; // Safe seafood storage range in Fahrenheit
    } else {
      pass = received >= 0 && received <= 4; // Safe seafood storage range in Celsius
    }

    return {
      message: () =>
        `expected ${received}°${unit} ${pass ? 'not ' : ''}to be within safe seafood storage temperature range`,
      pass,
    };
  },

  toBeValidSeafoodSpecies: (received: string) => {
    // Common seafood species scientific names
    const validSpecies = [
      'Salmo salar', // Atlantic Salmon
      'Oncorhynchus', // Pacific Salmon genus
      'Gadus morhua', // Atlantic Cod
      'Hippoglossus stenolepis', // Pacific Halibut
      'Cancer magister', // Dungeness Crab
      'Pandalus borealis', // Northern Shrimp
      'Thunnus', // Tuna genus
    ];

    const pass = validSpecies.some((species) => received.includes(species));

    return {
      message: () => `expected ${received} ${pass ? 'not ' : ''}to be a valid seafood species name`,
      pass,
    };
  },
};

// Mock voice recognition events
export const createMockSpeechRecognitionEvent = (transcript: string, confidence = 0.9) => ({
  results: [
    [
      {
        transcript,
        confidence,
        isFinal: true,
      },
    ],
  ],
  resultIndex: 0,
});

// Mock file upload helpers
export const createMockFile = (name: string, content: string, type = 'text/csv') => {
  const file = new File([content], name, { type });
  return file;
};

// Mock OpenAI response helper
export const createMockOpenAIResponse = (content: string) => ({
  choices: [
    {
      message: {
        content,
        role: 'assistant',
      },
      finish_reason: 'stop',
    },
  ],
  usage: {
    prompt_tokens: 100,
    completion_tokens: 50,
    total_tokens: 150,
  },
});

// Wait for async operations in tests
export const waitForNextTick = () => new Promise((resolve) => setTimeout(resolve, 0));

// Wait for loading to finish - using dynamic import to avoid unused import warning
export const waitForLoadingToFinish = async () => {
  const { waitFor } = await import('@testing-library/react');
  await waitFor(() => {
    expect(document.querySelector('[data-testid="loading"]')).not.toBeInTheDocument();
  });
};

// Mock localStorage for testing
export const mockLocalStorage = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: vi.fn((key: string) => store[key] ?? null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value.toString();
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key];
    }),
    clear: vi.fn(() => {
      store = {};
    }),
  };
})();

// Mock performance API for performance testing
export const mockPerformance = {
  now: vi.fn(() => Date.now()),
  mark: vi.fn(),
  measure: vi.fn(),
  getEntriesByType: vi.fn(() => []),
  clearMarks: vi.fn(),
  clearMeasures: vi.fn(),
};

// Test data generators
export const generateTestProduct = (overrides = {}) => ({
  id: `test-prod-${Date.now()}`,
  name: 'Test Product',
  category_id: 'cat-test',
  species: 'Test species',
  origin: 'Test Origin',
  sustainability_rating: 'A',
  storage_requirements: 'Keep refrigerated',
  allergens: ['Fish'],
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
});

export const generateTestInventoryEvent = (overrides = {}) => ({
  id: `test-event-${Date.now()}`,
  event_type: 'receiving',
  product_id: 'test-prod-1',
  quantity: 100,
  unit: 'lbs',
  batch_number: 'TEST-001-2024',
  created_at: new Date().toISOString(),
  ...overrides,
});

// This function was moved above to avoid duplicate definition

// Mock intersection observer for components that use it
export const mockIntersectionObserver = () => {
  const mockIntersectionObserver = vi.fn();
  mockIntersectionObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  });
  window.IntersectionObserver = mockIntersectionObserver;
};
