/**
 * Enhanced OpenAI API mocking utilities for voice processing tests
 * Provides realistic responses for transcription and seafood domain processing
 */

import { vi } from 'vitest';

// OpenAI API Response Types
export interface OpenAITranscriptionResponse {
  text: string;
  task?: string;
  language?: string;
  duration?: number;
  segments?: Array<{
    id: number;
    seek: number;
    start: number;
    end: number;
    text: string;
    tokens: number[];
    temperature: number;
    avg_logprob: number;
    compression_ratio: number;
    no_speech_prob: number;
  }>;
}

export interface OpenAIChatResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Voice Command Structure (matching our application)
export interface VoiceCommand {
  action_type: 'create_event' | 'update_event' | 'query_inventory' | 'get_help';
  event_type?: 'receiving' | 'sale' | 'disposal' | 'production' | 'adjustment';
  product_name?: string;
  quantity?: number;
  unit?: string;
  vendor_name?: string;
  customer_name?: string;
  batch_number?: string;
  notes?: string;
  confidence_score: number;
  confidence_breakdown?: {
    product_match: number;
    quantity_extraction: number;
    vendor_match?: number;
    customer_match?: number;
    event_type: number;
    overall: number;
  };
  raw_transcript: string;
  processing_metadata?: {
    model_used: string;
    processing_time_ms: number;
    prompt_tokens: number;
    completion_tokens: number;
  };
}

interface MockScenario {
  transcript: string;
  command: VoiceCommand;
}

interface MockOpenAIConfig {
  shouldFail: boolean;
  failureType: string;
  latencyMs: number;
  responseQuality: 'high' | 'medium' | 'low';
  modelBehavior: 'consistent' | 'variable';
}

// Seafood-specific test data
export const SEAFOOD_VOCABULARY = {
  fish: [
    'salmon',
    'cod',
    'halibut',
    'tuna',
    'trout',
    'bass',
    'snapper',
    'mahi mahi',
    'swordfish',
    'rockfish',
    'sole',
    'flounder',
    'pollock',
    'haddock',
  ],
  shellfish: [
    'crab',
    'lobster',
    'shrimp',
    'prawns',
    'scallops',
    'dungeness crab',
    'king crab',
    'snow crab',
    'blue crab',
    'langostino',
  ],
  mollusks: ['oysters', 'clams', 'mussels', 'abalone', 'squid', 'octopus', 'geoduck'],
  units: [
    'pounds',
    'lbs',
    'kilograms',
    'kg',
    'ounces',
    'oz',
    'pieces',
    'each',
    'dozen',
    'cases',
    'boxes',
    'tons',
  ],
  vendors: [
    'Ocean Fresh Seafoods',
    'Pacific Catch',
    'Northwest Seafood',
    'Marine Harvest',
    'Trident Seafoods',
    'Icicle Seafoods',
    'Wild Planet',
    "Fisherman's Market",
    'Coastal Seafood',
    'Bay Point Seafood',
    'Seattle Fish Company',
  ],
  customers: [
    'Marina Restaurant',
    'The Fish House',
    'Ocean View Cafe',
    'Pier 70',
    "Morrison's",
    'Fresh Catch Market',
    'Harbor Restaurant',
    'Waterfront Bistro',
  ],
};

// Transcript patterns for different confidence levels
export const TRANSCRIPT_PATTERNS = {
  high_confidence: [
    'Received {quantity} {unit} of {product} from {vendor}',
    'Sold {quantity} {unit} of {product} to {customer}',
    'Disposed of {quantity} {unit} of {product} due to spoilage',
    'Adjustment of {quantity} {unit} {product} inventory',
  ],
  medium_confidence: [
    'Got {quantity} {product} from {vendor}',
    'Delivered {quantity} {unit} {product} to {customer}',
    '{quantity} {unit} of {product} went bad',
    'Fixed inventory for {product}',
  ],
  low_confidence: [
    'Something about {product}',
    'Maybe {quantity} from somewhere',
    "Can't hear clearly... {product}... {vendor}",
    'Bad audio quality... received... {product}',
  ],
};

export class MockOpenAIClient {
  private config = {
    shouldFail: false,
    failureType: 'generic',
    latencyMs: 500,
    responseQuality: 'high' as 'high' | 'medium' | 'low',
    modelBehavior: 'consistent' as 'consistent' | 'variable',
  };

  private requestCount = 0;
  private totalTokensUsed = 0;
  private scenarioQueue: MockScenario[] = [];
  private activeScenario: MockScenario | null = null;
  private sequenceCounter = 0;

  constructor(config: Partial<typeof MockOpenAIClient.prototype.config> = {}) {
    this.config = { ...this.config, ...config };
  }

  // Configuration methods
  setConfig(config: Partial<typeof MockOpenAIClient.prototype.config>): void {
    this.config = { ...this.config, ...config };
  }

  setShouldFail(fail: boolean, type: string = 'generic'): void {
    this.config.shouldFail = fail;
    this.config.failureType = type;
  }

  setLatency(ms: number): void {
    this.config.latencyMs = ms;
  }

  setResponseQuality(quality: 'high' | 'medium' | 'low'): void {
    this.config.responseQuality = quality;
  }

  enqueueScenario(scenario: MockScenario): void {
    this.scenarioQueue.push(scenario);
  }

  clearScenarios(): void {
    this.scenarioQueue = [];
    this.activeScenario = null;
  }

  // Simulate network latency
  private async simulateLatency(): Promise<void> {
    if (this.config.latencyMs > 0) {
      await new Promise((resolve) => setTimeout(resolve, this.config.latencyMs));
    }
  }

  // Create error responses
  private createError(type: string): Error {
    switch (type) {
      case 'timeout':
        return new Error('Request timeout: The server took too long to respond');
      case 'rate_limit':
        return new Error('Rate limit exceeded. Please try again later');
      case 'auth':
        return new Error('Authentication failed. Invalid API key');
      case 'insufficient_quota':
        return new Error('Insufficient quota. Please check your OpenAI usage');
      case 'model_unavailable':
        return new Error('Model temporarily unavailable');
      case 'invalid_audio':
        return new Error('Invalid audio format or corrupted file');
      case 'network':
        return new Error('Connection refused');
      default:
        return new Error('OpenAI API error');
    }
  }

  // Audio transcription (Whisper API)
  async transcribeAudio(
    audioBlob: Blob | File,
    options: any = {}
  ): Promise<OpenAITranscriptionResponse> {
    this.requestCount++;
    await this.simulateLatency();

    if (this.config.shouldFail) {
      throw this.createError(this.config.failureType);
    }

    if (!this.activeScenario && this.scenarioQueue.length > 0) {
      this.activeScenario = this.scenarioQueue.shift() ?? null;
    }

    if (this.activeScenario) {
      const transcript = this.activeScenario.transcript;
      const duration = Math.max(1, audioBlob.size / 1000);
      return {
        text: transcript,
        task: 'transcribe',
        language: 'en',
        duration,
        segments: [
          {
            id: 0,
            seek: 0,
            start: 0,
            end: duration,
            text: transcript,
            tokens: this.generateMockTokens(transcript),
            temperature: 0,
            avg_logprob: -0.1,
            compression_ratio: 1.0,
            no_speech_prob: 0.05,
          },
        ],
      };
    }

    // Simulate different transcription qualities based on audio characteristics
    const audioSize = audioBlob.size;
    const duration = Math.max(1, audioSize / 1000); // Rough duration estimate

    let transcript: string;

    if (audioSize < 1000) {
      // Small/poor quality audio
      transcript = this.generateLowQualityTranscript();
    } else if (audioSize < 5000) {
      // Medium quality audio
      transcript = this.generateMediumQualityTranscript();
    } else {
      // High quality audio
      transcript = this.generateHighQualityTranscript();
    }

    return {
      text: transcript,
      task: 'transcribe',
      language: 'en',
      duration,
      segments: [
        {
          id: 0,
          seek: 0,
          start: 0.0,
          end: duration,
          text: transcript,
          tokens: this.generateMockTokens(transcript),
          temperature: 0.0,
          avg_logprob: -0.3,
          compression_ratio: 1.1,
          no_speech_prob: audioSize < 1000 ? 0.4 : audioSize < 5000 ? 0.2 : 0.1,
        },
      ],
    };
  }

  // Chat completion for processing transcripts into structured commands
  async processTranscript(transcript: string, context: any = {}): Promise<VoiceCommand> {
    this.requestCount++;
    await this.simulateLatency();

    if (this.config.shouldFail) {
      throw this.createError(this.config.failureType);
    }

    if (this.activeScenario && this.activeScenario.transcript === transcript) {
      const scenarioCommand = {
        ...this.activeScenario.command,
        raw_transcript: transcript,
      };
      this.activeScenario = null;
      return scenarioCommand;
    }

    const promptTokens = Math.ceil(transcript.length / 4) + 200; // Rough token estimate
    const completionTokens = 150;
    this.totalTokensUsed += promptTokens + completionTokens;

    // Parse transcript into structured command
    const command = this.parseTranscriptToCommand(transcript);

    // Add processing metadata
    command.processing_metadata = {
      model_used: 'gpt-4o-mini',
      processing_time_ms: this.config.latencyMs,
      prompt_tokens: promptTokens,
      completion_tokens: completionTokens,
    };

    return command;
  }

  // Generate realistic transcripts based on quality level
  private generateHighQualityTranscript(): string {
    const pattern = this.pickFromArray(TRANSCRIPT_PATTERNS.high_confidence);
    const product = this.getCycleProduct();
    const quantity = this.getNextQuantity(20, 30);
    const unit = this.pickFromArray(SEAFOOD_VOCABULARY.units.slice(0, 4));
    const vendor = this.pickFromArray(SEAFOOD_VOCABULARY.vendors);
    const customer = this.pickFromArray(SEAFOOD_VOCABULARY.customers);

    return pattern
      .replace('{product}', product)
      .replace('{quantity}', quantity.toString())
      .replace('{unit}', unit)
      .replace('{vendor}', vendor)
      .replace('{customer}', customer);
  }

  private generateMediumQualityTranscript(): string {
    const pattern = this.pickFromArray(TRANSCRIPT_PATTERNS.medium_confidence);
    const product = this.getCycleProduct();
    const quantity = this.getNextQuantity(10, 20);
    const unit = this.pickFromArray(SEAFOOD_VOCABULARY.units);
    const vendor = this.pickFromArray(SEAFOOD_VOCABULARY.vendors);
    const customer = this.pickFromArray(SEAFOOD_VOCABULARY.customers);

    return pattern
      .replace('{product}', product)
      .replace('{quantity}', quantity.toString())
      .replace('{unit}', unit)
      .replace('{vendor}', vendor)
      .replace('{customer}', customer);
  }

  private generateLowQualityTranscript(): string {
    const pattern = this.pickFromArray(TRANSCRIPT_PATTERNS.low_confidence);
    const product = this.getCycleProduct();
    const quantity = this.getNextQuantity(5, 15);
    const vendor = this.pickFromArray(SEAFOOD_VOCABULARY.vendors);

    return pattern
      .replace('{product}', product)
      .replace('{quantity}', quantity.toString())
      .replace('{vendor}', vendor);
  }

  private getCycleProduct(): string {
    const allProducts = [
      ...SEAFOOD_VOCABULARY.fish,
      ...SEAFOOD_VOCABULARY.shellfish,
      ...SEAFOOD_VOCABULARY.mollusks,
    ];
    const index = this.sequenceCounter % allProducts.length;
    this.sequenceCounter++;
    return allProducts[index];
  }

  private getNextQuantity(base: number, span: number): number {
    const quantity = base + (this.sequenceCounter % span);
    this.sequenceCounter++;
    return quantity;
  }

  private pickFromArray<T>(arr: T[]): T {
    if (arr.length === 0) {
      throw new Error('Cannot pick from empty array');
    }
    const value = arr[this.sequenceCounter % arr.length];
    this.sequenceCounter++;
    return value;
  }

  private generateMockTokens(text: string): number[] {
    const words = text.split(' ');
    return words.map((word, index) => 1000 + ((this.hashString(word) + index) % 50000));
  }

  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = (hash << 5) - hash + str.charCodeAt(i);
      hash |= 0;
    }
    return Math.abs(hash);
  }

  // Parse transcript into structured voice command
  private parseTranscriptToCommand(transcript: string): VoiceCommand {
    const lowerTranscript = transcript.toLowerCase();

    // Determine action and event type
    let action_type: VoiceCommand['action_type'] = 'create_event';
    let event_type: VoiceCommand['event_type'] = 'receiving';

    if (
      lowerTranscript.includes('sold') ||
      lowerTranscript.includes('delivered') ||
      lowerTranscript.includes('shipped')
    ) {
      event_type = 'sale';
    } else if (
      lowerTranscript.includes('disposed') ||
      lowerTranscript.includes('threw away') ||
      lowerTranscript.includes('spoiled')
    ) {
      event_type = 'disposal';
    } else if (
      lowerTranscript.includes('produced') ||
      lowerTranscript.includes('processed') ||
      lowerTranscript.includes('filleted')
    ) {
      event_type = 'production';
    } else if (
      lowerTranscript.includes('adjustment') ||
      lowerTranscript.includes('corrected') ||
      lowerTranscript.includes('fixed')
    ) {
      event_type = 'adjustment';
    }

    // Extract product name
    const product_name = this.extractProductName(lowerTranscript);

    // Extract quantity and unit
    const { quantity, unit } = this.extractQuantityAndUnit(lowerTranscript);

    // Extract vendor or customer
    const vendor_name = this.extractVendorName(lowerTranscript);
    const customer_name = this.extractCustomerName(lowerTranscript);

    // Calculate confidence scores
    const confidence_breakdown = this.calculateConfidenceBreakdown(
      transcript,
      product_name,
      quantity,
      unit,
      vendor_name,
      customer_name
    );

    return {
      action_type,
      event_type,
      product_name,
      quantity,
      unit,
      vendor_name,
      customer_name,
      confidence_score: confidence_breakdown.overall,
      confidence_breakdown,
      raw_transcript: transcript,
    };
  }

  private extractProductName(text: string): string {
    const allProducts = [
      ...SEAFOOD_VOCABULARY.fish,
      ...SEAFOOD_VOCABULARY.shellfish,
      ...SEAFOOD_VOCABULARY.mollusks,
    ];

    for (const product of allProducts) {
      if (text.includes(product.toLowerCase())) {
        return product;
      }
    }

    // Fuzzy matching for common variations
    if (text.includes('crab') && text.includes('dungeness')) return 'dungeness crab';
    if (text.includes('salmon') && text.includes('atlantic')) return 'atlantic salmon';
    if (text.includes('salmon') && text.includes('pacific')) return 'pacific salmon';

    return 'unknown';
  }

  private extractQuantityAndUnit(text: string): { quantity: number; unit: string } {
    // Extract numbers (including spelled out numbers)
    const numberWords: { [key: string]: number } = {
      one: 1,
      two: 2,
      three: 3,
      four: 4,
      five: 5,
      six: 6,
      seven: 7,
      eight: 8,
      nine: 9,
      ten: 10,
      eleven: 11,
      twelve: 12,
      fifteen: 15,
      twenty: 20,
      'twenty-five': 25,
      thirty: 30,
      fifty: 50,
      hundred: 100,
    };

    let quantity = 0;
    let unit = 'pounds';

    // Look for numeric quantities first
    const numberMatch = text.match(/(\d+(?:\.\d+)?)/);
    if (numberMatch) {
      quantity = parseFloat(numberMatch[1]);
    } else {
      // Look for spelled out numbers
      for (const [word, value] of Object.entries(numberWords)) {
        if (text.includes(word)) {
          quantity = value;
          break;
        }
      }
    }

    // Look for units
    for (const unitOption of SEAFOOD_VOCABULARY.units) {
      if (text.includes(unitOption.toLowerCase())) {
        unit = unitOption;
        break;
      }
    }

    // Special cases
    if (text.includes('dozen')) {
      if (quantity === 0) quantity = 12;
      unit = 'pieces';
    }

    return { quantity, unit };
  }

  private extractVendorName(text: string): string {
    for (const vendor of SEAFOOD_VOCABULARY.vendors) {
      const vendorLower = vendor.toLowerCase();
      if (text.includes(vendorLower)) {
        return vendor;
      }

      // Check for partial matches
      const vendorWords = vendorLower.split(' ');
      if (vendorWords.length > 1 && vendorWords.some((word) => text.includes(word))) {
        return vendor;
      }
    }

    // Look for common vendor patterns
    if (text.includes('from ')) {
      const fromIndex = text.indexOf('from ');
      const afterFrom = text.substring(fromIndex + 5).trim();
      const vendorEnd = afterFrom.search(/[\.,;]|$|due to|because/);
      if (vendorEnd > 0) {
        return afterFrom.substring(0, vendorEnd).trim();
      }
    }

    return '';
  }

  private extractCustomerName(text: string): string {
    for (const customer of SEAFOOD_VOCABULARY.customers) {
      const customerLower = customer.toLowerCase();
      if (text.includes(customerLower)) {
        return customer;
      }
    }

    // Look for common customer patterns
    if (text.includes('to ')) {
      const toIndex = text.indexOf('to ');
      const afterTo = text.substring(toIndex + 3).trim();
      const customerEnd = afterTo.search(/[\.,;]|$|for|because/);
      if (customerEnd > 0) {
        return afterTo.substring(0, customerEnd).trim();
      }
    }

    return '';
  }

  private calculateConfidenceBreakdown(
    transcript: string,
    product: string,
    quantity: number,
    unit: string,
    vendor: string,
    customer: string
  ) {
    const lowerTranscript = transcript.toLowerCase();
    const product_match = product && product !== 'unknown' ? 0.9 : 0.45;
    const quantity_extraction = quantity > 0 ? 0.85 : 0.35;
    const vendor_match = vendor ? 0.8 : 0.5;
    const customer_match = customer ? 0.8 : 0.5;
    const actionKeywords = ['receive', 'receiv', 'sold', 'sale', 'dispose', 'count', 'inventory'];
    const hasAction = actionKeywords.some((keyword) => lowerTranscript.includes(keyword));
    const event_type_score = hasAction ? 0.82 : 0.6;

    let overallRaw = (product_match + quantity_extraction + event_type_score) / 3;
    if (product === 'unknown') overallRaw -= 0.2;
    if (!quantity || quantity <= 0) overallRaw -= 0.15;
    const overall = Number(Math.max(0, overallRaw).toFixed(4));

    return {
      product_match,
      quantity_extraction,
      vendor_match,
      customer_match,
      event_type: event_type_score,
      overall,
    };
  }

  // Utility methods for testing
  getRequestCount(): number {
    return this.requestCount;
  }

  getTotalTokensUsed(): number {
    return this.totalTokensUsed;
  }

  reset(): void {
    this.requestCount = 0;
    this.totalTokensUsed = 0;
    this.config = {
      shouldFail: false,
      failureType: 'generic',
      latencyMs: 500,
      responseQuality: 'high',
      modelBehavior: 'consistent',
    };
    this.sequenceCounter = 0;
    this.scenarioQueue = [];
    this.activeScenario = null;
  }
}

// Factory function for creating mock OpenAI clients
export const createMockOpenAIClient = (config: any = {}): MockOpenAIClient => {
  return new MockOpenAIClient(config);
};

// Mock for OpenAI module
export const mockOpenAI = {
  OpenAI: vi.fn().mockImplementation((config: any) => {
    const mockClient = createMockOpenAIClient(config);

    return {
      audio: {
        transcriptions: {
          create: vi.fn().mockImplementation(async (params: any) => {
            return mockClient.transcribeAudio(params.file, params);
          }),
        },
      },
      chat: {
        completions: {
          create: vi.fn().mockImplementation(async (params: any) => {
            const userMessage = params.messages.find((m: any) => m.role === 'user');
            const transcript = userMessage?.content ?? '';
            const command = await mockClient.processTranscript(transcript);

            return {
              id: `chatcmpl-${Date.now()}`,
              object: 'chat.completion',
              created: Math.floor(Date.now() / 1000),
              model: params.model ?? 'gpt-4o-mini',
              choices: [
                {
                  index: 0,
                  message: {
                    role: 'assistant',
                    content: JSON.stringify(command),
                  },
                  finish_reason: 'stop',
                },
              ],
              usage: {
                prompt_tokens: command.processing_metadata?.prompt_tokens ?? 100,
                completion_tokens: command.processing_metadata?.completion_tokens ?? 150,
                total_tokens: command.processing_metadata
                  ? command.processing_metadata.prompt_tokens +
                    command.processing_metadata.completion_tokens
                  : 250,
              },
            };
          }),
        },
      },
    };
  }),
};

// Test scenario generators
export const generateVoiceTestScenarios = () => {
  return [
    {
      name: 'High confidence salmon receiving',
      audio: new Blob(['high quality audio'], { type: 'audio/webm' }),
      expectedTranscript: 'Received 25 pounds of fresh salmon from Ocean Fresh Seafoods',
      expectedCommand: {
        action_type: 'create_event',
        event_type: 'receiving',
        product_name: 'salmon',
        quantity: 25,
        unit: 'pounds',
        vendor_name: 'Ocean Fresh Seafoods',
        confidence_score: 0.9,
      },
    },
    {
      name: 'Medium confidence crab sale',
      audio: new Blob(['medium quality'], { type: 'audio/webm' }),
      expectedTranscript: 'Sold twelve dungeness crabs to Marina Restaurant',
      expectedCommand: {
        action_type: 'create_event',
        event_type: 'sale',
        product_name: 'dungeness crab',
        quantity: 12,
        unit: 'pieces',
        customer_name: 'Marina Restaurant',
        confidence_score: 0.75,
      },
    },
    {
      name: 'Low confidence background noise',
      audio: new Blob(['poor'], { type: 'audio/webm' }),
      expectedTranscript: 'Something about... tuna... from...',
      expectedCommand: {
        action_type: 'create_event',
        product_name: 'tuna',
        confidence_score: 0.4,
      },
    },
  ];
};
