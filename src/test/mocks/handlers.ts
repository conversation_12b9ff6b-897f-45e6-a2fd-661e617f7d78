import { http, HttpResponse } from 'msw';
import { mockSupabaseData } from './data';
import { vendorHandlers } from './vendor-handlers';

// Base Supabase URL for mocking
const SUPABASE_URL = 'https://test.supabase.co';

export const handlers = [
  // Supabase Auth handlers
  http.post(`${SUPABASE_URL}/auth/v1/token`, () => {
    return HttpResponse.json({
      access_token: 'mock-access-token',
      token_type: 'bearer',
      expires_in: 3600,
      refresh_token: 'mock-refresh-token',
      user: mockSupabaseData.users[0],
    });
  }),

  http.get(`${SUPABASE_URL}/auth/v1/user`, () => {
    return HttpResponse.json(mockSupabaseData.users[0]);
  }),

  // Supabase REST API handlers
  http.get(`${SUPABASE_URL}/rest/v1/products`, ({ request }) => {
    const url = new URL(request.url);
    const select = url.searchParams.get('select');

    if (select?.includes('categories')) {
      return HttpResponse.json(
        mockSupabaseData.products.map((product) => ({
          ...product,
          categories: mockSupabaseData.categories.find((cat) => cat.id === product.category_id),
        }))
      );
    }

    return HttpResponse.json(mockSupabaseData.products);
  }),

  http.post(`${SUPABASE_URL}/rest/v1/products`, async ({ request }) => {
    const body = await request.json();
    const newProduct = {
      ...body,
      id: `prod-${Date.now()}`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    return HttpResponse.json(newProduct, { status: 201 });
  }),

  http.get(`${SUPABASE_URL}/rest/v1/categories`, () => {
    return HttpResponse.json(mockSupabaseData.categories);
  }),

  http.get(`${SUPABASE_URL}/rest/v1/inventory_events`, ({ request }) => {
    const url = new URL(request.url);
    const productId = url.searchParams.get('product_id');

    let events = mockSupabaseData.inventoryEvents;
    if (productId) {
      events = events.filter((event) => event.product_id === productId);
    }

    return HttpResponse.json(events);
  }),

  http.post(`${SUPABASE_URL}/rest/v1/inventory_events`, async ({ request }) => {
    const body = await request.json();
    const newEvent = {
      ...body,
      id: `event-${Date.now()}`,
      created_at: new Date().toISOString(),
    };

    return HttpResponse.json(newEvent, { status: 201 });
  }),

  http.get(`${SUPABASE_URL}/rest/v1/haccp_events`, () => {
    return HttpResponse.json(mockSupabaseData.haccpEvents);
  }),

  http.post(`${SUPABASE_URL}/rest/v1/haccp_events`, async ({ request }) => {
    const body = await request.json();
    const newEvent = {
      ...body,
      id: `haccp-${Date.now()}`,
      created_at: new Date().toISOString(),
    };

    return HttpResponse.json(newEvent, { status: 201 });
  }),

  // OpenAI API handlers
  http.post('https://api.openai.com/v1/chat/completions', async ({ request }) => {
    const body = (await request.json()) as any;
    const messages = body.messages ?? [];
    const lastMessage = messages[messages.length - 1];

    // Mock different responses based on input
    let mockResponse = 'I processed your seafood inventory request.';

    if (lastMessage?.content?.toLowerCase().includes('salmon')) {
      mockResponse = JSON.stringify({
        action: 'add_inventory',
        product: 'Atlantic Salmon',
        quantity: 50,
        unit: 'lbs',
        location: 'Freezer A',
        batch_number: 'SAL-001-2024',
      });
    } else if (lastMessage?.content?.toLowerCase().includes('crab')) {
      mockResponse = JSON.stringify({
        action: 'add_inventory',
        product: 'Dungeness Crab',
        quantity: 25,
        unit: 'lbs',
        location: 'Fresh Section',
        batch_number: 'CRAB-002-2024',
      });
    }

    return HttpResponse.json({
      id: 'chatcmpl-mock',
      object: 'chat.completion',
      created: Date.now(),
      model: 'gpt-4',
      choices: [
        {
          index: 0,
          message: {
            role: 'assistant',
            content: mockResponse,
          },
          finish_reason: 'stop',
        },
      ],
      usage: {
        prompt_tokens: 100,
        completion_tokens: 50,
        total_tokens: 150,
      },
    });
  }),

  // OpenAI Whisper API handler
  http.post('https://api.openai.com/v1/audio/transcriptions', () => {
    return HttpResponse.json({
      text: 'Add fifty pounds of Atlantic salmon to freezer A batch SAL-001-2024',
    });
  }),

  // Handle file uploads (multipart form data)
  http.post(`${SUPABASE_URL}/storage/v1/object/*`, () => {
    return HttpResponse.json({
      Key: 'uploads/test-file.csv',
      id: 'upload-123',
      fullPath: 'uploads/test-file.csv',
    });
  }),

  // Error simulation handlers
  http.get(`${SUPABASE_URL}/rest/v1/products`, ({ request }) => {
    const url = new URL(request.url);
    if (url.searchParams.get('simulate') === 'error') {
      return new HttpResponse(null, { status: 500 });
    }
  }),

  // RLS policy violation simulation
  http.get(`${SUPABASE_URL}/rest/v1/products`, ({ request }) => {
    const headers = Object.fromEntries(request.headers.entries());
    if (headers.authorization === 'Bearer invalid-token') {
      return HttpResponse.json({ message: 'JWT expired', code: 'PGRST301' }, { status: 401 });
    }
  }),

  // Include vendor-specific handlers
  ...vendorHandlers,
];
