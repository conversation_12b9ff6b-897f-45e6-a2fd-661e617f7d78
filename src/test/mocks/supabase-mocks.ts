/**
 * Comprehensive Supabase mocking utilities for testing
 * Provides realistic database operations, authentication, storage, and real-time subscriptions
 */

import { vi } from 'vitest';
import { SupabaseClient, PostgrestResponse, AuthResponse } from '@supabase/supabase-js';

// Mock data types based on our schema
export interface MockProduct {
  id: string;
  name: string;
  category_id: string;
  species?: string;
  origin?: string;
  sustainability_rating?: string;
  storage_requirements?: string;
  allergens?: string[];
  created_at: string;
  updated_at: string;
}

export interface MockInventoryEvent {
  id: string;
  event_type: 'receiving' | 'sale' | 'disposal' | 'production' | 'adjustment';
  product_id: string;
  quantity: number;
  unit: string;
  batch_number?: string;
  notes?: string;
  occurred_at: string;
  created_at: string;
  voice_confidence_score?: number;
  voice_confidence_breakdown?: any;
  raw_transcript?: string;
  audio_recording_url?: string;
  created_by_voice?: boolean;
}

export interface MockUser {
  id: string;
  email: string;
  user_metadata: {
    full_name?: string;
    company?: string;
  };
  app_metadata: {
    role?: string;
  };
  created_at: string;
  updated_at: string;
}

// In-memory database for testing
export class MockSupabaseDatabase {
  private products: MockProduct[] = [];
  private inventoryEvents: MockInventoryEvent[] = [];
  private users: MockUser[] = [];
  private categories: any[] = [];
  private vendors: any[] = [];
  private customers: any[] = [];

  constructor() {
    this.seedInitialData();
  }

  private seedInitialData() {
    // Seed test categories
    this.categories = [
      { id: 'cat-fish', name: 'Fish', description: 'Fresh and frozen fish' },
      { id: 'cat-shellfish', name: 'Shellfish', description: 'Crabs, lobsters, shrimp' },
      { id: 'cat-mollusks', name: 'Mollusks', description: 'Oysters, clams, mussels' },
    ];

    // Seed test products
    this.products = [
      {
        id: 'prod-salmon-1',
        name: 'Atlantic Salmon',
        category_id: 'cat-fish',
        species: 'Salmo salar',
        origin: 'Norway',
        sustainability_rating: 'A',
        storage_requirements: 'Keep refrigerated at 32-38°F',
        allergens: ['Fish'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 'prod-crab-1',
        name: 'Dungeness Crab',
        category_id: 'cat-shellfish',
        species: 'Cancer magister',
        origin: 'Pacific Northwest',
        sustainability_rating: 'A',
        storage_requirements: 'Keep live or frozen',
        allergens: ['Shellfish'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];

    // Seed test users
    this.users = [
      {
        id: 'user-1',
        email: '<EMAIL>',
        user_metadata: { full_name: 'Test User', company: 'Test Company' },
        app_metadata: { role: 'manager' },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];

    // Seed test vendors
    this.vendors = [
      {
        id: 'vendor-1',
        name: 'Ocean Fresh Seafoods',
        contact_email: '<EMAIL>',
        contact_phone: '555-0123',
        address: '123 Harbor St, Seattle, WA',
      },
      {
        id: 'vendor-2',
        name: 'Pacific Catch',
        contact_email: '<EMAIL>',
        contact_phone: '555-0456',
        address: '456 Wharf Ave, Portland, OR',
      },
    ];

    // Seed test customers
    this.customers = [
      {
        id: 'customer-1',
        name: 'Marina Restaurant',
        contact_email: '<EMAIL>',
        contact_phone: '555-0789',
        address: '789 Pier Blvd, San Francisco, CA',
      },
    ];
  }

  // Generic table operations
  getTable(tableName: string): any[] {
    switch (tableName) {
      case 'products':
        return this.products;
      case 'inventory_events':
        return this.inventoryEvents;
      case 'categories':
        return this.categories;
      case 'vendors':
        return this.vendors;
      case 'customers':
        return this.customers;
      case 'users':
        return this.users;
      default:
        return [];
    }
  }

  setTable(tableName: string, data: any[]): void {
    switch (tableName) {
      case 'products':
        this.products = data;
        break;
      case 'inventory_events':
        this.inventoryEvents = data;
        break;
      case 'categories':
        this.categories = data;
        break;
      case 'vendors':
        this.vendors = data;
        break;
      case 'customers':
        this.customers = data;
        break;
      case 'users':
        this.users = data;
        break;
    }
  }

  // Query operations
  select(tableName: string, columns?: string[]): any[] {
    const table = this.getTable(tableName);
    if (!columns || columns.includes('*')) {
      return [...table];
    }
    return table.map((row) => {
      const filtered: any = {};
      columns.forEach((col) => {
        if (row[col] !== undefined) {
          filtered[col] = row[col];
        }
      });
      return filtered;
    });
  }

  insert(tableName: string, data: any | any[]): any[] {
    const table = this.getTable(tableName);
    const records = Array.isArray(data) ? data : [data];

    const insertedRecords = records.map((record) => ({
      ...record,
      id: record.id ?? `${tableName}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      created_at: record.created_at ?? new Date().toISOString(),
      updated_at: record.updated_at ?? new Date().toISOString(),
    }));

    table.push(...insertedRecords);
    this.setTable(tableName, table);
    return insertedRecords;
  }

  update(tableName: string, updates: any, condition: { column: string; value: any }): any[] {
    const table = this.getTable(tableName);
    const updatedRecords: any[] = [];

    for (let i = 0; i < table.length; i++) {
      if (table[i][condition.column] === condition.value) {
        table[i] = {
          ...table[i],
          ...updates,
          updated_at: new Date().toISOString(),
        };
        updatedRecords.push(table[i]);
      }
    }

    this.setTable(tableName, table);
    return updatedRecords;
  }

  delete(tableName: string, condition: { column: string; value: any }): any[] {
    const table = this.getTable(tableName);
    const deletedRecords: any[] = [];

    const newTable = table.filter((row) => {
      if (row[condition.column] === condition.value) {
        deletedRecords.push(row);
        return false;
      }
      return true;
    });

    this.setTable(tableName, newTable);
    return deletedRecords;
  }

  // Filter operations
  eq(data: any[], column: string, value: any): any[] {
    return data.filter((row) => row[column] === value);
  }

  neq(data: any[], column: string, value: any): any[] {
    return data.filter((row) => row[column] !== value);
  }

  gt(data: any[], column: string, value: any): any[] {
    return data.filter((row) => row[column] > value);
  }

  gte(data: any[], column: string, value: any): any[] {
    return data.filter((row) => row[column] >= value);
  }

  lt(data: any[], column: string, value: any): any[] {
    return data.filter((row) => row[column] < value);
  }

  lte(data: any[], column: string, value: any): any[] {
    return data.filter((row) => row[column] <= value);
  }

  like(data: any[], column: string, pattern: string): any[] {
    const regex = new RegExp(pattern.replace(/%/g, '.*'), 'i');
    return data.filter((row) => regex.test(row[column]));
  }

  in(data: any[], column: string, values: any[]): any[] {
    return data.filter((row) => values.includes(row[column]));
  }

  order(data: any[], column: string, ascending = true): any[] {
    return [...data].sort((a, b) => {
      if (ascending) {
        return a[column] > b[column] ? 1 : -1;
      } else {
        return a[column] < b[column] ? 1 : -1;
      }
    });
  }

  limit(data: any[], count: number): any[] {
    return data.slice(0, count);
  }

  // Clear all data (useful for test cleanup)
  clear(): void {
    this.products = [];
    this.inventoryEvents = [];
    this.users = [];
    this.categories = [];
    this.vendors = [];
    this.customers = [];
    this.seedInitialData();
  }
}

// Create singleton database instance for tests
const mockDatabase = new MockSupabaseDatabase();

const defaultMockConfig: SupabaseMockConfig = {
  shouldFail: false,
  failureType: 'network',
  latencyMs: 100,
  authUser: null,
};

let globalMockConfig: SupabaseMockConfig = { ...defaultMockConfig };

export const resetMockDatabase = (): void => {
  mockDatabase.clear();
};

export const seedMockDatabase = (seeders?: {
  products?: MockProduct[];
  inventoryEvents?: MockInventoryEvent[];
  users?: MockUser[];
}): void => {
  mockDatabase.clear();

  if (seeders?.products) {
    seeders.products.forEach((product) => mockDatabase.insert('products', product));
  }
  if (seeders?.inventoryEvents) {
    seeders.inventoryEvents.forEach((event) =>
      mockDatabase.insert('inventory_events', event)
    );
  }
  if (seeders?.users) {
    seeders.users.forEach((user) => mockDatabase.insert('users', user));
  }
};

export const setPerformanceSimulation = (enabled: boolean, latencyMs = 150): void => {
  globalMockConfig = {
    ...globalMockConfig,
    latencyMs: enabled ? latencyMs : defaultMockConfig.latencyMs,
  };
};

export const mockNetworkIssue = (
  failureType: 'connection' | 'auth' | 'permission' | 'validation' = 'connection'
): void => {
  globalMockConfig = {
    ...globalMockConfig,
    shouldFail: true,
    failureType: failureType === 'connection' ? 'network' : failureType,
  };
};

export const restoreNetworkConditions = (): void => {
  globalMockConfig = { ...globalMockConfig, shouldFail: false, failureType: 'network' };
};

export const measureDatabasePerformance = async <T>(
  operation: () => Promise<T> | T
): Promise<{ result: T; duration: number; metrics: { duration: number; queries: number } }> => {
  const now = () => (typeof performance !== 'undefined' ? performance.now() : Date.now());
  const start = now();
  const result = await operation();
  const duration = now() - start;

  return {
    result,
    duration,
    metrics: {
      duration,
      queries: 1,
    },
  };
};

export interface SupabaseMockConfig {
  shouldFail?: boolean;
  failureType?: 'network' | 'auth' | 'permission' | 'validation';
  latencyMs?: number;
  authUser?: MockUser | null;
}

// Enhanced Supabase client mock
export class MockSupabaseClient {
  private config: SupabaseMockConfig = {
    shouldFail: false,
    latencyMs: 100,
    authUser: null,
  };

  private currentQuery: {
    tableName?: string;
    operation?: string;
    data?: any[];
    filters?: any[];
  } = {};

  constructor(config: SupabaseMockConfig = {}) {
    this.config = { ...this.config, ...config };
  }

  // Configuration methods
  setConfig(config: Partial<SupabaseMockConfig>): void {
    this.config = { ...this.config, ...config };
  }

  reset(): void {
    mockDatabase.clear();
    this.config = {
      shouldFail: false,
      latencyMs: 100,
      authUser: null,
    };
  }

  private syncConfig(): void {
    this.config = { ...this.config, ...globalMockConfig };
  }

  // Simulate network latency
  private async simulateLatency(): Promise<void> {
    this.syncConfig();
    if (this.config.latencyMs && this.config.latencyMs > 0) {
      await new Promise((resolve) => setTimeout(resolve, this.config.latencyMs));
    }
  }

  // Create error responses
  private createError(type: string = 'generic'): any {
    switch (type) {
      case 'network':
        return { message: 'Connection refused', status: 500 };
      case 'auth':
        return { message: 'Authentication failed', status: 401 };
      case 'permission':
        return { message: 'Permission denied', status: 403 };
      case 'validation':
        return { message: 'Invalid input data', status: 400 };
      default:
        return { message: 'Unknown error', status: 500 };
    }
  }

  // Query builder pattern implementation
  from(tableName: string) {
    this.currentQuery = { tableName, data: mockDatabase.select(tableName), filters: [] };
    return this;
  }

  select(columns?: string) {
    if (!this.currentQuery.tableName) throw new Error('No table selected');

    const columnList = columns ? columns.split(',').map((c) => c.trim()) : ['*'];
    this.currentQuery.data = mockDatabase.select(this.currentQuery.tableName, columnList);
    this.currentQuery.operation = 'select';
    return this;
  }

  insert(data: any | any[]) {
    if (!this.currentQuery.tableName) throw new Error('No table selected');

    this.currentQuery.data = mockDatabase.insert(this.currentQuery.tableName, data);
    this.currentQuery.operation = 'insert';
    return this;
  }

  update(data: any) {
    if (!this.currentQuery.tableName) throw new Error('No table selected');
    this.currentQuery.operation = 'update';
    this.currentQuery.data = [data]; // Store the update data
    return this;
  }

  delete() {
    if (!this.currentQuery.tableName) throw new Error('No table selected');
    this.currentQuery.operation = 'delete';
    return this;
  }

  // Filter methods
  eq(column: string, value: any) {
    if (!this.currentQuery.data) return this;
    this.currentQuery.data = mockDatabase.eq(this.currentQuery.data, column, value);
    this.currentQuery.filters?.push({ type: 'eq', column, value });
    return this;
  }

  neq(column: string, value: any) {
    if (!this.currentQuery.data) return this;
    this.currentQuery.data = mockDatabase.neq(this.currentQuery.data, column, value);
    return this;
  }

  gt(column: string, value: any) {
    if (!this.currentQuery.data) return this;
    this.currentQuery.data = mockDatabase.gt(this.currentQuery.data, column, value);
    return this;
  }

  gte(column: string, value: any) {
    if (!this.currentQuery.data) return this;
    this.currentQuery.data = mockDatabase.gte(this.currentQuery.data, column, value);
    return this;
  }

  lt(column: string, value: any) {
    if (!this.currentQuery.data) return this;
    this.currentQuery.data = mockDatabase.lt(this.currentQuery.data, column, value);
    return this;
  }

  lte(column: string, value: any) {
    if (!this.currentQuery.data) return this;
    this.currentQuery.data = mockDatabase.lte(this.currentQuery.data, column, value);
    return this;
  }

  like(column: string, pattern: string) {
    if (!this.currentQuery.data) return this;
    this.currentQuery.data = mockDatabase.like(this.currentQuery.data, column, pattern);
    return this;
  }

  in(column: string, values: any[]) {
    if (!this.currentQuery.data) return this;
    this.currentQuery.data = mockDatabase.in(this.currentQuery.data, column, values);
    return this;
  }

  order(column: string, options?: { ascending?: boolean }) {
    if (!this.currentQuery.data) return this;
    this.currentQuery.data = mockDatabase.order(
      this.currentQuery.data,
      column,
      options?.ascending !== false
    );
    return this;
  }

  limit(count: number) {
    if (!this.currentQuery.data) return this;
    this.currentQuery.data = mockDatabase.limit(this.currentQuery.data, count);
    return this;
  }

  // Execute query methods
  async single(): Promise<PostgrestResponse<any>> {
    await this.simulateLatency();

    if (this.config.shouldFail) {
      return {
        data: null,
        error: this.createError(this.config.failureType),
        status: 500,
        statusText: 'Internal Server Error',
      };
    }

    const data = this.currentQuery.data?.[0] ?? null;
    return {
      data,
      error: null,
      status: 200,
      statusText: 'OK',
    };
  }

  async maybeSingle(): Promise<PostgrestResponse<any>> {
    return this.single();
  }

  // Main execution method (used by .then())
  async then(resolve: (value: PostgrestResponse<any>) => any): Promise<any> {
    await this.simulateLatency();

    if (this.config.shouldFail) {
      const result = {
        data: null,
        error: this.createError(this.config.failureType),
        status: 500,
        statusText: 'Internal Server Error',
      };
      return resolve(result);
    }

    let result: PostgrestResponse<any>;

    switch (this.currentQuery.operation) {
      case 'update':
        if (this.currentQuery.filters && this.currentQuery.filters.length > 0) {
          const filter = this.currentQuery.filters[0];
          const updatedRecords = mockDatabase.update(
            this.currentQuery.tableName!,
            this.currentQuery.data![0],
            { column: filter.column, value: filter.value }
          );
          result = {
            data: updatedRecords,
            error: null,
            status: 200,
            statusText: 'OK',
          };
        } else {
          result = {
            data: null,
            error: { message: 'Update requires a filter' },
            status: 400,
            statusText: 'Bad Request',
          };
        }
        break;

      case 'delete':
        if (this.currentQuery.filters && this.currentQuery.filters.length > 0) {
          const filter = this.currentQuery.filters[0];
          const deletedRecords = mockDatabase.delete(this.currentQuery.tableName!, {
            column: filter.column,
            value: filter.value,
          });
          result = {
            data: deletedRecords,
            error: null,
            status: 200,
            statusText: 'OK',
          };
        } else {
          result = {
            data: null,
            error: { message: 'Delete requires a filter' },
            status: 400,
            statusText: 'Bad Request',
          };
        }
        break;

      default:
        result = {
          data: this.currentQuery.data ?? [],
          error: null,
          status: 200,
          statusText: 'OK',
        };
    }

    return resolve(result);
  }

  // Authentication mock
  auth = {
    getSession: vi.fn().mockImplementation(async () => {
      await this.simulateLatency();
      return {
        data: {
          session: this.config.authUser
            ? {
                user: this.config.authUser,
                access_token: 'mock-token',
                refresh_token: 'mock-refresh',
              }
            : null,
        },
        error: null,
      };
    }),

    getUser: vi.fn().mockImplementation(async () => {
      await this.simulateLatency();
      return {
        data: { user: this.config.authUser },
        error: this.config.authUser ? null : { message: 'No user' },
      };
    }),

    signInWithPassword: vi.fn().mockImplementation(async ({ email, password }) => {
      await this.simulateLatency();

      if (this.config.shouldFail && this.config.failureType === 'auth') {
        return {
          data: { user: null, session: null },
          error: { message: 'Invalid credentials' },
        };
      }

      const user = mockDatabase.getTable('users').find((u: any) => u.email === email);
      return {
        data: {
          user,
          session: user ? { user, access_token: 'mock-token' } : null,
        },
        error: user ? null : { message: 'Invalid credentials' },
      };
    }),

    signOut: vi.fn().mockImplementation(async () => {
      await this.simulateLatency();
      this.config.authUser = null;
      return { error: null };
    }),

    onAuthStateChange: vi.fn().mockImplementation((callback) => {
      // Simulate auth state change
      setTimeout(() => {
        callback('SIGNED_IN', { user: this.config.authUser });
      }, 100);

      return {
        data: {
          subscription: {
            unsubscribe: vi.fn(),
          },
        },
      };
    }),
  };

  // Storage mock
  storage = {
    from: vi.fn().mockImplementation((bucket: string) => ({
      upload: vi.fn().mockImplementation(async (path: string, file: File | Blob) => {
        await this.simulateLatency();

        if (this.config.shouldFail) {
          return {
            data: null,
            error: { message: 'Upload failed' },
          };
        }

        return {
          data: {
            path: `${bucket}/${path}`,
            id: `upload-${Date.now()}`,
            fullPath: `${bucket}/${path}`,
          },
          error: null,
        };
      }),

      createSignedUrl: vi.fn().mockImplementation(async (path: string, expiresIn: number) => {
        await this.simulateLatency();
        return {
          data: {
            signedUrl: `https://mock-storage.example.com/${bucket}/${path}?expires=${expiresIn}`,
          },
          error: null,
        };
      }),

      remove: vi.fn().mockImplementation(async (paths: string[]) => {
        await this.simulateLatency();
        return {
          data: paths.map((path) => ({ name: path })),
          error: null,
        };
      }),

      list: vi.fn().mockImplementation(async (path?: string) => {
        await this.simulateLatency();
        return {
          data: [
            { name: 'file1.jpg', id: 'file1', updated_at: new Date().toISOString() },
            { name: 'file2.mp3', id: 'file2', updated_at: new Date().toISOString() },
          ],
          error: null,
        };
      }),
    })),
  };

  // Real-time subscriptions mock
  channel = vi.fn().mockImplementation((channelName: string) => ({
    on: vi.fn().mockImplementation((event: string, filter: any, callback: Function) => {
      // Simulate real-time event
      setTimeout(() => {
        callback({
          eventType: event,
          new: { id: 'new-record', name: 'Test' },
          old: {},
          errors: null,
        });
      }, 200);
      return this;
    }),

    subscribe: vi.fn().mockImplementation((callback?: Function) => {
      setTimeout(() => {
        if (callback) callback('SUBSCRIBED', null);
      }, 100);
      return {
        unsubscribe: vi.fn(),
      };
    }),
  }));

  // RPC (Remote Procedure Call) mock
  rpc = vi.fn().mockImplementation(async (functionName: string, params: any = {}) => {
    await this.simulateLatency();

    if (this.config.shouldFail) {
      return {
        data: null,
        error: { message: `RPC function ${functionName} failed` },
      };
    }

    // Mock some common RPC functions
    switch (functionName) {
      case 'get_inventory_summary':
        return {
          data: {
            total_products: mockDatabase.getTable('products').length,
            total_events: mockDatabase.getTable('inventory_events').length,
            recent_events: mockDatabase.getTable('inventory_events').slice(-5),
          },
          error: null,
        };

      case 'calculate_stock_levels':
        return {
          data: mockDatabase.getTable('products').map((product: any) => ({
            product_id: product.id,
            current_stock: Math.floor(Math.random() * 1000),
            reserved_stock: Math.floor(Math.random() * 100),
            available_stock: Math.floor(Math.random() * 900),
          })),
          error: null,
        };

      default:
        return {
          data: { message: `Mock response for ${functionName}`, params },
          error: null,
        };
    }
  });
}

// Factory functions for creating mock clients
export const createMockSupabaseClient = (config: SupabaseMockConfig = {}): MockSupabaseClient => {
  return new MockSupabaseClient({ ...globalMockConfig, ...config });
};

// Test data generators for Supabase entities
export const generateMockProduct = (overrides: Partial<MockProduct> = {}): MockProduct => ({
  id: `prod-${Date.now()}`,
  name: 'Test Product',
  category_id: 'cat-fish',
  species: 'Test species',
  origin: 'Test Origin',
  sustainability_rating: 'A',
  storage_requirements: 'Keep refrigerated',
  allergens: ['Fish'],
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
});

export const generateMockInventoryEvent = (
  overrides: Partial<MockInventoryEvent> = {}
): MockInventoryEvent => ({
  id: `event-${Date.now()}`,
  event_type: 'receiving',
  product_id: 'prod-1',
  quantity: 100,
  unit: 'lbs',
  batch_number: 'BATCH-001',
  notes: 'Test event',
  occurred_at: new Date().toISOString(),
  created_at: new Date().toISOString(),
  ...overrides,
});

export const generateMockUser = (overrides: Partial<MockUser> = {}): MockUser => ({
  id: `user-${Date.now()}`,
  email: '<EMAIL>',
  user_metadata: { full_name: 'Test User', company: 'Test Company' },
  app_metadata: { role: 'user' },
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
});

// Global mock setup for vi.mock()
export const mockSupabase = {
  createClient: vi.fn().mockImplementation(() => createMockSupabaseClient()),
  SupabaseClient: MockSupabaseClient,
};

// Export the singleton database for direct manipulation in tests
export { mockDatabase };
