import { test, expect } from '@playwright/test';

test.describe('Temperature Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the temperature dashboard
    await page.goto('/temperature');

    // Wait for the page to load
    await page.waitForLoadState('networkidle');

    // Wait for the dashboard component to be visible
    await expect(page.locator('h1:has-text("Temperature Dashboard")')).toBeVisible();
  });

  test('should display temperature dashboard with key components', async ({ page }) => {
    // Check that main dashboard elements are visible
    await expect(page.locator('h1:has-text("Temperature Dashboard")')).toBeVisible();

    // Check for sensor selection dropdown
    await expect(
      page.locator(
        'select[aria-label*="sensor"], [role="combobox"]:has-text("Select sensor"), button:has-text("All Sensors")'
      )
    ).toBeVisible();

    // Check for date range selection
    await expect(
      page.locator(
        'select[aria-label*="range"], [role="combobox"]:has-text("Last"), button:has-text("Last 24 Hours")'
      )
    ).toBeVisible();

    // Check for temperature unit toggle
    await expect(
      page.locator(
        'button:has-text("°"), button[aria-label*="temperature"], button[aria-label*="unit"]'
      )
    ).toBeVisible();

    // Check for temperature and humidity cards
    await expect(page.locator('text=Temperature')).toBeVisible();
    await expect(page.locator('text=Humidity')).toBeVisible();

    // Check for temperature chart
    await expect(page.locator('text=Temperature Chart')).toBeVisible();
  });

  test('should have multi-sensor view option', async ({ page }) => {
    // Look for sensor selection dropdown
    const sensorDropdown = page
      .locator(
        'button:has-text("All Sensors"), button:has-text("Select sensor"), [role="combobox"]'
      )
      .first();

    // Click the dropdown to open it
    await sensorDropdown.click();

    // Wait for dropdown options to appear
    await page.waitForTimeout(500);

    // Check if "All Sensors" option exists
    const allSensorsOption = page.locator(
      '[role="option"]:has-text("All Sensors"), text="All Sensors"'
    );
    await expect(allSensorsOption).toBeVisible();

    // Select "All Sensors" if not already selected
    await allSensorsOption.click();

    // Verify multi-sensor indicators
    await expect(page.locator('text=All Sensors')).toBeVisible();
    await expect(
      page.locator('text=Temperature Chart (All Sensors), text=Temperature (All Sensors)')
    ).toBeVisible();
  });

  test('should have working temperature unit toggle', async ({ page }) => {
    // Look for temperature unit toggle button
    const unitToggle = page
      .locator(
        'button:has-text("°C"), button:has-text("°F"), button[aria-label*="unit"], button[title*="Switch"]'
      )
      .first();

    // Verify the toggle button is visible
    await expect(unitToggle).toBeVisible();

    // Get initial unit state
    const initialUnit = await unitToggle.textContent();
    console.log('Initial unit:', initialUnit);

    // Click the toggle
    await unitToggle.click();

    // Wait for the change to take effect
    await page.waitForTimeout(500);

    // Verify the unit changed
    const newUnit = await unitToggle.textContent();
    console.log('New unit:', newUnit);

    expect(newUnit).not.toBe(initialUnit);

    // Click again to toggle back
    await unitToggle.click();
    await page.waitForTimeout(500);

    // Verify it changed back
    const finalUnit = await unitToggle.textContent();
    expect(finalUnit).toBe(initialUnit);
  });

  test('should display temperature chart with data', async ({ page }) => {
    // Wait for chart to load
    await page.waitForTimeout(2000);

    // Check if chart container exists
    const chartContainer = page
      .locator('.recharts-wrapper, [data-testid="temperature-chart"], text=Temperature Chart')
      .first();
    await expect(chartContainer).toBeVisible();

    // Look for chart elements (SVG, canvas, or chart-specific classes)
    const chartElements = page.locator(
      'svg.recharts-surface, canvas, .recharts-line, .recharts-cartesian-grid'
    );

    // If chart elements are found, verify they're visible
    const chartElementCount = await chartElements.count();
    if (chartElementCount > 0) {
      await expect(chartElements.first()).toBeVisible();
      console.log(`Found ${chartElementCount} chart elements`);
    }

    // Check for "No data" message or actual data
    const noDataMessage = page.locator('text=No temperature data available, text=Loading sensors');
    const hasNoData = (await noDataMessage.count()) > 0;

    if (hasNoData) {
      console.log('Dashboard shows no data available message');
      await expect(noDataMessage.first()).toBeVisible();
    } else {
      console.log('Dashboard should be showing temperature data');
      // If there's data, we should see some chart elements
      expect(chartElementCount).toBeGreaterThan(0);
    }
  });

  test('should handle refresh functionality', async ({ page }) => {
    // Look for refresh button
    const refreshButton = page.locator(
      'button:has-text("Refresh"), button[aria-label="Refresh"], button:has(svg.lucide-refresh)'
    );

    if ((await refreshButton.count()) > 0) {
      await expect(refreshButton.first()).toBeVisible();

      // Click refresh button
      await refreshButton.first().click();

      // Wait for potential loading state
      await page.waitForTimeout(1000);

      // Verify page didn't crash
      await expect(page.locator('h1:has-text("Temperature Dashboard")')).toBeVisible();
    } else {
      console.log('No refresh button found on the page');
    }
  });

  test('should handle export functionality', async ({ page }) => {
    // Look for download/export button
    const exportButton = page.locator(
      'button:has-text("Download"), button:has-text("Export"), button:has(svg.lucide-download)'
    );

    if ((await exportButton.count()) > 0) {
      await expect(exportButton.first()).toBeVisible();

      // Note: We won't actually click it to avoid downloading files in test
      console.log('Export button is available');
    } else {
      console.log('No export button found on the page');
    }
  });

  test('should be responsive on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Wait for layout to adjust
    await page.waitForTimeout(1000);

    // Verify main elements are still visible
    await expect(page.locator('h1:has-text("Temperature Dashboard")')).toBeVisible();

    // Check that controls are accessible (may be stacked on mobile)
    const sensorControl = page
      .locator('button:has-text("All Sensors"), button:has-text("Select sensor")')
      .first();
    const unitControl = page.locator('button:has-text("°")').first();

    if ((await sensorControl.count()) > 0) {
      await expect(sensorControl).toBeVisible();
    }

    if ((await unitControl.count()) > 0) {
      await expect(unitControl).toBeVisible();
    }

    // Temperature cards should still be visible
    await expect(page.locator('text=Temperature')).toBeVisible();
  });
});
