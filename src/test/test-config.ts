import { beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { cleanup } from '@testing-library/react';

// Comprehensive test configuration for Seafood Manager application
export const TEST_CONFIG = {
  // Timeouts for different test types
  timeouts: {
    unit: 5000, // Unit tests should be fast
    integration: 10000, // Integration tests can take longer
    e2e: 30000, // E2E tests can be slow
    voice: 15000, // Voice processing tests need time for API calls
    import: 20000, // Import tests for large files
    performance: 60000, // Performance tests can take up to 1 minute
  },

  // Performance thresholds
  performance: {
    // Component rendering performance
    componentRender: {
      maxRenderTime: 100, // milliseconds
      maxReRenders: 5, // during lifecycle
    },

    // API response times
    api: {
      fast: 200, // For simple queries
      standard: 1000, // For complex operations
      bulk: 5000, // For bulk operations
    },

    // Voice processing thresholds
    voice: {
      transcription: 3000, // Max time for audio transcription
      processing: 2000, // Max time for command processing
      endToEnd: 5000, // Max time for complete voice workflow
    },

    // Import/export thresholds
    import: {
      smallFile: 1000, // < 1MB files
      mediumFile: 5000, // 1-10MB files
      largeFile: 15000, // > 10MB files
    },
  },

  // Database test configuration
  database: {
    testTables: [
      'products',
      'categories',
      'inventory_events',
      'vendors',
      'customers',
      'haccp_events',
      'voice_events',
    ],
    maxRecordsPerTest: 1000,
    cleanupAfterEach: true,
  },

  // Voice processing test configuration
  voice: {
    mockLatency: {
      transcription: 800,
      processing: 1200,
      storage: 300,
    },
    confidenceThresholds: {
      high: 0.85,
      medium: 0.65,
      low: 0.45,
    },
    maxAudioSizeMB: 25,
    supportedFormats: ['audio/webm', 'audio/wav', 'audio/mp3', 'audio/m4a'],
  },

  // Import system test configuration
  import: {
    maxFileSizeMB: 100,
    supportedFormats: [
      'text/csv',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ],
    testDataSets: {
      small: { rows: 50, expectedProcessTime: 1000 },
      medium: { rows: 500, expectedProcessTime: 5000 },
      large: { rows: 5000, expectedProcessTime: 15000 },
    },
  },

  // Security test configuration
  security: {
    testUsers: [
      { id: 'user-1', role: 'manager', company: 'test-company-1' },
      { id: 'user-2', role: 'employee', company: 'test-company-1' },
      { id: 'user-3', role: 'manager', company: 'test-company-2' },
    ],
  },

  // Mock service configuration
  mocks: {
    openai: {
      defaultLatency: 500,
      rateLimitWindow: 60000,
      rateLimitRequests: 50,
    },
    supabase: {
      defaultLatency: 100,
      maxQueryComplexity: 10,
    },
  },

  // Test data generation
  testData: {
    products: {
      categories: ['fish', 'shellfish', 'mollusks'],
      defaultQuantity: 100,
      defaultUnit: 'pounds',
    },
    events: {
      typesDistribution: {
        receiving: 0.4,
        sale: 0.3,
        adjustment: 0.15,
        disposal: 0.1,
        production: 0.05,
      },
    },
  },
};

// Global test setup
beforeAll(() => {
  // Setup global mocks
  global.ResizeObserver = class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  };

  // Mock IntersectionObserver with all required properties
  class MockIntersectionObserver implements IntersectionObserver {
    root: Element | Document | null = null;
    rootMargin: string = '0px';
    thresholds: ReadonlyArray<number> = [0];

    constructor(_callback: IntersectionObserverCallback, options?: IntersectionObserverInit) {
      this.root = options?.root ?? null;
      this.rootMargin = options?.rootMargin ?? '0px';
      this.thresholds = options?.threshold
        ? Array.isArray(options.threshold)
          ? options.threshold
          : [options.threshold]
        : [0];
    }

    observe(_target: Element): void {}
    unobserve(_target: Element): void {}
    disconnect(): void {}
    takeRecords(): IntersectionObserverEntry[] {
      return [];
    }
  }

  (globalThis as any).IntersectionObserver = MockIntersectionObserver;

  // Mock matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: (query: string) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: () => {},
      removeListener: () => {},
      addEventListener: () => {},
      removeEventListener: () => {},
      dispatchEvent: () => {},
    }),
  });

  // Mock scrollTo
  window.scrollTo = () => {};

  // Mock localStorage
  const localStorageMock = {
    getItem: (_key: string) => null,
    setItem: (_key: string, _value: string) => {},
    removeItem: (_key: string) => {},
    clear: () => {},
    length: 0,
    key: (_index: number) => null,
  };
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
  });

  // Mock sessionStorage
  Object.defineProperty(window, 'sessionStorage', {
    value: localStorageMock,
  });
});

// Cleanup after all tests
afterAll(() => {
  // Cleanup any global resources
});

// Setup before each test
beforeEach(() => {
  // Reset any global state
});

// Cleanup after each test
afterEach(() => {
  // Cleanup React components
  cleanup();

  // Clear all mocks
  vi.clearAllMocks();
});

// Test utilities
export const testUtils = {
  // Create mock audio blob
  createMockAudioBlob: (sizeKB: number = 100): Blob => {
    const arrayBuffer = new ArrayBuffer(sizeKB * 1024);
    return new Blob([arrayBuffer], { type: 'audio/webm' });
  },

  // Create mock voice event
  createMockVoiceEvent: (overrides = {}) => ({
    id: 'test-event-123',
    event_type: 'receiving',
    product_name: 'Test Product',
    quantity: 50,
    unit: 'lbs',
    voice_confidence_score: 0.9,
    voice_confidence_breakdown: {
      product_match: 0.9,
      quantity_extraction: 0.9,
      vendor_match: 0.9,
      overall: 0.9,
    },
    raw_transcript: 'Test transcript',
    audio_recording_url: 'https://example.com/audio.wav',
    occurred_at: '2024-01-15T10:30:00Z',
    created_at: '2024-01-15T10:30:00Z',
    created_by_voice: true,
    ...overrides,
  }),

  // Wait for async operations
  waitFor: (ms: number) => new Promise((resolve) => setTimeout(resolve, ms)),

  // Mock fetch responses
  mockFetchResponse: (data: any, status: number = 200) => {
    global.fetch = vi.fn().mockResolvedValue({
      ok: status >= 200 && status < 300,
      status,
      json: () => Promise.resolve(data),
      text: () => Promise.resolve(JSON.stringify(data)),
    });
  },

  // Performance measurement
  measurePerformance: async <T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> => {
    const startTime = performance.now();
    const result = await fn();
    const endTime = performance.now();
    return { result, duration: endTime - startTime };
  },
};

// Custom matchers for testing
export const customMatchers = {
  // Check if a value is within performance threshold
  toBeWithinPerformanceThreshold: (received: number, threshold: number) => {
    const pass = received <= threshold;
    return {
      message: () =>
        pass
          ? `Expected ${received} to exceed ${threshold}ms`
          : `Expected ${received} to be within ${threshold}ms threshold`,
      pass,
    };
  },

  // Check if audio blob is valid
  toBeValidAudioBlob: (received: Blob) => {
    const pass = received instanceof Blob && received.type.startsWith('audio/');
    return {
      message: () =>
        pass
          ? `Expected ${received} not to be a valid audio blob`
          : `Expected ${received} to be a valid audio blob`,
      pass,
    };
  },
};

// Extend expect with custom matchers
if (typeof expect !== 'undefined') {
  expect.extend(customMatchers);
}

// Export types for custom matchers
declare global {
  namespace Vi {
    interface Assertion {
      toBeWithinPerformanceThreshold(threshold: number): void;
      toBeValidAudioBlob(): void;
    }
  }
}
