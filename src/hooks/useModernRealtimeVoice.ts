import { useState, useEffect, useRef, useCallback } from 'react';
import { ModernRealtimeVoiceClient, ModernVoiceClientConfig, createModernRealtimeVoiceClient } from '../lib/ModernRealtimeVoiceClient';

/**
 * Modern React hook for OpenAI Realtime Voice processing using the 2025 Agents SDK
 * Uses RealtimeAgent and RealtimeSession for simplified voice interactions
 */

export interface UseModernRealtimeVoiceOptions {
  apiKey?: string;
  ephemeralToken?: string;
  relayUrl?: string;
  transport?: 'webrtc' | 'websocket';
  useInsecureApiKey?: boolean;
  model?: string;
  voice?: 'alloy' | 'echo' | 'fable' | 'onyx' | 'nova' | 'shimmer';
  enableDebugLogs?: boolean;
  autoConnect?: boolean;
  forceTransport?: 'webrtc' | 'websocket';
  disableFallback?: boolean;
  enableVerboseLogging?: boolean;
  /**
   * Enables the alternative initialization strategy that configures the RealtimeSession upfront.
   * Use when SDK constructor patching fails with the session.type error.
   */
  useAlternativeInitialization?: boolean;
}

export interface ModernRealtimeVoiceState {
  isConnected: boolean;
  isListening: boolean;
  error: string | null;
  transcript: string;
  response: string;
  toolCalls: Array<{
    toolName: string;
    args: Record<string, unknown>;
    result: unknown;
    timestamp: number;
  }>;
  // Performance metrics
  latency?: number;
  connectionAttempts: number;
  // Transport debugging
  currentTransport?: 'webrtc' | 'websocket';
  connectionState?: string;
  iceConnectionState?: string;
  toolRegistrationStatus?: 'pending' | 'success' | 'failed';
  // UI component support (for new voice assistant components)
  interimTranscript: string;
  voiceState: 'idle' | 'listening' | 'processing' | 'responding';
  currentlyExecutingTool: string | null;
}

export function useModernRealtimeVoice(options: UseModernRealtimeVoiceOptions = {}) {
  const [state, setState] = useState<ModernRealtimeVoiceState>({
    isConnected: false,
    isListening: false,
    error: null,
    transcript: '',
    response: '',
    toolCalls: [],
    connectionAttempts: 0,
    interimTranscript: '',
    voiceState: 'idle',
    currentlyExecutingTool: null,
  });

  const clientRef = useRef<ModernRealtimeVoiceClient | null>(null);
  const isInitializedRef = useRef(false);
  const retryCountRef = useRef(0);
  const clientNeedsRecreationRef = useRef(false);
  const maxRetries = 3;
  const retryDelay = 1000; // Base delay in ms

  // Track credential readiness state
  const [credentialsReady, setCredentialsReady] = useState(false);

  // Initialize client
  const initializeClient = useCallback(() => {
    if (clientNeedsRecreationRef.current) {
      if (options.enableDebugLogs || options.enableVerboseLogging) {
        console.warn('♻️ Reinitializing voice client after critical error');
      }
      clientNeedsRecreationRef.current = false;
      clientRef.current = null;
      isInitializedRef.current = false;
    }

    if (isInitializedRef.current || clientRef.current) {
      return;
    }

    try {
      const config: ModernVoiceClientConfig = {
        apiKey: options.apiKey,
        ephemeralToken: options.ephemeralToken,
        relayUrl: options.relayUrl,
        transport: options.transport,
        useInsecureApiKey: options.useInsecureApiKey,
        model: options.model ?? 'gpt-4o-realtime-preview-2024-12-17',
        voice: options.voice ?? 'alloy',
        enableDebugLogs: options.enableDebugLogs ?? options.enableVerboseLogging ?? false,
        forceTransport: options.forceTransport,
        disableFallback: options.disableFallback,
        useAlternativeInitialization: options.useAlternativeInitialization ?? false,
      };

      // Enhanced transport logging
      if (config.enableDebugLogs) {
        console.log('🔧 useModernRealtimeVoice initializing with config:', {
          transport: config.transport,
          forceTransport: config.forceTransport,
          disableFallback: config.disableFallback,
          hasApiKey: !!config.apiKey,
          hasEphemeralToken: !!config.ephemeralToken,
          hasRelayUrl: !!config.relayUrl,
          model: config.model,
          voice: config.voice,
          useAlternativeInitialization: config.useAlternativeInitialization,
          apiKeyPrefix: config.apiKey ? config.apiKey.substring(0, 8) : 'none',
          ephemeralTokenPrefix: config.ephemeralToken ? config.ephemeralToken.substring(0, 8) : 'none'
        });
      }

      if (config.useAlternativeInitialization && (config.enableDebugLogs || options.enableVerboseLogging)) {
        console.log('🔧 Alternative initialization strategy enabled');
      }

      if (config.relayUrl && !config.apiKey && !config.ephemeralToken) {
        config.apiKey = 'relay';
        config.useInsecureApiKey = true;
      }

      const missingCredentials =
        !config.relayUrl && !config.apiKey && !config.ephemeralToken;

      // Wait until credentials are provided before initializing the client
      if (missingCredentials) {
        if (config.enableDebugLogs) {
          console.debug('⏳ useModernRealtimeVoice: awaiting apiKey or ephemeralToken before init');
          console.debug('   Credential status:', {
            hasApiKey: !!config.apiKey,
            hasEphemeralToken: !!config.ephemeralToken,
            hasRelayUrl: !!config.relayUrl
          });
        }
        setCredentialsReady(false);
        return;
      }

      // Mark credentials as ready
      setCredentialsReady(true);
      if (config.enableDebugLogs) {
        console.log('✅ Credentials ready - proceeding with client initialization');
      }

      const events = {
        onConnected: () => {
          const status = clientRef.current?.getConnectionStatus();
          setState(prev => ({
            ...prev,
            isConnected: true,
            error: null,
            voiceState: 'idle',
            currentTransport: status?.transport,
            connectionState: status?.connectionState,
            iceConnectionState: status?.iceConnectionState,
            toolRegistrationStatus: status?.toolRegistrationStatus
          }));

          if (options.enableDebugLogs || options.enableVerboseLogging) {
            console.log('✅ Voice connection established:', status);
          }
        },
        onDisconnected: () => {
          setState(prev => ({
            ...prev,
            isConnected: false,
            isListening: false,
            voiceState: 'idle',
            currentlyExecutingTool: null,
            interimTranscript: '',
            currentTransport: undefined,
            connectionState: 'disconnected',
            iceConnectionState: 'disconnected',
            toolRegistrationStatus: 'pending'
          }));

          if (options.enableDebugLogs || options.enableVerboseLogging) {
            console.log('🔌 Voice connection disconnected');
          }
        },
        onListening: () => {
          setState(prev => ({ ...prev, isListening: true, voiceState: 'listening', interimTranscript: '' }));
        },
        onProcessing: () => {
          setState(prev => ({ ...prev, isListening: false, voiceState: 'processing' }));
        },
        onTranscript: (transcript: string, isFinal: boolean) => {
          if (isFinal) {
            setState(prev => ({ ...prev, transcript, interimTranscript: '' }));
          } else {
            // Update interim transcript for live display
            setState(prev => ({ ...prev, interimTranscript: transcript }));
          }
        },
        onResponse: (text: string) => {
          setState(prev => ({ ...prev, response: text, voiceState: 'responding', currentlyExecutingTool: null }));
        },
        onError: (error: string) => {
          // Enhance error message to clarify the issue
          let enhancedError = error;
          if (error.includes('session.type') || error.includes('Unknown parameter')) {
            enhancedError = `OpenAI API Connection Error: ${error}. This is a known SDK bug with the session.type parameter. The DataChannel patch should prevent this - check browser console for patch application logs.`;
          } else if (error.includes('database') || error.includes('RLS')) {
            enhancedError = `Database Access Error: ${error}. Note: Voice authentication may be working, but database permissions need verification.`;
          }

          setState(prev => ({
            ...prev,
            error: enhancedError,
            isConnected: false,
            isListening: false,
            voiceState: 'idle',
            currentlyExecutingTool: null
          }));
        },
        onToolCall: (toolName: string, args: Record<string, unknown>, result: unknown) => {
          const toolCall = {
            toolName,
            args,
            result,
            timestamp: Date.now(),
          };
          setState(prev => ({
            ...prev,
            toolCalls: [...prev.toolCalls, toolCall],
            currentlyExecutingTool: toolName, // Track currently executing tool
            voiceState: 'processing'
          }));

          if (options.enableDebugLogs || options.enableVerboseLogging) {
            console.log('🛠️ Tool call executed:', {
              toolName,
              args,
              result: typeof result === 'string' ? result.substring(0, 100) : result,
              timestamp: new Date(toolCall.timestamp).toISOString()
            });
          }
        },
      };

      clientRef.current = createModernRealtimeVoiceClient(config, events);
      isInitializedRef.current = true;

      if (config.enableDebugLogs) {
        console.log('🎤 Modern RealtimeVoice client initialized');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize client';
      setState(prev => ({ ...prev, error: errorMessage }));
      console.error('Failed to initialize Modern RealtimeVoice client:', error);
    }
  }, [
    options.apiKey,
    options.ephemeralToken,
    options.relayUrl,
    options.transport,
    options.useInsecureApiKey,
    options.model,
    options.voice,
    options.enableDebugLogs,
    options.disableFallback,
    options.enableVerboseLogging,
    options.forceTransport,
    options.useAlternativeInitialization,
  ]);

  // Connect to the service with enhanced retry logic and diagnostics
  const connectWithRetry = useCallback(async (retryCount = 0): Promise<boolean> => {
    const startTime = performance.now();

    setState(prev => ({ ...prev, connectionAttempts: prev.connectionAttempts + 1 }));

    if (options.enableDebugLogs || options.enableVerboseLogging) {
      console.log(`🔌 Connection attempt ${retryCount + 1}/${maxRetries + 1} starting...`);
    }

    if (!clientRef.current) {
      initializeClient();
    }

    if (!clientRef.current) {
      if (!options.apiKey && !options.ephemeralToken && !options.relayUrl) {
        const error = 'No API credentials provided (apiKey, ephemeralToken, or relayUrl). Please create a session first or provide an API key.';
        setState(prev => ({ ...prev, error }));
        console.error('❌ Connection failed:', error);
        // Don't retry - fail immediately for missing credentials
        return false;
      }
      const error = 'Client initialization failed - cannot proceed with connection';
      console.error('❌ Client validation failed: client is null');
      setState(prev => ({ ...prev, error }));
      return false;
    }

    // Validate client readiness before attempting connection
    const clientStatus = clientRef.current.getConnectionStatus();
    if (!clientStatus.hasCredentials) {
      const error = 'Client initialized but credentials not set. This is a timing issue - please try again.';
      console.error('❌ Client validation failed:', {
        hasCredentials: clientStatus.hasCredentials,
        credentialType: clientStatus.credentialType
      });
      setState(prev => ({ ...prev, error }));
      return false;
    }

    try {
      setState(prev => ({ ...prev, error: null }));

      // Log pre-connection diagnostics
      if (options.enableDebugLogs || options.enableVerboseLogging) {
        if (clientRef.current) {
          const preStatus = clientRef.current.getConnectionStatus();
          console.log('🔍 Pre-connection status:', {
            ...preStatus,
            credentialsReady,
            hasCredentials: preStatus.hasCredentials,
            credentialType: preStatus.credentialType,
            initializationStrategy: preStatus.initializationStrategy,
          });
        } else {
          console.warn('⚠️ Client is null during pre-connection diagnostics');
        }
      }

      const success = await clientRef.current.connect();

      if (success) {
        const latency = performance.now() - startTime;
        const postStatus = clientRef.current?.getConnectionStatus();

        if (postStatus) {
          setState(prev => ({
            ...prev,
            latency,
            currentTransport: postStatus.transport,
            connectionState: postStatus.connectionState,
            iceConnectionState: postStatus.iceConnectionState,
            toolRegistrationStatus: postStatus.toolRegistrationStatus
          }));

          if (options.enableDebugLogs || options.enableVerboseLogging) {
            console.log('✅ Connection successful:', {
              latency: Math.round(latency),
              transport: postStatus.transport,
              connectionState: postStatus.connectionState,
              toolRegistrationStatus: postStatus.toolRegistrationStatus,
              initializationStrategy: postStatus.initializationStrategy,
            });
          }
        } else {
          setState(prev => ({
            ...prev,
            latency
          }));
          if (options.enableDebugLogs || options.enableVerboseLogging) {
            console.warn('⚠️ Connection reported success but post-status is unavailable');
          }
        }

        retryCountRef.current = 0; // Reset retry count on success
        return true;
      } else {
        if (options.enableDebugLogs || options.enableVerboseLogging) {
          console.warn('❌ Connection returned false');
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Connection failed';

      if (options.enableDebugLogs || options.enableVerboseLogging) {
        console.error('💥 Connection error:', {
          message: errorMessage,
          retryCount,
          maxRetries,
          error
        });
      }

      const isCriticalError = errorMessage.includes('session.type') ||
                              errorMessage.includes('Unknown parameter') ||
                              errorMessage.includes('Client initialization failed');

      const isSessionTypeError = errorMessage.includes('session.type') || errorMessage.includes('Unknown parameter');
      const currentStrategy = clientRef.current?.getConnectionStatus().initializationStrategy ?? 'standard';
      const sessionTypeMessage = isSessionTypeError
        ? currentStrategy === 'standard'
          ? 'Connection failed due to SDK session.type bug. Enable the alternative initialization strategy and retry.'
          : 'Connection failed even with alternative initialization. Updating the SDK or retrying later may be required.'
        : null;

      if (sessionTypeMessage && (options.enableDebugLogs || options.enableVerboseLogging)) {
        console.warn('⚠️ Session.type issue detected:', {
          message: errorMessage,
          initializationStrategy: currentStrategy,
        });
      }

      if (isCriticalError && clientRef.current) {
        console.warn('⚠️ Critical error detected - client may need reinitialization');
        clientNeedsRecreationRef.current = true;
      }

      // Categorize errors for better retry decisions
      const isTransportError = errorMessage.toLowerCase().includes('webrtc') ||
                               errorMessage.toLowerCase().includes('negotiation') ||
                               errorMessage.toLowerCase().includes('ice');

      const isAuthError = errorMessage.toLowerCase().includes('auth') ||
                          errorMessage.toLowerCase().includes('token') ||
                          errorMessage.toLowerCase().includes('401') ||
                          errorMessage.toLowerCase().includes('403');

      // Don't retry authentication errors
      if (isAuthError) {
        setState(prev => ({
          ...prev,
          error: `Authentication error: ${errorMessage}`
        }));
        return false;
      }

      if (retryCount < maxRetries) {
        const delay = retryDelay * Math.pow(2, retryCount); // Exponential backoff with jitter
        const jitter = Math.random() * 500; // Add up to 500ms jitter
        const finalDelay = delay + jitter;

        setState(prev => ({
          ...prev,
          error: `Connection failed, retrying in ${Math.round(finalDelay)}ms... (${retryCount + 1}/${maxRetries}) - ${isTransportError ? 'Transport' : 'Network'} error${sessionTypeMessage ? ` | ${sessionTypeMessage}` : ''}`
        }));

        await new Promise(resolve => setTimeout(resolve, finalDelay));
        return connectWithRetry(retryCount + 1);
      } else {
        const finalError = `${errorMessage} (failed after ${maxRetries} retries)`;
        setState(prev => ({ ...prev, error: sessionTypeMessage ? `${finalError} | ${sessionTypeMessage}` : finalError }));

        if (options.enableDebugLogs || options.enableVerboseLogging) {
          console.error('🚫 Connection failed permanently:', finalError);
        }
        return false;
      }
    }
    return false;
  }, [initializeClient, options.apiKey, options.ephemeralToken, options.relayUrl, options.enableDebugLogs, options.enableVerboseLogging, credentialsReady]);

  // Public connect function
  const connect = useCallback(async (): Promise<boolean> => {
    return connectWithRetry(0);
  }, [connectWithRetry]);

  // Disconnect from the service
  const disconnect = useCallback(async (): Promise<void> => {
    if (clientRef.current) {
      await clientRef.current.disconnect();
    }
  }, []);

  const updateClientConfig = useCallback(async (config: Partial<ModernVoiceClientConfig>): Promise<void> => {
    // Log config update for debugging
    if (options.enableDebugLogs || options.enableVerboseLogging) {
      console.log('🔄 Updating client config:', {
        hasEphemeralToken: !!config.ephemeralToken,
        hasApiKey: !!config.apiKey,
        hasRelayUrl: !!config.relayUrl,
        useAlternativeInitialization: config.useAlternativeInitialization,
        ephemeralTokenPrefix: config.ephemeralToken ? config.ephemeralToken.substring(0, 8) : 'none',
        currentClientExists: !!clientRef.current,
        isInitialized: isInitializedRef.current
      });
    }

    // CRITICAL FIX: If client doesn't exist and we're providing credentials, force initialization
    if (!clientRef.current) {
      // Update the options first if credentials are being provided
      if (config.ephemeralToken || config.apiKey || Object.prototype.hasOwnProperty.call(config, 'useAlternativeInitialization')) {
        // Merge config into options for initialization
        Object.assign(options, config);

        // CRITICAL: Reset initialization flag so initializeClient() will actually run
        isInitializedRef.current = false;

        if (options.enableDebugLogs) {
          console.log('💾 Config merged into options, resetting init flag, initializing client...');
        }
      }
      initializeClient();

      // Wait a small delay to ensure state propagation
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    // If client now exists (or already existed), update its config
    if (clientRef.current) {
      if (Object.prototype.hasOwnProperty.call(config, 'useAlternativeInitialization')) {
        const requestedStrategy = config.useAlternativeInitialization ? 'alternative' : 'standard';
        const currentStrategy = clientRef.current.getConnectionStatus().initializationStrategy;
        if (requestedStrategy !== currentStrategy) {
          clientNeedsRecreationRef.current = true;
          if (options.enableDebugLogs || options.enableVerboseLogging) {
            console.log('🔄 Initialization strategy change detected, client will be recreated', {
              from: currentStrategy,
              to: requestedStrategy,
            });
          }
        }
      }

      clientRef.current.updateConfig(config);

      // Verify credentials were actually set
      const status = clientRef.current?.getConnectionStatus();
      if (options.enableDebugLogs) {
        if (status) {
          console.log('✅ Config updated, credential status:', {
            hasCredentials: status.hasCredentials,
            credentialType: status.credentialType
          });
        } else {
          console.warn('⚠️ Config update completed but status unavailable');
        }
      }

      // Update credential readiness state
      if (config.ephemeralToken || config.apiKey || config.relayUrl) {
        setCredentialsReady(true);
      }
    } else {
      console.warn('⚠️ Client still does not exist after config update attempt');
    }
  }, [initializeClient, options]);

  // Send a text message (useful for testing)
  const sendMessage = useCallback(async (message: string): Promise<void> => {
    if (!clientRef.current || !state.isConnected) {
      throw new Error('Not connected to voice service');
    }
    
    await clientRef.current.sendMessage(message);
  }, [state.isConnected]);

  // Clear tool calls
  const clearToolCalls = useCallback(() => {
    setState(prev => ({ ...prev, toolCalls: [] }));
  }, []);

  // Clear conversation using official RealtimeSession history management
  const clearConversation = useCallback(() => {
    // Clear local state
    setState(prev => ({
      ...prev,
      transcript: '',
      response: '',
      toolCalls: []
    }));

    // Clear session history using official SDK method
    if (clientRef.current) {
      clientRef.current.clearConversationHistory();
    }
  }, []);

  // Auto-connect if enabled
  useEffect(() => {
    if (options.autoConnect && !state.isConnected && !state.error) {
      connect();
    }
  }, [options.autoConnect, state.isConnected, state.error, connect]);

  // Initialize client on mount
  useEffect(() => {
    initializeClient();
    
    return () => {
      if (clientRef.current) {
        clientRef.current.disconnect();
        clientRef.current = null;
        isInitializedRef.current = false;
      }
    };
  }, [initializeClient]);

  return {
    // State
    isConnected: state.isConnected,
    isListening: state.isListening,
    error: state.error,
    transcript: state.transcript,
    response: state.response,
    toolCalls: state.toolCalls,
    interimTranscript: state.interimTranscript,
    voiceState: state.voiceState,
    currentlyExecutingTool: state.currentlyExecutingTool,

    // Actions
    connect,
    disconnect,
    sendMessage,
    clearToolCalls,
    clearConversation,
    updateClientConfig,

    // Utils
    getConnectionStatus: () => {
      if (!clientRef.current) {
        return {
          isConnected: false,
          transport: 'websocket' as const,
          connectionState: 'disconnected',
          iceConnectionState: 'disconnected',
          toolRegistrationStatus: 'pending' as const,
          hasCredentials: false,
          credentialType: undefined,
          patchApplied: false,
          initializationStrategy: options.useAlternativeInitialization ? 'alternative' as const : 'standard' as const,
        };
      }
      return clientRef.current.getConnectionStatus();
    },
    getConversationHistory: () => clientRef.current?.getConversationHistory() ?? [],
  };
}
