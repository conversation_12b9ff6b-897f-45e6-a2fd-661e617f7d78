import { useCallback, useEffect, useMemo, useState } from 'react';
// import type { RealtimeChannel } from '@supabase/supabase-js'; // Unused until realtime is re-enabled
import { supabase } from '@/lib/supabase';
import type {
  <PERSON>gingThresholds,
  LotAgingStatus,
  LotLocationData,
  LotLocationFilters,
} from '@/types/tempstick';

const WARNING_THRESHOLD_DAYS = 150; // ~5 months
const CRITICAL_THRESHOLD_DAYS = 180; // ~6 months

const DEFAULT_THRESHOLDS: AgingThresholds = {
  warningDays: WARNING_THRESHOLD_DAYS,
  criticalDays: CRITICAL_THRESHOLD_DAYS,
};

const POSITIVE_EVENT_TYPES = new Set([
  'receiving',
  'received',
  'restock',
  'replenishment',
  'physical_count',
  'adjustment_in',
]);

const NEGATIVE_EVENT_TYPES = new Set([
  'sale',
  'sales',
  'disposal',
  'waste',
  'shrinkage',
  'transfer_out',
  'adjustment_out',
]);

type InventoryEventRow = {
  id: string;
  event_type: string | null;
  name: string | null;
  quantity: number | null;
  unit: string | null;
  metadata: Record<string, unknown> | null;
  occurred_at: string | null;
  created_at: string | null;
  updated_at: string | null;
  product_id: string | null;
};

interface AggregatedLot {
  lotId: string | null;
  lotNumber: string;
  productName: string | null;
  quantity: number;
  unit: string | null;
  storageAreaId: string | null;
  storageAreaName: string | null;
  sensorId: string | null;
  sensorExternalId: string | null;
  createdAt: string | null;
  updatedAt: string | null;
  expiryDate: string | null;
}

const calculateAgeInDays = (createdAt: string | null | undefined): number => {
  if (!createdAt) return 0;
  const createdDate = new Date(createdAt);
  if (Number.isNaN(createdDate.getTime())) {
    return 0;
  }

  const diff = Date.now() - createdDate.getTime();
  return Math.max(0, Math.floor(diff / (1000 * 60 * 60 * 24)));
};

const isExpired = (expiryDate: string | null): boolean => {
  if (!expiryDate) return false;
  const timestamp = new Date(expiryDate).getTime();
  return Number.isFinite(timestamp) && timestamp < Date.now();
};

const resolveAgingStatus = (
  ageInDays: number,
  thresholds: AgingThresholds,
  expired: boolean
): LotAgingStatus => {
  if (expired || ageInDays >= thresholds.criticalDays) {
    return 'critical';
  }

  if (ageInDays >= thresholds.warningDays) {
    return 'warning';
  }

  return 'fresh';
};

const isApproachingExpiry = (
  ageInDays: number,
  expiryDate: string | null,
  thresholds: AgingThresholds
): boolean => {
  if (expiryDate) {
    const expiry = new Date(expiryDate);
    if (Number.isNaN(expiry.getTime())) {
      return ageInDays >= thresholds.warningDays;
    }

    const remaining = Math.floor((expiry.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
    return remaining <= 30 || ageInDays >= thresholds.warningDays;
  }

  return ageInDays >= thresholds.warningDays;
};

const shallowEqualFilters = (a: LotLocationFilters, b: LotLocationFilters): boolean => {
  const keys = new Set([...Object.keys(a), ...Object.keys(b)] as (keyof LotLocationFilters)[]);
  for (const key of keys) {
    const valA = a[key];
    const valB = b[key];

    if (Array.isArray(valA) && Array.isArray(valB)) {
      if (valA.length !== valB.length) return false;
      for (let index = 0; index < valA.length; index += 1) {
        if (valA[index] !== valB[index]) return false;
      }
      continue;
    }

    if (valA !== valB) {
      return false;
    }
  }

  return true;
};

export const useLotLocationData = (
  filters: LotLocationFilters = {},
  thresholds: AgingThresholds = DEFAULT_THRESHOLDS
) => {
  const [lotData, setLotData] = useState<LotLocationData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeFilters, setActiveFilters] = useState<LotLocationFilters>(filters);

  const fetchLotData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const { data: events, error: eventsError } = await supabase
        .from('inventory_events')
        .select(
          'id, event_type, name, quantity, unit, metadata, occurred_at, created_at, updated_at, product_id'
        )
        .order('occurred_at', { ascending: false })
        .limit(500);

      if (eventsError) {
        throw eventsError;
      }

      const lotsMap = new Map<string, AggregatedLot>();

      (events ?? []).forEach((row) => {
        const event = row as InventoryEventRow;
        const metadata = (event.metadata ?? {}) as Record<string, unknown>;

        const lotNumber = (metadata.lot_number ?? metadata.batch_number ?? metadata.tlc ?? null) as
          | string
          | null;

        if (!lotNumber || typeof lotNumber !== 'string') {
          return;
        }

        const storageAreaId = typeof metadata.storage_area_id === 'string' ? metadata.storage_area_id : null;
        const sensorInternalId = typeof metadata.sensor_id === 'string' && metadata.sensor_id.includes('-')
          ? metadata.sensor_id
          : typeof metadata.sensor_internal_id === 'string'
          ? metadata.sensor_internal_id
          : null;

        const key = [lotNumber, storageAreaId ?? 'unassigned', sensorInternalId ?? 'unsensed']
          .filter(Boolean)
          .join('|');

        const quantity = typeof event.quantity === 'number' ? event.quantity : Number(event.quantity ?? 0) || 0;
        const eventType = (event.event_type ?? '').toLowerCase();

        let entry = lotsMap.get(key);
        if (!entry) {
          entry = {
            lotId: typeof metadata.lot_id === 'string' ? metadata.lot_id : lotNumber,
            lotNumber,
            productName: (metadata.product_name as string) ?? event.name ?? null,
            quantity: 0,
            unit: (metadata.unit as string) ?? event.unit ?? null,
            storageAreaId,
            storageAreaName: (metadata.storage_area_name as string) ?? null,
            sensorId: sensorInternalId,
            sensorExternalId:
              (metadata.sensor_external_id as string) ??
              (metadata.tempstick_sensor_id as string) ??
              (typeof metadata.sensor_id === 'string' && !metadata.sensor_id.includes('-')
                ? (metadata.sensor_id as string)
                : null),
            createdAt:
              (metadata.produced_at as string) ??
              (metadata.created_at as string) ??
              event.occurred_at ??
              event.created_at,
            updatedAt: event.updated_at ?? event.occurred_at ?? event.created_at,
            expiryDate:
              (metadata.expiration_date as string) ??
              (metadata.expiry_date as string) ??
              (metadata.best_by_date as string) ??
              null,
          } satisfies AggregatedLot;

          lotsMap.set(key, entry);
        }

        if (eventType === 'physical_count') {
          entry.quantity = quantity;
        } else if (POSITIVE_EVENT_TYPES.has(eventType)) {
          entry.quantity += quantity;
        } else if (NEGATIVE_EVENT_TYPES.has(eventType)) {
          entry.quantity -= quantity;
        }

        const eventTimestamp = event.occurred_at ?? event.updated_at ?? event.created_at;
        if (eventTimestamp) {
          if (!entry.createdAt || eventTimestamp < entry.createdAt) {
            entry.createdAt = eventTimestamp;
          }
          if (!entry.updatedAt || eventTimestamp > entry.updatedAt) {
            entry.updatedAt = eventTimestamp;
          }
        }

        if (!entry.storageAreaName && typeof metadata.storage_area_name === 'string') {
          entry.storageAreaName = metadata.storage_area_name;
        }
      });

      const aggregatedLots = Array.from(lotsMap.values()).filter((lot) => lot.quantity > 0);

      const storageAreaIds = Array.from(
        new Set(
          aggregatedLots
            .map((lot) => lot.storageAreaId)
            .filter((value): value is string => typeof value === 'string' && value.length > 0)
        )
      );

      const sensorIds = Array.from(
        new Set(
          aggregatedLots
            .map((lot) => lot.sensorId)
            .filter((value): value is string => typeof value === 'string' && value.length > 0)
        )
      );

      let storageAreasById = new Map<string, { id: string; name: string | null }>();
      if (storageAreaIds.length > 0) {
        const { data: storageAreas, error: storageAreasError } = await supabase
          .from('storage_areas')
          .select('id, name')
          .in('id', storageAreaIds);

        if (storageAreasError) {
          throw storageAreasError;
        }

        storageAreasById = new Map(
          (storageAreas ?? []).map((area: { id: string; name: string | null }) => [area.id, area])
        );
      }

      let sensorsById = new Map<
        string,
        { id: string; sensor_id: string | null; name: string | null; storage_area_id: string | null }
      >();
      if (sensorIds.length > 0) {
        const { data: sensors, error: sensorsError } = await supabase
          .from('sensors')
          .select('id, sensor_id, name, storage_area_id')
          .in('id', sensorIds);

        if (sensorsError) {
          throw sensorsError;
        }

        sensorsById = new Map(
          (sensors ?? []).map(
            (sensor: { id: string; sensor_id: string | null; name: string | null; storage_area_id: string | null }) => [
              sensor.id,
              sensor,
            ]
          )
        );
      }

      const compiledLots: LotLocationData[] = aggregatedLots.map((lot) => {
        const storageArea = lot.storageAreaId ? storageAreasById.get(lot.storageAreaId) : null;
        const sensor = lot.sensorId ? sensorsById.get(lot.sensorId) : null;

        const createdAt = lot.createdAt ?? new Date().toISOString();
        const expiryDate = lot.expiryDate;
        const expired = isExpired(expiryDate);
        const ageInDays = calculateAgeInDays(createdAt);
        const agingStatus = resolveAgingStatus(ageInDays, thresholds, expired);
        const approachingExpiry = isApproachingExpiry(ageInDays, expiryDate, thresholds);

        return {
          lotId: lot.lotId ?? lot.lotNumber,
          lotNumber: lot.lotNumber,
          batchId: null,
          batchNumber: null,
          productName: lot.productName ?? 'Unknown product',
          quantity: Number.isFinite(lot.quantity) ? Math.max(0, Math.round(lot.quantity * 100) / 100) : null,
          storageAreaId: storageArea?.id ?? lot.storageAreaId ?? null,
          storageAreaName: storageArea?.name ?? lot.storageAreaName ?? null,
          sensorId: sensor?.id ?? lot.sensorId ?? null,
          sensorExternalId: sensor?.sensor_id ?? lot.sensorExternalId ?? null,
          createdAt,
          updatedAt: lot.updatedAt ?? createdAt,
          expiryDate,
          ageInDays,
          ageStatus: agingStatus,
          isApproachingExpiry: approachingExpiry,
          isExpired: expired,
        } satisfies LotLocationData;
      });

      setLotData(compiledLots);
    } catch (err) {
      console.error('Failed to fetch lot location data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load lot location data');
      setLotData([]);
    } finally {
      setLoading(false);
    }
  }, [thresholds]);

  const filteredLotData = useMemo(() => {
    return lotData.filter((lot) => {
      if (activeFilters.storageAreaIds && activeFilters.storageAreaIds.length > 0) {
        if (!lot.storageAreaId || !activeFilters.storageAreaIds.includes(lot.storageAreaId)) {
          return false;
        }
      }

      if (activeFilters.sensorIds && activeFilters.sensorIds.length > 0) {
        if (!lot.sensorId || !activeFilters.sensorIds.includes(lot.sensorId)) {
          return false;
        }
      }

      if (activeFilters.approachingExpiryWithinDays != null) {
        if (lot.expiryDate) {
          const expiry = new Date(lot.expiryDate).getTime();
          const within = expiry - Date.now();
          const daysRemaining = Math.floor(within / (1000 * 60 * 60 * 24));
          if (Number.isFinite(daysRemaining) && daysRemaining > activeFilters.approachingExpiryWithinDays) {
            return false;
          }
        } else if (lot.ageInDays < activeFilters.approachingExpiryWithinDays) {
          return false;
        }
      }

      if (activeFilters.includeExpired === false && lot.isExpired) {
        return false;
      }

      return true;
    });
  }, [activeFilters, lotData]);

  const getLotsInStorageArea = useCallback(
    (storageAreaId: string) => filteredLotData.filter((lot) => lot.storageAreaId === storageAreaId),
    [filteredLotData]
  );

  const getLotsApproachingExpiry = useCallback(
    (days: number = thresholds.warningDays) =>
      filteredLotData.filter((lot) => lot.isApproachingExpiry || lot.ageInDays >= days),
    [filteredLotData, thresholds.warningDays]
  );

  const refresh = useCallback(async () => {
    await fetchLotData();
  }, [fetchLotData]);

  useEffect(() => {
    fetchLotData();
  }, [fetchLotData]);

  useEffect(() => {
    // Realtime subscriptions disabled until lot/batch tracking schema is implemented
    return () => {
      // Cleanup function (no-op for now)
    };
  }, []);

  return {
    lots: filteredLotData,
    rawLots: lotData,
    loading,
    error,
    refresh,
    setFilters: setActiveFilters,
    thresholds,
    getLotsInStorageArea,
    getLotsApproachingExpiry,
  };
};

export default useLotLocationData;
