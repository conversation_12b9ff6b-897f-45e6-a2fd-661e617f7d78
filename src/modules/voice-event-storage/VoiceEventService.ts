import { createClient, type SupabaseClient } from '@supabase/supabase-js';

import { supabase } from '../../lib/supabase';
import { VoiceEvent, VoiceEventData, VoiceEventFilters } from '../../types/schema';
import { EventAudit, VoiceEventStatistics } from './types';

/**
 * Service class for managing voice events in the database
 * Handles CRUD operations, filtering, and audit trail management
 */
const warn = (message: string, details?: unknown) => {
  if (details) {
    console.warn(message, details);
    return;
  }

  console.warn(message);
};

const isServerEnvironment = (): boolean => typeof window === 'undefined';

const isRlsViolation = (error: unknown): boolean => {
  if (!error || typeof error !== 'object') {
    return false;
  }

  const candidate = error as { code?: string; message?: string };
  const code = candidate.code ?? '';
  const message = candidate.message ?? '';
  const normalizedMessage = message.toLowerCase();

  return (
    code === '42501' ||
    code === 'PGRST301' ||
    normalizedMessage.includes('rls') ||
    normalizedMessage.includes('row level security') ||
    normalizedMessage.includes('permission denied')
  );
};

let serviceRoleClient: SupabaseClient | null = null;

const getServiceRoleClient = (): SupabaseClient | null => {
  if (serviceRoleClient) {
    return serviceRoleClient;
  }

  if (!isServerEnvironment()) {
    return null;
  }

  if (typeof process === 'undefined' || !process.env) {
    warn('VoiceEventService: Unable to access process.env - service role fallback disabled');
    return null;
  }

  const supabaseUrl =
    process.env.SUPABASE_URL ??
    process.env.VITE_SUPABASE_URL ??
    process.env.NEXT_PUBLIC_SUPABASE_URL ??
    null;

  const serviceRoleKey =
    process.env.SUPABASE_SERVICE_ROLE_KEY ??
    process.env.VITE_SUPABASE_SERVICE_ROLE_KEY ??
    null;

  if (!supabaseUrl || !serviceRoleKey) {
    warn('VoiceEventService: Missing Supabase service role configuration - fallback disabled');
    return null;
  }

  try {
    serviceRoleClient = createClient(supabaseUrl, serviceRoleKey, {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      },
    });
    return serviceRoleClient;
  } catch (error) {
    console.error('VoiceEventService: Failed to initialize service role client', error);
    return null;
  }
};

export class VoiceEventService {
  /**
   * Create a new voice event in the database
   * @param eventData - The voice event data to save
   * @returns Promise<VoiceEvent> - The created event with ID
   */
  async createVoiceEvent(eventData: VoiceEventData): Promise<VoiceEvent> {
    try {
      // Validate required voice fields
      if (!eventData.voice_confidence_score || !eventData.raw_transcript) {
        throw new Error('Voice confidence score and raw transcript are required for voice events');
      }

      // CRITICAL FIX: Resolve product_id from product_name before saving
      let productId: string | null = null;
      if (eventData.product_name) {
        productId = await this.findOrCreateProduct(eventData.product_name);
      }

      // Prepare the event data for insertion
      const metadata = {
        ...(eventData.metadata ?? {}),
        product_name: eventData.product_name,
        vendor_name: eventData.vendor_name,
        customer_name: eventData.customer_name,
        condition: eventData.condition,
        temperature: eventData.temperature,
        temperature_unit: eventData.temperature_unit,
        processing_method: eventData.processing_method,
        quality_grade: eventData.quality_grade,
        market_form: eventData.market_form,
        session_id: eventData.session_id,
        created_by_voice: true,
        voice_processed: true,
        voice_processing_timestamp: new Date().toISOString(),
      };

      const createdByUserId = (() => {
        const candidate = metadata.created_by_user_id ?? metadata.user_id;
        return typeof candidate === 'string' && candidate.length > 0 ? candidate : null;
      })();

      const insertData = {
        event_type: eventData.event_type,
        product_id: productId, // Now properly resolved!
        name: eventData.product_name ?? 'Unknown Product', // Also set name field
        quantity: eventData.quantity,
        unit: eventData.unit,
        notes: eventData.notes,
        occurred_at: eventData.occurred_at ?? new Date().toISOString(),

        // Voice-specific fields
        voice_confidence_score: eventData.voice_confidence_score,
        voice_confidence_breakdown: eventData.voice_confidence_breakdown,
        raw_transcript: eventData.raw_transcript,
        audio_recording_url: eventData.audio_recording_url,
        created_by_voice: true,
        created_by_user_id: createdByUserId,

        // Metadata with voice-specific information
        metadata,
      } satisfies Record<string, unknown>;

      const { data, error } = await supabase
        .from('inventory_events')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        if (isRlsViolation(error)) {
          const serviceClient = getServiceRoleClient();

          if (serviceClient) {
            const fallbackMetadata = {
              ...metadata,
              fallback_source: 'voice_service_role_fallback',
              fallback_timestamp: new Date().toISOString(),
            } satisfies Record<string, unknown>;

            const fallbackPayload = {
              ...insertData,
              metadata: fallbackMetadata,
              created_by_user_id: createdByUserId,
            } satisfies Record<string, unknown>;

            const { data: fallbackData, error: fallbackError } = await serviceClient
              .from('inventory_events')
              .insert(fallbackPayload)
              .select()
              .single();

            if (!fallbackError && fallbackData) {
              console.warn('VoiceEventService: RLS violation detected. Service role fallback succeeded.');
              return this.mapDatabaseEventToVoiceEvent(fallbackData);
            }

            console.error('VoiceEventService: Service role fallback failed:', fallbackError ?? 'Unknown error');
          } else {
            warn('VoiceEventService: Service role client unavailable. Unable to bypass RLS for voice event creation.');
          }
        }

        console.error('Error creating voice event:', error);
        throw new Error(`Failed to create voice event: ${error.message}`);
      }

      return this.mapDatabaseEventToVoiceEvent(data);
    } catch (error) {
      console.error('VoiceEventService.createVoiceEvent error:', error);
      throw error;
    }
  }

  /**
   * Find existing product by name or create a new one
   * Moved from VoiceAssistant.tsx to service layer for better architecture
   * @param productName - Name of the product
   * @returns Promise<string> - Product ID
   */
  private async findOrCreateProduct(productName: string): Promise<string> {
    try {
      // First try to find existing product (case-insensitive search)
      const { data: existingProducts } = await supabase
        .from('Products')
        .select('id, name')
        .ilike('name', `%${productName}%`)
        .limit(1);

      if (existingProducts && existingProducts.length > 0) {
        console.log(`Found existing product: ${existingProducts[0].name} (${existingProducts[0].id})`);
        return existingProducts[0].id;
      }

      // Create new product if not found
      const { data: newProduct, error } = await supabase
        .from('Products')
        .insert({
          name: productName.charAt(0).toUpperCase() + productName.slice(1),
          category: 'Seafood', // Default category
          status: 'active',
        })
        .select('id')
        .single();

      if (error) {
        console.error('Error creating product:', error);
        throw new Error(`Failed to create product: ${error.message}`);
      }

      console.log(`Created new product: ${productName} (${newProduct.id})`);
      return newProduct.id;
    } catch (error) {
      console.error('Error in findOrCreateProduct:', error);
      // Return null rather than throwing to allow event creation to proceed
      // The product_name will still be in metadata
      return null as unknown as string;
    }
  }

  /**
   * Retrieve voice events with optional filtering
   * @param filters - Optional filters for the query
   * @returns Promise<VoiceEvent[]> - Array of voice events
   */
  async getVoiceEvents(filters?: VoiceEventFilters): Promise<VoiceEvent[]> {
    try {
      let query = supabase
        .from('inventory_events')
        .select(
          `
          id,
          event_type,
          name,
          quantity,
          unit,
          total_amount,
          unit_price,
          notes,
          voice_confidence_score,
          voice_confidence_breakdown,
          raw_transcript,
          audio_recording_url,
          created_by_voice,
          vendor_name,
          customer_name,
          condition_on_receipt,
          temperature_at_receipt,
          occurred_at,
          created_at,
          updated_at,
          metadata
        `
        )
        .eq('created_by_voice', true)
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters) {
        // Date range filter
        if (filters.dateRange) {
          query = query
            .gte('created_at', filters.dateRange.start)
            .lte('created_at', filters.dateRange.end);
        }

        // Event type filter
        if (filters.eventType && filters.eventType.length > 0) {
          query = query.in('event_type', filters.eventType);
        }

        // Confidence threshold filter
        if (filters.confidenceThreshold !== undefined) {
          query = query.gte('voice_confidence_score', filters.confidenceThreshold);
        }

        // Search query filter (searches in name and notes)
        if (filters.searchQuery) {
          query = query.or(
            `name.ilike.%${filters.searchQuery}%,notes.ilike.%${filters.searchQuery}%`
          );
        }

        // Created by user filter
        if (filters.createdBy) {
          query = query.eq('metadata->created_by_user_id', filters.createdBy);
        }
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching voice events:', error);
        throw new Error(`Failed to fetch voice events: ${error.message}`);
      }

      return (data ?? []).map((event) => this.mapDatabaseEventToVoiceEvent(event));
    } catch (error) {
      console.error('VoiceEventService.getVoiceEvents error:', error);
      throw error;
    }
  }

  /**
   * Update a voice event and create audit trail
   * @param eventId - ID of the event to update
   * @param updates - Partial event data to update
   * @param userId - ID of the user making the change
   * @param changeReason - Optional reason for the change
   * @returns Promise<VoiceEvent> - The updated event
   */
  async updateVoiceEvent(
    eventId: string,
    updates: Partial<VoiceEvent>,
    userId?: string,
    changeReason?: string
  ): Promise<VoiceEvent> {
    try {
      // First, get the current event for audit trail
      const { data: currentEvent, error: fetchError } = await supabase
        .from('inventory_events')
        .select('*')
        .eq('id', eventId)
        .single();

      if (fetchError) {
        throw new Error(`Failed to fetch current event: ${fetchError.message}`);
      }

      // Prepare update data
      const updateData: Record<string, unknown> = {};

      if (updates.product_name !== undefined) updateData.name = updates.product_name;
      if (updates.quantity !== undefined) updateData.quantity = updates.quantity;
      if (updates.unit !== undefined) updateData.unit = updates.unit;
      if (updates.notes !== undefined) updateData.notes = updates.notes;
      if (updates.voice_confidence_score !== undefined)
        updateData.voice_confidence_score = updates.voice_confidence_score;
      if (updates.voice_confidence_breakdown !== undefined)
        updateData.voice_confidence_breakdown = updates.voice_confidence_breakdown;
      if (updates.raw_transcript !== undefined) updateData.raw_transcript = updates.raw_transcript;
      if (updates.condition !== undefined) updateData.condition_on_receipt = updates.condition;
      if (updates.temperature !== undefined)
        updateData.temperature_at_receipt = updates.temperature;
      if (updates.vendor_name !== undefined) updateData.vendor_name = updates.vendor_name;
      if (updates.customer_name !== undefined) updateData.customer_name = updates.customer_name;
      if (updates.occurred_at !== undefined) updateData.occurred_at = updates.occurred_at;

      // Update the event
      const { data: updatedEvent, error: updateError } = await supabase
        .from('inventory_events')
        .update(updateData)
        .eq('id', eventId)
        .select()
        .single();

      if (updateError) {
        throw new Error(`Failed to update voice event: ${updateError.message}`);
      }

      // Create audit trail entries for changed fields
      await this.createAuditTrailEntries(eventId, currentEvent, updatedEvent, userId, changeReason);

      return this.mapDatabaseEventToVoiceEvent(updatedEvent);
    } catch (error) {
      console.error('VoiceEventService.updateVoiceEvent error:', error);
      throw error;
    }
  }

  /**
   * Delete a voice event
   * @param eventId - ID of the event to delete
   * @returns Promise<void>
   */
  async deleteVoiceEvent(eventId: string): Promise<void> {
    try {
      const { error } = await supabase.from('inventory_events').delete().eq('id', eventId);

      if (error) {
        throw new Error(`Failed to delete voice event: ${error.message}`);
      }
    } catch (error) {
      console.error('VoiceEventService.deleteVoiceEvent error:', error);
      throw error;
    }
  }

  /**
   * Get events that require quality review (low confidence)
   * @param confidenceThreshold - Threshold below which events need review (default: 0.7)
   * @returns Promise<VoiceEvent[]> - Array of events needing review
   */
  async getEventsForQualityReview(confidenceThreshold: number = 0.7): Promise<VoiceEvent[]> {
    try {
      const { data, error } = await supabase
        .from('inventory_events')
        .select(
          `
          id,
          event_type,
          name,
          quantity,
          unit,
          voice_confidence_score,
          voice_confidence_breakdown,
          raw_transcript,
          audio_recording_url,
          created_at,
          occurred_at,
          metadata
        `
        )
        .eq('created_by_voice', true)
        .lt('voice_confidence_score', confidenceThreshold)
        .order('created_at', { ascending: true }); // Oldest first for review queue

      if (error) {
        throw new Error(`Failed to fetch events for quality review: ${error.message}`);
      }

      return (data ?? []).map((event) => this.mapDatabaseEventToVoiceEvent(event));
    } catch (error) {
      console.error('VoiceEventService.getEventsForQualityReview error:', error);
      throw error;
    }
  }

  /**
   * Get audit trail for a specific event
   * @param eventId - ID of the event
   * @returns Promise<EventAudit[]> - Array of audit trail entries
   */
  async getEventAuditTrail(eventId: string): Promise<EventAudit[]> {
    try {
      const { data, error } = await supabase
        .from('event_audit_trail')
        .select('*')
        .eq('event_id', eventId)
        .order('changed_at', { ascending: false });

      if (error) {
        throw new Error(`Failed to fetch audit trail: ${error.message}`);
      }

      return data ?? [];
    } catch (error) {
      console.error('VoiceEventService.getEventAuditTrail error:', error);
      throw error;
    }
  }

  /**
   * Approve a voice event (mark as reviewed and approved)
   * @param eventId - ID of the event to approve
   * @param reviewerId - ID of the user approving the event
   * @returns Promise<void>
   */
  async approveVoiceEvent(eventId: string, reviewerId?: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('inventory_events')
        .update({
          metadata: {
            review_status: 'approved',
            reviewed_by: reviewerId,
            reviewed_at: new Date().toISOString(),
          },
        })
        .eq('id', eventId);

      if (error) {
        throw new Error(`Failed to approve voice event: ${error.message}`);
      }

      // Create audit trail entry
      await this.createAuditTrailEntries(
        eventId,
        { metadata: { review_status: 'pending' } },
        { metadata: { review_status: 'approved' } },
        reviewerId,
        'Event approved during quality review'
      );
    } catch (error) {
      console.error('VoiceEventService.approveVoiceEvent error:', error);
      throw error;
    }
  }

  /**
   * Reject a voice event (mark as reviewed and rejected)
   * @param eventId - ID of the event to reject
   * @param reason - Reason for rejection
   * @param reviewerId - ID of the user rejecting the event
   * @returns Promise<void>
   */
  async rejectVoiceEvent(eventId: string, reason: string, reviewerId?: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('inventory_events')
        .update({
          metadata: {
            review_status: 'rejected',
            rejection_reason: reason,
            reviewed_by: reviewerId,
            reviewed_at: new Date().toISOString(),
          },
        })
        .eq('id', eventId);

      if (error) {
        throw new Error(`Failed to reject voice event: ${error.message}`);
      }

      // Create audit trail entry
      await this.createAuditTrailEntries(
        eventId,
        { metadata: { review_status: 'pending' } },
        { metadata: { review_status: 'rejected', rejection_reason: reason } },
        reviewerId,
        `Event rejected during quality review: ${reason}`
      );
    } catch (error) {
      console.error('VoiceEventService.rejectVoiceEvent error:', error);
      throw error;
    }
  }

  /**
   * Batch approve multiple voice events
   * @param eventIds - Array of event IDs to approve
   * @param reviewerId - ID of the user approving the events
   * @returns Promise<void>
   */
  async batchApproveVoiceEvents(eventIds: string[], reviewerId?: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('inventory_events')
        .update({
          metadata: {
            review_status: 'approved',
            reviewed_by: reviewerId,
            reviewed_at: new Date().toISOString(),
          },
        })
        .in('id', eventIds);

      if (error) {
        throw new Error(`Failed to batch approve voice events: ${error.message}`);
      }

      // Create audit trail entries for each event
      for (const eventId of eventIds) {
        await this.createAuditTrailEntries(
          eventId,
          { metadata: { review_status: 'pending' } },
          { metadata: { review_status: 'approved' } },
          reviewerId,
          'Event approved during batch quality review'
        );
      }
    } catch (error) {
      console.error('VoiceEventService.batchApproveVoiceEvents error:', error);
      throw error;
    }
  }

  /**
   * Batch reject multiple voice events
   * @param eventIds - Array of event IDs to reject
   * @param reason - Reason for rejection
   * @param reviewerId - ID of the user rejecting the events
   * @returns Promise<void>
   */
  async batchRejectVoiceEvents(
    eventIds: string[],
    reason: string,
    reviewerId?: string
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('inventory_events')
        .update({
          metadata: {
            review_status: 'rejected',
            rejection_reason: reason,
            reviewed_by: reviewerId,
            reviewed_at: new Date().toISOString(),
          },
        })
        .in('id', eventIds);

      if (error) {
        throw new Error(`Failed to batch reject voice events: ${error.message}`);
      }

      // Create audit trail entries for each event
      for (const eventId of eventIds) {
        await this.createAuditTrailEntries(
          eventId,
          { metadata: { review_status: 'pending' } },
          { metadata: { review_status: 'rejected', rejection_reason: reason } },
          reviewerId,
          `Event rejected during batch quality review: ${reason}`
        );
      }
    } catch (error) {
      console.error('VoiceEventService.batchRejectVoiceEvents error:', error);
      throw error;
    }
  }

  /**
   * Get voice event statistics
   * @returns Promise with various statistics about voice events
   */
  async getVoiceEventStatistics(): Promise<VoiceEventStatistics> {
    try {
      const { data, error } = await supabase
        .from('inventory_events')
        .select('voice_confidence_score')
        .eq('created_by_voice', true);

      if (error) {
        throw new Error(`Failed to fetch voice event statistics: ${error.message}`);
      }

      const events = data ?? [];
      const totalEvents = events.length;

      if (totalEvents === 0) {
        return {
          totalEvents: 0,
          highConfidenceEvents: 0,
          mediumConfidenceEvents: 0,
          lowConfidenceEvents: 0,
          averageConfidence: 0,
          eventsNeedingReview: 0,
        };
      }

      const highConfidenceEvents = events.filter((e) => e.voice_confidence_score >= 0.9).length;
      const mediumConfidenceEvents = events.filter(
        (e) => e.voice_confidence_score >= 0.7 && e.voice_confidence_score < 0.9
      ).length;
      const lowConfidenceEvents = events.filter((e) => e.voice_confidence_score < 0.7).length;
      const averageConfidence =
        events.reduce((sum, e) => sum + (e.voice_confidence_score ?? 0), 0) / totalEvents;

      return {
        totalEvents,
        highConfidenceEvents,
        mediumConfidenceEvents,
        lowConfidenceEvents,
        averageConfidence: Math.round(averageConfidence * 100) / 100,
        eventsNeedingReview: lowConfidenceEvents,
      };
    } catch (error) {
      console.error('VoiceEventService.getVoiceEventStatistics error:', error);
      throw error;
    }
  }

  /**
   * Private method to create audit trail entries for changed fields
   */
  private async createAuditTrailEntries(
    eventId: string,
    oldEvent: unknown,
    newEvent: unknown,
    userId?: string,
    changeReason?: string
  ): Promise<void> {
    const auditEntries: unknown[] = [];
    const fieldsToTrack = [
      'name',
      'quantity',
      'unit',
      'notes',
      'voice_confidence_score',
      'voice_confidence_breakdown',
      'raw_transcript',
      'condition_on_receipt',
      'temperature_at_receipt',
      'vendor_name',
      'customer_name',
      'occurred_at',
    ];

    for (const field of fieldsToTrack) {
      if (oldEvent[field] !== newEvent[field]) {
        auditEntries.push({
          event_id: eventId,
          field_name: field,
          old_value: oldEvent[field],
          new_value: newEvent[field],
          changed_by: userId,
          change_reason: changeReason,
          change_source: 'manual',
        });
      }
    }

    if (auditEntries.length > 0) {
      const { error } = await supabase.from('event_audit_trail').insert(auditEntries);

      if (error) {
        console.error('Error creating audit trail entries:', error);
        // Don't throw here - audit trail failure shouldn't prevent the update
      }
    }
  }

  /**
   * Private method to map database event to VoiceEvent interface
   */
  private mapDatabaseEventToVoiceEvent(dbEvent: unknown): VoiceEvent {
    const event = dbEvent as Record<string, unknown>;
    const metadata = (event.metadata as Record<string, unknown>) ?? {};

    return {
      id: event.id as string,
      event_type: event.event_type as VoiceEvent['event_type'],
      product_name:
        (metadata.product_name as string) ?? (event.name as string) ?? 'Unknown Product',
      quantity: event.quantity as number,
      unit: (event.unit as string) ?? 'lbs',
      vendor_name: metadata.vendor_name as string,
      customer_name: metadata.customer_name as string,
      condition: metadata.condition as string,
      temperature: metadata.temperature as number,
      temperature_unit: (metadata.temperature_unit as string) ?? 'fahrenheit',
      processing_method: metadata.processing_method as string,
      quality_grade: metadata.quality_grade as string,
      market_form: metadata.market_form as string,
      notes: event.notes as string,
      occurred_at: event.occurred_at as string,

      // Voice-specific fields
      voice_confidence_score: (event.voice_confidence_score as number) ?? 0,
      voice_confidence_breakdown: (event.voice_confidence_breakdown as Record<string, number>) ?? {
        product_match: 0,
        quantity_extraction: 0,
        vendor_match: 0,
        overall: 0,
      },
      raw_transcript: (event.raw_transcript as string) ?? '',
      audio_recording_url: event.audio_recording_url as string,
      created_by_voice: (event.created_by_voice as boolean) ?? false,

      // Metadata
      created_at: event.created_at as string,
      updated_at: event.updated_at as string,
      created_by: metadata.created_by_user_id as string,
      last_modified_by: metadata.last_modified_by as string,
      session_id: metadata.session_id as string,
      metadata,
    };
  }
}

// Export a singleton instance
export const voiceEventService = new VoiceEventService();

export const __testing__ = {
  resetServiceRoleClient(): void {
    serviceRoleClient = null;
  },
  setServiceRoleClient(client: SupabaseClient | null): void {
    serviceRoleClient = client;
  },
  isRlsViolation,
};
