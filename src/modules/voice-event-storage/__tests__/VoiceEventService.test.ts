import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import type { SupabaseClient } from '@supabase/supabase-js';
import { VoiceEventService, __testing__ } from '../VoiceEventService';
import { supabase } from '../../../lib/supabase';
import { VoiceEventData, VoiceEventFilters } from '../types';

// Mock the supabase client
vi.mock('../../../lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: vi.fn(),
      select: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      eq: vi.fn(),
      gte: vi.fn(),
      lte: vi.fn(),
      in: vi.fn(),
      or: vi.fn(),
      order: vi.fn(),
      lt: vi.fn(),
      single: vi.fn(),
    })),
  },
}));

describe('VoiceEventService', () => {
  let service: VoiceEventService;
  let mockSupabase: any;
  let findProductSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    service = new VoiceEventService();
    mockSupabase = {
      insert: vi.fn(),
      select: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      eq: vi.fn(),
      gte: vi.fn(),
      lte: vi.fn(),
      in: vi.fn(),
      or: vi.fn(),
      order: vi.fn(),
      lt: vi.fn(),
      single: vi.fn(),
    };

    (supabase.from as any).mockImplementation((tableName: string) => {
      if (tableName === 'event_audit_trail') {
        return {
          insert: vi.fn().mockReturnValue({
            select: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({ data: null, error: null }),
            }),
          }),
        };
      }
      return mockSupabase;
    });

    findProductSpy = vi
      .spyOn(VoiceEventService.prototype as unknown as { findOrCreateProduct: () => Promise<string> }, 'findOrCreateProduct')
      .mockResolvedValue('mock-product-id');
  });

  afterEach(() => {
    vi.clearAllMocks();
    __testing__.resetServiceRoleClient();
    findProductSpy.mockRestore();
  });

  describe('createVoiceEvent', () => {
    it('should create a voice event successfully', async () => {
      const mockEventData: VoiceEventData & {
        voice_confidence_score: number;
        voice_confidence_breakdown: any;
        raw_transcript: string;
      } = {
        event_type: 'receiving',
        product_name: 'Salmon',
        quantity: 50,
        unit: 'lbs',
        voice_confidence_score: 0.95,
        voice_confidence_breakdown: {
          product_match: 0.98,
          quantity_extraction: 0.92,
          vendor_match: 0.95,
          overall: 0.95,
        },
        raw_transcript: 'Received 50 pounds of salmon',
      };

      const mockResponse = {
        id: '123',
        event_type: 'receiving',
        name: 'Salmon',
        quantity: 50,
        unit: 'lbs',
        voice_confidence_score: 0.95,
        voice_confidence_breakdown: mockEventData.voice_confidence_breakdown,
        raw_transcript: mockEventData.raw_transcript,
        created_by_voice: true,
        created_at: '2024-01-01T00:00:00Z',
      };

      mockSupabase.insert.mockReturnValue({
        select: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockResponse, error: null }),
        }),
      });

      const result = await service.createVoiceEvent(mockEventData);

      expect(supabase.from).toHaveBeenCalledWith('inventory_events');
      expect(result.id).toBe('123');
      expect(result.product_name).toBe('Salmon');
      expect(result.voice_confidence_score).toBe(0.95);
    });

    it('should handle creation errors', async () => {
      const mockEventData = {
        event_type: 'receiving',
        product_name: 'Salmon',
        quantity: 50,
        unit: 'lbs',
        voice_confidence_score: 0.95,
        voice_confidence_breakdown: {},
        raw_transcript: 'test',
      };

      mockSupabase.insert.mockReturnValue({
        select: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: null, error: new Error('Database error') }),
        }),
      });

      await expect(service.createVoiceEvent(mockEventData)).rejects.toThrow(
        'Failed to create voice event'
      );
    });

    it('should fallback to service role client on RLS violations', async () => {
      const originalWindow = (globalThis as Record<string, unknown>).window;
      // Simulate server environment where window is undefined
      // @ts-expect-error intentionally deleting window for test
      delete (globalThis as Record<string, unknown>).window;

      const mockEventData: VoiceEventData & {
        voice_confidence_score: number;
        voice_confidence_breakdown: any;
        raw_transcript: string;
      } = {
        event_type: 'receiving',
        product_name: 'Halibut',
        quantity: 10,
        unit: 'lbs',
        voice_confidence_score: 0.92,
        voice_confidence_breakdown: {
          product_match: 0.95,
          quantity_extraction: 0.9,
          vendor_match: 0.88,
          overall: 0.92,
        },
        raw_transcript: 'Received 10 pounds of halibut',
      };

      const rlsError = { code: '42501', message: 'new row violates row-level security policy' };
      const mockSingle = vi.fn().mockResolvedValue({ data: null, error: rlsError });
      const mockSelect = vi.fn().mockReturnValue({ single: mockSingle });
      mockSupabase.insert.mockReturnValue({ select: mockSelect });

      const fallbackResponse = {
        id: 'fallback-event-id',
        event_type: 'receiving',
        name: 'Halibut',
        quantity: 10,
        unit: 'lbs',
        created_by_voice: true,
        created_at: '2025-10-04T04:12:00Z',
        metadata: {
          fallback_source: 'voice_service_role_fallback',
        },
      };

      const fallbackSingle = vi.fn().mockResolvedValue({ data: fallbackResponse, error: null });
      const fallbackSelect = vi.fn().mockReturnValue({ single: fallbackSingle });
      const fallbackInsert = vi.fn().mockReturnValue({ select: fallbackSelect });
      const fallbackFrom = vi.fn().mockReturnValue({ insert: fallbackInsert });

      __testing__.setServiceRoleClient({
        from: fallbackFrom,
      } as unknown as SupabaseClient);

      try {
        const result = await service.createVoiceEvent(mockEventData);

        expect(mockSupabase.insert).toHaveBeenCalled();
        expect(fallbackFrom).toHaveBeenCalledWith('inventory_events');
        expect(result.id).toBe('fallback-event-id');
        expect(result.product_name).toBe('Halibut');
      } finally {
        if (originalWindow !== undefined) {
          (globalThis as Record<string, unknown>).window = originalWindow;
        } else {
          // @ts-expect-error restoring undefined window state
          delete (globalThis as Record<string, unknown>).window;
        }
      }
    });
  });

  describe('getVoiceEvents', () => {
    it('should retrieve voice events without filters', async () => {
      const mockEvents = [
        {
          id: '123',
          event_type: 'receiving',
          name: 'Salmon',
          quantity: 50,
          unit: 'lbs',
          voice_confidence_score: 0.95,
          voice_confidence_breakdown: {},
          raw_transcript: 'test',
          created_by_voice: true,
          created_at: '2024-01-01T00:00:00Z',
        },
      ];

      mockSupabase.select.mockReturnValue({
        eq: vi.fn().mockReturnValue({
          order: vi.fn().mockReturnValue({
            data: mockEvents,
            error: null,
          }),
        }),
      });

      const result = await service.getVoiceEvents();

      expect(supabase.from).toHaveBeenCalledWith('inventory_events');
      expect(result).toHaveLength(1);
      expect(result[0].product_name).toBe('Salmon');
    });

    it('should apply date range filter', async () => {
      const filters: VoiceEventFilters = {
        dateRange: {
          start: '2024-01-01',
          end: '2024-12-31',
        },
      };

      const mockEvents = [
        {
          id: '123',
          event_type: 'receiving',
          name: 'Salmon',
          quantity: 50,
          unit: 'lbs',
          voice_confidence_score: 0.95,
          created_at: '2024-06-01T00:00:00Z',
        },
      ];

      const mockChain = {
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        gte: vi.fn().mockReturnThis(),
        lte: vi.fn().mockReturnThis(),
        data: mockEvents,
        error: null,
      };

      mockSupabase.select.mockReturnValue(mockChain);

      const result = await service.getVoiceEvents(filters);

      expect(mockChain.gte).toHaveBeenCalledWith('created_at', '2024-01-01');
      expect(mockChain.lte).toHaveBeenCalledWith('created_at', '2024-12-31');
      expect(result).toHaveLength(1);
    });

    it('should apply confidence threshold filter', async () => {
      const filters: VoiceEventFilters = {
        confidenceThreshold: 0.8,
      };

      const mockEvents = [
        {
          id: '123',
          event_type: 'receiving',
          name: 'Salmon',
          quantity: 50,
          unit: 'lbs',
          voice_confidence_score: 0.85,
          created_at: '2024-06-01T00:00:00Z',
        },
      ];

      const mockChain = {
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        gte: vi.fn().mockReturnThis(),
        data: mockEvents,
        error: null,
      };

      mockSupabase.select.mockReturnValue(mockChain);

      const result = await service.getVoiceEvents(filters);

      expect(mockChain.gte).toHaveBeenCalledWith('voice_confidence_score', 0.8);
      expect(result).toHaveLength(1);
    });
  });

  describe('updateVoiceEvent', () => {
    it('should update a voice event successfully', async () => {
      const eventId = '123';
      const updates = {
        product_name: 'Updated Salmon',
        quantity: 75,
      };

      const mockCurrentEvent = {
        id: '123',
        name: 'Salmon',
        quantity: 50,
        unit: 'lbs',
      };

      const mockUpdatedEvent = {
        id: '123',
        name: 'Updated Salmon',
        quantity: 75,
        unit: 'lbs',
        created_at: '2024-01-01T00:00:00Z',
      };

      // Mock getting current event
      mockSupabase.select.mockReturnValueOnce({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockCurrentEvent, error: null }),
        }),
      });

      // Mock update
      mockSupabase.update.mockReturnValue({
        eq: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({ data: mockUpdatedEvent, error: null }),
          }),
        }),
      });

      const result = await service.updateVoiceEvent(eventId, updates);

      expect(result.product_name).toBe('Updated Salmon');
      expect(result.quantity).toBe(75);
    });
  });

  describe('deleteVoiceEvent', () => {
    it('should delete a voice event successfully', async () => {
      const eventId = '123';

      mockSupabase.delete.mockReturnValue({
        eq: vi.fn().mockReturnValue({
          error: null,
        }),
      });

      await service.deleteVoiceEvent(eventId);

      expect(supabase.from).toHaveBeenCalledWith('inventory_events');
    });

    it('should handle deletion errors', async () => {
      const eventId = '123';

      mockSupabase.delete.mockReturnValue({
        eq: vi.fn().mockReturnValue({
          error: new Error('Deletion failed'),
        }),
      });

      await expect(service.deleteVoiceEvent(eventId)).rejects.toThrow(
        'Failed to delete voice event'
      );
    });
  });

  describe('getEventsForQualityReview', () => {
    it('should retrieve events needing quality review', async () => {
      const mockEvents = [
        {
          id: '123',
          event_type: 'receiving',
          name: 'Salmon',
          quantity: 50,
          unit: 'lbs',
          voice_confidence_score: 0.65,
          created_at: '2024-01-01T00:00:00Z',
        },
      ];

      const mockChain = {
        eq: vi.fn().mockReturnThis(),
        lt: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        data: mockEvents,
        error: null,
      };

      mockSupabase.select.mockReturnValue(mockChain);

      const result = await service.getEventsForQualityReview();

      expect(mockChain.lt).toHaveBeenCalledWith('voice_confidence_score', 0.7);
      expect(result).toHaveLength(1);
      expect(result[0].voice_confidence_score).toBe(0.65);
    });

    it('should use custom confidence threshold', async () => {
      const mockChain = {
        eq: vi.fn().mockReturnThis(),
        lt: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        data: [],
        error: null,
      };

      mockSupabase.select.mockReturnValue(mockChain);

      await service.getEventsForQualityReview(0.9);

      expect(mockChain.lt).toHaveBeenCalledWith('voice_confidence_score', 0.9);
    });
  });

  describe('getVoiceEventStatistics', () => {
    it('should return correct statistics for events', async () => {
      const mockData = [
        { voice_confidence_score: 0.95 },
        { voice_confidence_score: 0.85 },
        { voice_confidence_score: 0.75 },
        { voice_confidence_score: 0.65 },
        { voice_confidence_score: 0.55 },
      ];

      mockSupabase.select.mockReturnValue({
        eq: vi.fn().mockReturnValue({
          data: mockData,
          error: null,
        }),
      });

      const result = await service.getVoiceEventStatistics();

      expect(result.totalEvents).toBe(5);
      expect(result.highConfidenceEvents).toBe(1); // >= 0.9
      expect(result.mediumConfidenceEvents).toBe(2); // >= 0.7 and < 0.9
      expect(result.lowConfidenceEvents).toBe(2); // < 0.7
      expect(result.averageConfidence).toBe(0.75);
      expect(result.eventsNeedingReview).toBe(2);
    });

    it('should handle empty results', async () => {
      mockSupabase.select.mockReturnValue({
        eq: vi.fn().mockReturnValue({
          data: [],
          error: null,
        }),
      });

      const result = await service.getVoiceEventStatistics();

      expect(result.totalEvents).toBe(0);
      expect(result.averageConfidence).toBe(0);
    });
  });

  describe('approveVoiceEvent', () => {
    it('should approve a voice event successfully', async () => {
      const eventId = '123';
      const reviewerId = 'user-456';

      mockSupabase.update.mockReturnValue({
        eq: vi.fn().mockReturnValue({
          error: null,
        }),
      });

      await service.approveVoiceEvent(eventId, reviewerId);

      expect(supabase.from).toHaveBeenCalledWith('inventory_events');
    });
  });

  describe('rejectVoiceEvent', () => {
    it('should reject a voice event with reason', async () => {
      const eventId = '123';
      const reason = 'Incorrect quantity detected';
      const reviewerId = 'user-456';

      mockSupabase.update.mockReturnValue({
        eq: vi.fn().mockReturnValue({
          error: null,
        }),
      });

      await service.rejectVoiceEvent(eventId, reason, reviewerId);

      expect(supabase.from).toHaveBeenCalledWith('inventory_events');
    });
  });

  describe('batch operations', () => {
    it('should batch approve events', async () => {
      const eventIds = ['123', '456'];
      const reviewerId = 'user-789';

      mockSupabase.update.mockReturnValue({
        in: vi.fn().mockReturnValue({
          error: null,
        }),
      });

      await service.batchApproveVoiceEvents(eventIds, reviewerId);

      expect(supabase.from).toHaveBeenCalledWith('inventory_events');
    });

    it('should batch reject events', async () => {
      const eventIds = ['123', '456'];
      const reason = 'Quality issues';
      const reviewerId = 'user-789';

      mockSupabase.update.mockReturnValue({
        in: vi.fn().mockReturnValue({
          error: null,
        }),
      });

      await service.batchRejectVoiceEvents(eventIds, reason, reviewerId);

      expect(supabase.from).toHaveBeenCalledWith('inventory_events');
    });
  });
});
