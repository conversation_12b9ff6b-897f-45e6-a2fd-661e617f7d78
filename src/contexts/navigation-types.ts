/**
 * Navigation types for the Seafood Manager application
 */

export type ActiveView =
  | 'Dashboard'
  | 'Inventory'
  | 'Vendors'
  | 'Customers'
  | 'Analytics'
  | 'Messages'
  | 'Settings'
  | 'Import'
  | 'Voice Input'
  | 'Voice Management'
  | 'Events' // dedicated events table view
  | 'HACCP Events' // legacy entry point to HACCP Events (maps to Events)
  | 'HACCP: Batches'
  | 'HACCP: Events'
  | 'HACCP: Calendar'
  | 'Temperature Monitoring' // TempStick dashboard
  | 'Sensor Management' // Sensor configuration and management
  | 'Temperature: Dashboard' // Alternative naming for submenu
  | 'Temperature: Sensors' // Alternative naming for submenu
  | 'Temperature: Historical' // Historical temperature data view
  | 'Temperature: Sync' // Real-time sync interface
  | 'Temperature: Calibration' // Calibration management
  | 'Temperature: Live' // Live temperature monitoring
  | 'Real-Time Sync' // Alternative naming for temperature sync
  | 'TempStick Test' // TempStick API testing interface
  | 'TempStick Dashboard' // TempStick unified dashboard
  | 'TempStick Integration'; // TempStick integration dashboard
