import { createContext, useState, useEffect, useContext, ReactNode } from 'react';
import { getViewFromPath } from './navigation-utils';
import { ActiveView } from './navigation-types';

interface NavigationContextType {
  activeView: ActiveView;
  setActiveView: (view: ActiveView) => void;
  viewFilters: Record<string, unknown>;
  setViewFilter: (key: string, value: unknown) => void;
  clearViewFilters: () => void;
}

// eslint-disable-next-line react-refresh/only-export-components
export const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

export function NavigationProvider({ children }: { children: ReactNode }) {
  const [activeView, setActiveView] = useState<ActiveView>(() =>
    getViewFromPath(window.location.pathname)
  );
  const [viewFilters, setViewFilters] = useState<Record<string, unknown>>({});

  // Listen for URL changes and update activeView accordingly
  useEffect(() => {
    const handlePopState = () => {
      setActiveView(getViewFromPath(window.location.pathname));
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  const setViewFilter = (key: string, value: unknown) => {
    setViewFilters((prev) => ({ ...prev, [key]: value }));
  };

  const clearViewFilters = () => {
    setViewFilters({});
  };

  return (
    <NavigationContext.Provider
      value={{
        activeView,
        setActiveView,
        viewFilters,
        setViewFilter,
        clearViewFilters,
      }}
    >
      {children}
    </NavigationContext.Provider>
  );
}

export function useNavigationContext(): NavigationContextType {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error('useNavigationContext must be used within a NavigationProvider');
  }
  return context;
}
