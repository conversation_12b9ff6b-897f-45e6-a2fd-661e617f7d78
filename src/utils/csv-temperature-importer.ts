/**
 * CSV Temperature Data Importer
 *
 * Imports historical temperature data from CSV files into the temperature_readings table.
 * Supports various CSV formats and handles data validation, deduplication, and user association.
 */

import { supabase } from '@/lib/supabase';
// import type { Database } from '@/types/database';
import type { User } from '@supabase/supabase-js';

// type TemperatureReading = Database['public']['Tables']['temperature_readings']['Insert'];
interface TemperatureReading {
  sensor_id: string;
  temp_celsius: number;
  humidity?: number | null;
  recorded_at: string;
  user_id?: string;
}

interface CSVImportOptions {
  /** Map CSV column names to expected fields */
  columnMapping?: {
    timestamp?: string | number;
    temperature?: string | number;
    humidity?: string | number;
    battery?: string | number;
    rssi?: string | number;
    sensorId?: string;
    sensorName?: string;
  };
  /** Temperature unit in CSV ('celsius' | 'fahrenheit') */
  temperatureUnit?: 'celsius' | 'fahrenheit';
  /** Skip first row if it contains headers */
  hasHeaders?: boolean;
  /** Default sensor to associate readings with */
  defaultSensorId?: string;
  /** Batch size for database inserts */
  batchSize?: number;
}

interface CSVImportResult {
  success: boolean;
  totalRows: number;
  importedRows: number;
  skippedRows: number;
  errors: string[];
  duplicates: number;
  duration: number;
}

export class CSVTemperatureImporter {
  private user: User;

  constructor(user: User) {
    this.user = user;
  }

  /**
   * Parse CSV content and import temperature readings
   */
  async importCSV(csvContent: string, options: CSVImportOptions = {}): Promise<CSVImportResult> {
    const startTime = Date.now();
    const result: CSVImportResult = {
      success: false,
      totalRows: 0,
      importedRows: 0,
      skippedRows: 0,
      errors: [],
      duplicates: 0,
      duration: 0,
    };

    try {
      // Validate user authentication
      if (!this.user?.id) {
        throw new Error('User authentication required for CSV import');
      }

      console.log('🔄 Starting CSV temperature import...');
      console.log('👤 User:', `${this.user.id.substring(0, 8)}...`);
      console.log('⚙️  Options:', JSON.stringify(options, null, 2));

      // Parse CSV content
      const rows = this.parseCSVContent(csvContent, options);
      result.totalRows = rows.length;

      if (rows.length === 0) {
        throw new Error('No data rows found in CSV');
      }

      console.log(`📊 Parsed ${rows.length} rows from CSV`);

      // Get sensor mappings for the user
      const sensorMappings = await this.getSensorMappings();
      console.log('🔍 Found', Object.keys(sensorMappings).length, 'sensors for user');

      // Process rows in batches
      const batchSize = options.batchSize ?? 100;
      const readings: TemperatureReading[] = [];

      for (let i = 0; i < rows.length; i++) {
        try {
          const reading = await this.processRow(rows[i], options, sensorMappings);
          if (reading) {
            readings.push(reading);
          } else {
            result.skippedRows++;
          }
        } catch (error) {
          result.skippedRows++;
          result.errors.push(
            `Row ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`
          );
        }

        // Insert batch
        if (readings.length >= batchSize || i === rows.length - 1) {
          if (readings.length > 0) {
            const batchResult = await this.insertBatch(readings);
            result.importedRows += batchResult.inserted;
            result.duplicates += batchResult.duplicates;
            readings.length = 0; // Clear array
          }
        }
      }

      result.duration = Date.now() - startTime;
      result.success = result.errors.length === 0 || result.importedRows > 0;

      console.log(`✅ CSV import completed in ${result.duration}ms`);
      console.log(`   📊 ${result.importedRows}/${result.totalRows} rows imported`);
      console.log(`   ⚠️  ${result.skippedRows} rows skipped`);
      console.log(`   🔄 ${result.duplicates} duplicates found`);

      return result;
    } catch (error) {
      result.duration = Date.now() - startTime;
      result.errors.push(error instanceof Error ? error.message : 'Unknown import error');
      console.error('❌ CSV import failed:', error);
      return result;
    }
  }

  /**
   * Parse CSV content into rows
   */
  private parseCSVContent(csvContent: string, options: CSVImportOptions): string[][] {
    const lines = csvContent.trim().split('\n');
    const rows: string[][] = [];

    let startIndex = 0;
    const sensorInfo: { name?: string; sensorId?: string } = {};

    // TempStick CSV format detection and parsing:
    // Line 1: "Sensor: Name (ID: 12345),Created: ...,Start: ...,End: ..."
    // Line 2: Empty line
    // Line 3: Headers: "Timestamp,Temperature,Humidity,Heat Index,Dew Point,Battery,Connection Quality (RSSI),Seconds to Connect,"
    // Line 4+: Data rows

    if (lines.length > 0) {
      const firstLine = lines[0];

      // Check for TempStick format: "Sensor: ... (ID: 12345)"
      const tempstickMatch = firstLine.match(/^Sensor:\s*(.+?)\s*\(ID:\s*(\d+)\)/);
      if (tempstickMatch) {
        sensorInfo.name = tempstickMatch[1].trim();
        sensorInfo.sensorId = tempstickMatch[2];

        console.log('🔍 Detected TempStick CSV format');
        console.log(`   Sensor: "${sensorInfo.name}" (ID: ${sensorInfo.sensorId})`);

        // Store sensor info for column mapping
        if (options.columnMapping && !options.columnMapping.sensorId) {
          (options.columnMapping as Record<string, string | number>).sensorId = sensorInfo.sensorId;
        }

        // Skip: Line 1 (sensor info), Line 2 (empty), Line 3 (headers)
        startIndex = 3;

        // Verify header line exists and matches expected TempStick format
        if (lines.length > 2) {
          const headerLine = lines[2];
          const expectedHeaders = [
            'Timestamp',
            'Temperature',
            'Humidity',
            'Heat Index',
            'Dew Point',
            'Battery',
            'Connection Quality',
            'Seconds to Connect',
          ];
          const hasExpectedHeaders = expectedHeaders.some((header) => headerLine.includes(header));

          if (hasExpectedHeaders) {
            console.log('✅ TempStick header format confirmed');

            // Override temperature unit to Fahrenheit for TempStick data
            if (!options.temperatureUnit) {
              (options as CSVImportOptions).temperatureUnit = 'fahrenheit';
              console.log('🌡️  Auto-detected temperature unit: Fahrenheit');
            }
          } else {
            console.warn("⚠️  TempStick format detected but headers don't match expected format");
          }
        }
      } else if (options.hasHeaders) {
        // Standard CSV with just column headers
        startIndex = 1;
        console.log('🔍 Standard CSV format, skipping first line');
      }
    }

    // Parse data rows
    for (let i = startIndex; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue; // Skip empty lines

      // Simple CSV parsing (handles quoted fields)
      const row = this.parseCSVRow(line);

      // Skip rows that don't have the minimum expected columns
      // TempStick has 9 columns (8 data + 1 empty trailing)
      if (row.length >= 2) {
        // At minimum need timestamp and temperature
        rows.push(row);
      }
    }

    console.log(`📊 Parsed ${rows.length} data rows from ${lines.length} total lines`);

    if (sensorInfo.sensorId) {
      console.log(`🎯 Sensor ID extracted from header: ${sensorInfo.sensorId}`);
    }

    return rows;
  }

  /**
   * Parse individual CSV row
   */
  private parseCSVRow(line: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];

      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }

    result.push(current.trim());
    return result;
  }

  /**
   * Get sensor ID mappings for the current user
   */
  private async getSensorMappings(): Promise<Record<string, string>> {
    // Fetch ALL active sensors (no user_id filtering - sensors are company-wide shared)
    const { data: sensors, error } = await supabase
      .from('sensors')
      .select('id, sensor_id, name, device_name')
      .eq('is_active', true);

    if (error) {
      throw new Error(`Failed to fetch sensor mappings: ${error.message}`);
    }

    console.log('📡 Found company sensors for CSV import:', sensors?.length ?? 0);

    const mappings: Record<string, string> = {};

    sensors?.forEach((sensor) => {
      // Map by sensor_id (external TempStick ID)
      mappings[sensor.sensor_id] = sensor.id;

      // Map by name variations
      if (sensor.name) {
        mappings[sensor.name.toLowerCase()] = sensor.id;
      }
      if (sensor.device_name) {
        mappings[sensor.device_name.toLowerCase()] = sensor.id;
      }
    });

    console.log('🗺️ Created sensor mappings:', Object.keys(mappings).slice(0, 5).join(', '), '...');

    return mappings;
  }

  /**
   * Process a single CSV row into a temperature reading
   */
  private async processRow(
    row: string[],
    options: CSVImportOptions,
    sensorMappings: Record<string, string>
  ): Promise<TemperatureReading | null> {
    // TempStick CSV column mapping with user overrides:
    const mapping = {
      timestamp: 0,
      temperature: 1,
      humidity: 2,
      heatIndex: 3,
      dewPoint: 4,
      battery: 5,
      rssi: 6,
      connectTime: 7,
      ...options.columnMapping,
    };

    // Extract and validate required fields
    const timestampStr = row[mapping.timestamp]?.trim();
    const temperatureStr = row[mapping.temperature]?.trim();
    const humidityStr = row[mapping.humidity]?.trim();
    const batteryStr = row[mapping.battery]?.trim();
    const rssiStr = row[mapping.rssi]?.trim();

    // Validate required fields
    if (!timestampStr || !temperatureStr) {
      throw new Error('Missing required timestamp or temperature');
    }

    // Parse timestamp
    const timestamp = new Date(timestampStr);
    if (isNaN(timestamp.getTime())) {
      throw new Error(`Invalid timestamp: ${timestampStr}`);
    }

    // Parse temperature
    const temperature = parseFloat(temperatureStr);
    if (isNaN(temperature)) {
      throw new Error(`Invalid temperature: ${temperatureStr}`);
    }

    // Convert temperature based on source unit
    // TempStick exports in Fahrenheit, so we need proper conversion
    const sourceUnit = options.temperatureUnit ?? 'fahrenheit';
    let tempCelsius: number;
    let tempFahrenheit: number;

    if (sourceUnit === 'fahrenheit') {
      tempFahrenheit = temperature;
      tempCelsius = (temperature - 32) / 1.8;
    } else {
      tempCelsius = temperature;
      tempFahrenheit = temperature * 1.8 + 32;
    }

    // Parse optional fields
    const humidity = humidityStr ? parseFloat(humidityStr) : null;
    const batteryLevel = batteryStr ? parseFloat(batteryStr) : null;
    const signalStrength = rssiStr ? parseInt(rssiStr) : null;

    // Validate ranges
    if (humidity !== null && (humidity < 0 || humidity > 100)) {
      console.warn(`⚠️  Humidity out of range (0-100%): ${humidity}`);
    }

    if (batteryLevel !== null && (batteryLevel < 0 || batteryLevel > 100)) {
      console.warn(`⚠️  Battery level out of range (0-100%): ${batteryLevel}`);
    }

    // Determine sensor ID using multiple strategies
    let sensorId: string | undefined;

    // Strategy 1: Use sensor ID from column mapping (auto-detected from filename or manually set)
    if (options.columnMapping?.sensorId && typeof options.columnMapping.sensorId === 'string') {
      const mappingKey = options.columnMapping.sensorId;
      sensorId = sensorMappings[mappingKey] ?? sensorMappings[mappingKey.toLowerCase()];

      if (sensorId) {
        console.log(
          `🎯 Sensor mapped via filename/direct ID: ${mappingKey} -> ${sensorId.substring(0, 8)}...`
        );
      }
    }

    // Strategy 2: Use default sensor ID if specified
    if (!sensorId && options.defaultSensorId) {
      sensorId =
        sensorMappings[options.defaultSensorId] ?? sensorMappings[options.defaultSensorId.toLowerCase()];
    }

    // Strategy 3: Use column-based sensor identification (if available in row data)
    if (!sensorId && mapping.sensorId !== undefined && row[mapping.sensorId]) {
      const rowSensorId = row[mapping.sensorId].trim();
      sensorId = sensorMappings[rowSensorId] ?? sensorMappings[rowSensorId.toLowerCase()];
    }

    if (!sensorId) {
      console.error('❌ Sensor mapping failed!');
      console.error(`   Available sensor mappings:`, Object.keys(sensorMappings).slice(0, 5));
      console.error(
        `   Attempted to map: ${options.columnMapping?.sensorId ?? options.defaultSensorId ?? 'unknown'}`
      );
      throw new Error(
        `Could not map sensor: ${options.columnMapping?.sensorId ?? options.defaultSensorId ?? 'unknown sensor'}`
      );
    }

    // Determine temperature safety ranges (freezer context)
    // For freezers, safe range is typically -20°F to 10°F (-29°C to -12°C)
    const withinSafeRange = tempFahrenheit >= -20 && tempFahrenheit <= 10;
    const tempViolation = !withinSafeRange;

    return {
      user_id: this.user.id,
      sensor_id: sensorId,
      recorded_at: timestamp.toISOString(),
      temp_celsius: Math.round(tempCelsius * 10) / 10, // Round to 1 decimal
      temp_fahrenheit: Math.round(tempFahrenheit * 10) / 10, // Round to 1 decimal
      humidity: humidity ? Math.round(humidity * 10) / 10 : null,
      battery_level: batteryLevel ? Math.round(batteryLevel) : null,
      signal_strength: signalStrength,
      within_safe_range: withinSafeRange,
      temp_violation: tempViolation,
      data_source: 'csv_import',
    };
  }

  /**
   * Insert batch of readings with conflict resolution
   */
  private async insertBatch(
    readings: TemperatureReading[]
  ): Promise<{ inserted: number; duplicates: number }> {
    const { data, error } = await supabase
      .from('temperature_readings')
      .upsert(readings, {
        onConflict: 'sensor_id,recorded_at',
        ignoreDuplicates: true,
      })
      .select('id');

    if (error) {
      throw new Error(`Batch insert failed: ${error.message}`);
    }

    const inserted = data?.length ?? 0;
    const duplicates = readings.length - inserted;

    return { inserted, duplicates };
  }

  /**
   * Preview CSV structure without importing
   */
  static previewCSV(
    csvContent: string,
    options: CSVImportOptions = {}
  ): {
    headers?: string[];
    sampleRows: string[][];
    totalRows: number;
    sensorInfo?: { name: string; sensorId: string };
  } {
    const lines = csvContent.trim().split('\n');
    const importer = new CSVTemperatureImporter(null);

    let headers: string[] | undefined;
    const sampleRows: string[][] = [];
    let sensorInfo: { name: string; sensorId: string } | undefined;

    // Detect TempStick format and extract sensor info
    if (lines.length > 0) {
      const firstLine = lines[0];
      const tempstickMatch = firstLine.match(/^Sensor:\s*(.+?)\s*\(ID:\s*(\d+)\)/);
      if (tempstickMatch) {
        sensorInfo = {
          name: tempstickMatch[1].trim(),
          sensorId: tempstickMatch[2],
        };
      }
    }

    // Determine starting index and extract headers
    let startIndex = 0;
    if (sensorInfo) {
      // TempStick format: skip sensor info line, empty line, then headers
      startIndex = 3;
      if (lines.length > 2) {
        headers = importer.parseCSVRow(lines[2]);
      }
    } else if (options.hasHeaders && lines.length > 0) {
      // Standard CSV with headers
      startIndex = 1;
      headers = importer.parseCSVRow(lines[0]);
    }

    // Get sample rows (first 5 data rows)
    const endIndex = Math.min(startIndex + 5, lines.length);

    for (let i = startIndex; i < endIndex; i++) {
      if (lines[i]?.trim()) {
        sampleRows.push(importer.parseCSVRow(lines[i]));
      }
    }

    // Calculate total data rows
    let totalDataRows = 0;
    for (let i = startIndex; i < lines.length; i++) {
      if (lines[i]?.trim()) {
        totalDataRows++;
      }
    }

    return {
      headers,
      sampleRows,
      totalRows: totalDataRows,
      sensorInfo,
    };
  }
}

export default CSVTemperatureImporter;
