import { useEffect, useState } from 'react';
import { Session } from '@supabase/supabase-js';

import { appEnv } from '@/lib/config/env';

import { supabase } from './lib/supabase';
import { setupDatabase } from './lib/setupDatabase';
import { backgroundSync } from './lib/background-sync';
// import { signInAnonymously } from './lib/supabase'; // No anonymous sign-in - REMOVE IMPORT
import Auth from './components/Auth';
import AppLayout from './components/AppLayout';
import { NavigationProvider } from './contexts/NavigationContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { UnitProvider } from './contexts/UnitContext';
import { VoiceAssistantProvider } from './contexts/VoiceAssistantContext';
import { Toaster } from './components/ui/toaster';

function App() {
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [initializationAttempts, setInitializationAttempts] = useState(0);

  // Initialize auth only (no DB access pre-auth)
  useEffect(() => {
    let unsub: (() => void) | undefined;
    let cancelled = false;

    const initAuth = async () => {
      try {
        // Race getSession with a timeout to avoid indefinite spinner if network hangs
        const timeoutMs = 6000;
        const sessionResult = (await Promise.race([
          supabase.auth.getSession(),
          new Promise<{ data: { session: Session | null }; error: unknown | null }>((resolve) =>
            setTimeout(() => resolve({ data: { session: null }, error: null }), timeoutMs)
          ),
        ])) as { data: { session: Session | null }; error: unknown | null };

        const { session } = sessionResult.data;
        setSession(session);

        const {
          data: { subscription },
        } = supabase.auth.onAuthStateChange((_event, nextSession) => {
          setSession(nextSession);
        });
        unsub = () => subscription.unsubscribe();
      } catch (err) {
        console.error('Error initializing auth:', err);
        if (!cancelled) {
          setError(err instanceof Error ? err.message : 'Failed to initialize authentication');
        }
      } finally {
        // Allow UI to render Auth screen if not authenticated
        if (!cancelled) setIsLoading(false);
      }
    };

    initAuth();

    return () => {
      cancelled = true;
      if (unsub) unsub();
    };
  }, [initializationAttempts]);

  // Run database setup only AFTER user is authenticated
  useEffect(() => {
    if (!session) return;
    let cancelled = false;
    const initDb = async () => {
      try {
        setError(null);
        const setup = await setupDatabase();
        if (!setup?.success) {
          throw new Error('Failed to initialize database. Please try again.');
        }
      } catch (err) {
        console.error('Initialization error (DB):', err);
        if (!cancelled) {
          setError(err instanceof Error ? err.message : 'An error occurred during initialization');
        }
      }
    };
    initDb();
    return () => {
      cancelled = true;
    };
  }, [session]);

  // Initialize background sync service for TempStick polling once user is authenticated
  useEffect(() => {
    if (!session) return;

    // Temporarily disabled due to JWT auth errors blocking app initialization
    // console.log('🔄 Initializing TempStick background sync service...');

    // try {
    //   backgroundSync.initialize();
    //   console.log('✅ TempStick background sync service initialized');
    // } catch (error) {
    //   console.error('❌ Failed to initialize background sync service:', error);
    // }

    // return () => {
    //   backgroundSync.shutdown();
    // };
  }, [session]);

  const handleRetry = () => {
    setError(null);
    setIsLoading(true);
    setInitializationAttempts((prev) => prev + 1);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading application...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4">
        <div className="bg-white p-6 rounded-lg shadow-md max-w-md w-full">
          <h2 className="text-xl font-semibold text-red-600 mb-2">Configuration Error</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <p className="text-sm text-gray-500 mb-4">
            Please check your environment variables and ensure Supabase is properly configured.
          </p>
          <button
            onClick={handleRetry}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // TEMPORARY: Skip auth for testing - REMOVE IN PRODUCTION
  if (!session && !appEnv.featureFlags.skipAuth) {
    return <Auth />;
  }

  return (
    <ThemeProvider>
      <NavigationProvider>
        <UnitProvider defaultUnit="celsius">
          <VoiceAssistantProvider>
            <AppLayout />
            <Toaster />
          </VoiceAssistantProvider>
        </UnitProvider>
      </NavigationProvider>
    </ThemeProvider>
  );
}

export default App;
