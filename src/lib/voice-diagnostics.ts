import { supabase } from './supabase';
import { appEnv } from './config/env';
import type { ModernVoiceClientConfig } from './ModernRealtimeVoiceClient';

/**
 * Comprehensive diagnostics utility for voice operations
 * Provides testing, validation, and troubleshooting tools for the voice system
 */

export interface DiagnosticResult {
  success: boolean;
  message: string;
  details?: any;
  timestamp: string;
}

export interface DetailedDiagnosticResult {
  status: 'pass' | 'warning' | 'fail';
  message: string;
  details: Record<string, any>;
  recommendations?: string[];
}

export interface VoiceSystemDiagnostics {
  webrtc: DiagnosticResult;
  websocket: DiagnosticResult;
  openai: DiagnosticResult;
  authentication: DiagnosticResult;
  database: DiagnosticResult;
  edgeFunction: DiagnosticResult;
  environment: DiagnosticResult;
  performance: DiagnosticResult;
}

export interface PerformanceMetrics {
  webrtcNegotiationTime?: number;
  websocketConnectionTime?: number;
  toolExecutionTime?: number;
  audioLatency?: number;
  responseTime?: number;
}

function containsRealtimeType(value: unknown, visited = new WeakSet<object>()): boolean {
  if (value === null || value === undefined) {
    return false;
  }

  if (typeof value === 'object') {
    if (visited.has(value as object)) {
      return false;
    }
    visited.add(value as object);
  }

  if (Array.isArray(value)) {
    return value.some(item => containsRealtimeType(item, visited));
  }

  if (typeof value === 'object') {
    for (const [key, nested] of Object.entries(value as Record<string, unknown>)) {
      if (key === 'type' && nested === 'realtime') {
        return true;
      }
      if (containsRealtimeType(nested, visited)) {
        return true;
      }
    }
  }

  return false;
}

/**
 * Test WebRTC capability and connectivity
 */
export async function testWebRTCCapability(): Promise<DiagnosticResult> {
  const timestamp = new Date().toISOString();

  try {
    // Check basic WebRTC support
    if (!window.RTCPeerConnection) {
      return {
        success: false,
        message: 'WebRTC not supported in this browser',
        timestamp
      };
    }

    // Test STUN server connectivity
    const pc = new RTCPeerConnection({
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
      ]
    });

    const startTime = performance.now();
    let iceGatheringComplete = false;
    let gatheringTimeout: NodeJS.Timeout;

    return new Promise((resolve) => {
      const gatheringTimeoutMs = 5000;

      pc.onicecandidate = (event) => {
        if (event.candidate === null && !iceGatheringComplete) {
          iceGatheringComplete = true;
          clearTimeout(gatheringTimeout);
          const duration = performance.now() - startTime;
          pc.close();

          resolve({
            success: true,
            message: `WebRTC ICE gathering completed in ${Math.round(duration)}ms`,
            details: { duration, candidatesGathered: true },
            timestamp
          });
        }
      };

      pc.onicegatheringstatechange = () => {
        if (pc.iceGatheringState === 'complete' && !iceGatheringComplete) {
          iceGatheringComplete = true;
          clearTimeout(gatheringTimeout);
          const duration = performance.now() - startTime;
          pc.close();

          resolve({
            success: true,
            message: `WebRTC ICE gathering completed in ${Math.round(duration)}ms`,
            details: { duration, iceGatheringState: pc.iceGatheringState },
            timestamp
          });
        }
      };

      // Set timeout for ICE gathering
      gatheringTimeout = setTimeout(() => {
        if (!iceGatheringComplete) {
          pc.close();
          resolve({
            success: false,
            message: 'WebRTC ICE gathering timed out',
            details: {
              timeout: gatheringTimeoutMs,
              iceGatheringState: pc.iceGatheringState,
              iceConnectionState: pc.iceConnectionState
            },
            timestamp
          });
        }
      }, gatheringTimeoutMs);

      // Create a data channel to trigger ICE gathering
      pc.createDataChannel('test');
      pc.createOffer().then(offer => {
        return pc.setLocalDescription(offer);
      }).catch(error => {
        clearTimeout(gatheringTimeout);
        pc.close();
        resolve({
          success: false,
          message: `WebRTC offer creation failed: ${error.message}`,
          details: { error },
          timestamp
        });
      });
    });

  } catch (error) {
    return {
      success: false,
      message: `WebRTC test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: { error },
      timestamp
    };
  }
}

/**
 * Test WebSocket connectivity
 */
export async function testWebSocketConnectivity(): Promise<DiagnosticResult> {
  const timestamp = new Date().toISOString();

  try {
    const testUrl = 'wss://echo.websocket.org';
    const startTime = performance.now();

    return new Promise((resolve) => {
      const ws = new WebSocket(testUrl);
      const timeout = setTimeout(() => {
        ws.close();
        resolve({
          success: false,
          message: 'WebSocket connection timed out',
          timestamp
        });
      }, 5000);

      ws.onopen = () => {
        const duration = performance.now() - startTime;
        clearTimeout(timeout);
        ws.close();

        resolve({
          success: true,
          message: `WebSocket connection established in ${Math.round(duration)}ms`,
          details: { duration, url: testUrl },
          timestamp
        });
      };

      ws.onerror = (error) => {
        clearTimeout(timeout);
        resolve({
          success: false,
          message: 'WebSocket connection failed',
          details: { error, url: testUrl },
          timestamp
        });
      };
    });

  } catch (error) {
    return {
      success: false,
      message: `WebSocket test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: { error },
      timestamp
    };
  }
}

/**
 * Test OpenAI API accessibility
 */
export async function testOpenAIAPIAccess(): Promise<DiagnosticResult> {
  const timestamp = new Date().toISOString();

  try {
    const apiKey = appEnv.openAi.apiKey;

    if (!apiKey) {
      return {
        success: false,
        message: 'No OpenAI API key configured',
        timestamp
      };
    }

    const startTime = performance.now();

    // Test API connectivity with a simple request
    const response = await fetch('https://api.openai.com/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    const duration = performance.now() - startTime;

    if (response.ok) {
      const data = await response.json();
      const realtimeModels = data.data?.filter((model: any) =>
        model.id.includes('realtime') || model.id.includes('gpt-4o')
      ) || [];

      return {
        success: true,
        message: `OpenAI API accessible in ${Math.round(duration)}ms`,
        details: {
          duration,
          status: response.status,
          realtimeModelsAvailable: realtimeModels.length,
          configuredModel: appEnv.realtime.model
        },
        timestamp
      };
    } else {
      const errorText = await response.text();
      return {
        success: false,
        message: `OpenAI API error: ${response.status} ${response.statusText}`,
        details: { status: response.status, error: errorText, duration },
        timestamp
      };
    }

  } catch (error) {
    return {
      success: false,
      message: `OpenAI API test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: { error },
      timestamp
    };
  }
}

/**
 * Test user authentication status
 */
export async function testAuthentication(): Promise<DiagnosticResult> {
  const timestamp = new Date().toISOString();

  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError) {
      return {
        success: false,
        message: `Authentication error: ${userError.message}`,
        details: { error: userError },
        timestamp
      };
    }

    if (!user) {
      return {
        success: false,
        message: 'No authenticated user session',
        timestamp
      };
    }

    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      return {
        success: false,
        message: `Session error: ${sessionError.message}`,
        details: { error: sessionError },
        timestamp
      };
    }

    return {
      success: true,
      message: `Authenticated as ${user.email}`,
      details: {
        userId: user.id,
        email: user.email,
        sessionValid: !!session,
        lastSignIn: user.last_sign_in_at
      },
      timestamp
    };

  } catch (error) {
    return {
      success: false,
      message: `Authentication test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: { error },
      timestamp
    };
  }
}

/**
 * Test database connectivity and RLS policies
 */
export async function testDatabaseConnectivity(): Promise<DiagnosticResult> {
  const timestamp = new Date().toISOString();

  try {
    const startTime = performance.now();

    // Test basic connectivity
    const { data, error } = await supabase
      .from('inventory_events')
      .select('id')
      .limit(1);

    const duration = performance.now() - startTime;

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows (acceptable)
      return {
        success: false,
        message: `Database error: ${error.message}`,
        details: { error, duration },
        timestamp
      };
    }

    // Test user permissions for voice operations
    const { data: { user } } = await supabase.auth.getUser();

    if (user) {
      // Test if user can insert voice inventory events
      const testEventData = {
        event_type: 'physical_count',
        name: 'Voice Test Product',
        category: 'Test Category',
        quantity: 0,
        unit: 'lbs',
        occurred_at: new Date().toISOString(),
        created_by_voice: true,
        metadata: { test: true },
      };

      const { error: insertError } = await supabase
        .from('inventory_events')
        .insert([testEventData])
        .select()
        .single();

      if (insertError) {
        return {
          success: false,
          message: `Voice inventory permission test failed: ${insertError.message}`,
          details: { error: insertError, duration, userId: user.id },
          timestamp
        };
      }

      // Clean up test data
      await supabase
        .from('inventory_events')
        .delete()
        .eq('name', 'Voice Test Product')
        .eq('created_by_voice', true);
    }

    return {
      success: true,
      message: `Database accessible in ${Math.round(duration)}ms`,
      details: {
        duration,
        hasUser: !!user,
        canInsertVoiceEvents: !!user
      },
      timestamp
    };

  } catch (error) {
    return {
      success: false,
      message: `Database test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: { error },
      timestamp
    };
  }
}

/**
 * Test Edge Function availability
 */
export async function testEdgeFunctionConnectivity(): Promise<DiagnosticResult> {
  const timestamp = new Date().toISOString();

  try {
    const startTime = performance.now();

    const { data, error } = await supabase.functions.invoke('voice_inventory_event', {
      body: { test: true, dryRun: true }
    });

    const duration = performance.now() - startTime;

    if (error) {
      // Some errors are expected for test calls
      if (error.message?.includes('test') || error.message?.includes('dryRun')) {
        return {
          success: true,
          message: `Edge Function accessible in ${Math.round(duration)}ms (test response)`,
          details: { duration, testResponse: error.message },
          timestamp
        };
      }

      return {
        success: false,
        message: `Edge Function error: ${error.message}`,
        details: { error, duration },
        timestamp
      };
    }

    return {
      success: true,
      message: `Edge Function accessible in ${Math.round(duration)}ms`,
      details: { duration, response: data },
      timestamp
    };

  } catch (error) {
    return {
      success: false,
      message: `Edge Function test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: { error },
      timestamp
    };
  }
}

/**
 * Validate environment configuration
 */
export function validateEnvironmentConfiguration(): DiagnosticResult {
  const timestamp = new Date().toISOString();
  const issues: string[] = [];
  const warnings: string[] = [];

  // Check required environment variables
  if (!appEnv.supabase.url) {
    issues.push('Missing VITE_SUPABASE_URL');
  }

  if (!appEnv.supabase.anonKey) {
    issues.push('Missing VITE_SUPABASE_ANON_KEY');
  }

  if (!appEnv.openAi.apiKey) {
    warnings.push('Missing OPENAI_API_KEY (may limit functionality)');
  }

  // Check feature flag consistency
  if (appEnv.featureFlags.forceWebRtcTransport && !appEnv.featureFlags.directRealtime) {
    warnings.push('VITE_FORCE_WEBRTC_TRANSPORT enabled but VITE_FEATURE_DIRECT_REALTIME disabled');
  }

  if (appEnv.featureFlags.disableWebRtcFallback && !appEnv.featureFlags.useWebRtc) {
    warnings.push('VITE_DISABLE_WEBRTC_FALLBACK enabled but VITE_FEATURE_USE_WEBRTC disabled');
  }

  // Check model compatibility
  if (!appEnv.realtime.model.includes('gpt-4o') && !appEnv.realtime.model.includes('realtime')) {
    warnings.push('Configured model may not support realtime features');
  }

  const success = issues.length === 0;
  let message = success ? 'Environment configuration valid' : `Configuration issues: ${issues.join(', ')}`;

  if (warnings.length > 0) {
    message += ` (Warnings: ${warnings.join(', ')})`;
  }

  return {
    success,
    message,
    details: {
      issues,
      warnings,
      environment: appEnv.mode,
      featureFlags: appEnv.featureFlags
    },
    timestamp
  };
}

/**
 * Measure performance metrics
 */
export async function measurePerformanceMetrics(): Promise<DiagnosticResult & { metrics: PerformanceMetrics }> {
  const timestamp = new Date().toISOString();
  const metrics: PerformanceMetrics = {};

  try {
    // Measure WebRTC negotiation time
    const webrtcStart = performance.now();
    const webrtcResult = await testWebRTCCapability();
    metrics.webrtcNegotiationTime = performance.now() - webrtcStart;

    // Measure WebSocket connection time
    const wsStart = performance.now();
    const wsResult = await testWebSocketConnectivity();
    metrics.websocketConnectionTime = performance.now() - wsStart;

    // Measure database query time
    const dbStart = performance.now();
    await supabase.from('inventory_events').select('id').limit(1);
    const dbTime = performance.now() - dbStart;

    // Estimate tool execution time (mock test)
    const toolStart = performance.now();
    // Simulate tool processing time
    await new Promise(resolve => setTimeout(resolve, 50));
    metrics.toolExecutionTime = performance.now() - toolStart;

    return {
      success: true,
      message: 'Performance metrics collected',
      details: {
        webrtcCapable: webrtcResult.success,
        websocketCapable: wsResult.success,
        databaseResponseTime: Math.round(dbTime)
      },
      metrics,
      timestamp
    };

  } catch (error) {
    return {
      success: false,
      message: `Performance measurement failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: { error },
      metrics,
      timestamp
    };
  }
}

/**
 * Run comprehensive voice system diagnostics
 */
export async function runComprehensiveDiagnostics(): Promise<VoiceSystemDiagnostics> {
  const [
    webrtc,
    websocket,
    openai,
    authentication,
    database,
    edgeFunction,
    environment,
    performance
  ] = await Promise.all([
    testWebRTCCapability(),
    testWebSocketConnectivity(),
    testOpenAIAPIAccess(),
    testAuthentication(),
    testDatabaseConnectivity(),
    testEdgeFunctionConnectivity(),
    Promise.resolve(validateEnvironmentConfiguration()),
    measurePerformanceMetrics()
  ]);

  return {
    webrtc,
    websocket,
    openai,
    authentication,
    database,
    edgeFunction,
    environment,
    performance
  };
}

/**
 * Generate troubleshooting recommendations based on diagnostic results
 */
export function generateTroubleshootingRecommendations(diagnostics: VoiceSystemDiagnostics): string[] {
  const recommendations: string[] = [];

  if (!diagnostics.webrtc.success) {
    recommendations.push('WebRTC issues detected. Try using WebSocket transport or check firewall/NAT settings.');
  }

  if (!diagnostics.websocket.success) {
    recommendations.push('WebSocket connectivity issues. Check network proxy settings and firewalls.');
  }

  if (!diagnostics.openai.success) {
    recommendations.push('OpenAI API access issues. Verify API key and network connectivity.');
  }

  if (!diagnostics.authentication.success) {
    recommendations.push('Authentication issues. Please log out and log back in to refresh your session.');
  }

  if (!diagnostics.database.success) {
    recommendations.push('Database access issues. Check RLS policies and user permissions for voice operations.');
  }

  if (!diagnostics.edgeFunction.success) {
    recommendations.push('Edge Function issues. Voice inventory operations may fall back to direct database access.');
  }

  if (!diagnostics.environment.success) {
    recommendations.push('Environment configuration issues. Check environment variables and feature flags.');
  }

  if (recommendations.length === 0) {
    recommendations.push('All systems appear to be functioning correctly.');
  }

  return recommendations;
}

/**
 * =====================================================================
 * NEW DIAGNOSTICS for Session.Type Fix and Credential Validation
 * =====================================================================
 */

/**
 * Diagnose voice credentials configuration
 * Validates that at least one credential type is present and properly formatted
 */
export function diagnoseVoiceCredentials(config: ModernVoiceClientConfig): DetailedDiagnosticResult {
  const details: Record<string, any> = {
    hasApiKey: Boolean(config.apiKey),
    hasEphemeralToken: Boolean(config.ephemeralToken),
    hasRelayUrl: Boolean(config.relayUrl),
  };

  // Check if any credentials are present
  if (!details.hasApiKey && !details.hasEphemeralToken && !details.hasRelayUrl) {
    return {
      status: 'fail',
      message: 'No API credentials provided',
      details,
      recommendations: [
        'Provide at least one credential type: apiKey, ephemeralToken, or relayUrl',
        'For WebRTC mode, use ephemeralToken created via OpenAI Realtime Sessions API',
        'For WebSocket relay mode, provide relayUrl',
        'For direct API access, provide apiKey (less secure)',
      ],
    };
  }

  // Validate token formats
  if (config.apiKey) {
    const isValidApiKey = config.apiKey.startsWith('sk-');
    details.apiKeyFormat = isValidApiKey ? 'valid (sk-*)' : 'invalid';
    if (!isValidApiKey && config.apiKey !== 'relay') {
      return {
        status: 'warning',
        message: 'API key format appears invalid',
        details,
        recommendations: [
          'API keys should start with "sk-" prefix',
          'Check that you are using a valid OpenAI API key',
        ],
      };
    }
  }

  if (config.ephemeralToken) {
    const isValidEphemeral = config.ephemeralToken.startsWith('ek_');
    details.ephemeralTokenFormat = isValidEphemeral ? 'valid (ek_*)' : 'invalid';
    if (!isValidEphemeral) {
      return {
        status: 'warning',
        message: 'Ephemeral token format appears invalid',
        details,
        recommendations: [
          'Ephemeral tokens should start with "ek_" prefix',
          'Tokens are created via POST /v1/realtime/sessions',
          'Check server logs for token creation errors',
        ],
      };
    }
  }

  if (config.relayUrl) {
    try {
      new URL(config.relayUrl);
      details.relayUrlFormat = 'valid URL';
    } catch {
      details.relayUrlFormat = 'invalid URL';
      return {
        status: 'fail',
        message: 'Relay URL format is invalid',
        details,
        recommendations: [
          'Provide a valid WebSocket URL (ws:// or wss://)',
          'Example: wss://your-relay-server.com/api/realtime-relay',
        ],
      };
    }
  }

  return {
    status: 'pass',
    message: 'Credentials configuration valid',
    details,
  };
}

/**
 * Diagnose DataChannel patch status
 * Checks if the patch has been applied and is intercepting messages
 */
export function diagnoseDataChannelPatch(transport: any, sessionOverride?: any): DetailedDiagnosticResult {
  const session = sessionOverride ?? transport?._session ?? null;
  const details: Record<string, any> = {
    hasDataChannel: Boolean(transport?._dataChannel),
    patchApplied: Boolean(transport?._dataChannel?._patchedForSessionType),
    transportType: transport?.constructor?.name || 'unknown',
    sessionAttached: Boolean(session),
    constructorPatched: Boolean(session?._constructorPatched),
    sessionPatched: Boolean(session?._patchedForSessionType),
    mergedConfigPatched: Boolean(session?._patchedMergedSessionConfig),
  };

  if (session) {
    const keysToInspect = ['_config', '_sessionConfig', '_pendingSessionUpdate', 'config'];
    const sanitizedKeys: Record<string, 'clean' | 'contains-type' | 'not-present'> = {};
    for (const key of keysToInspect) {
      if (Object.prototype.hasOwnProperty.call(session, key)) {
        const value = (session as Record<string, unknown>)[key];
        sanitizedKeys[key] = containsRealtimeType(value) ? 'contains-type' : 'clean';
      } else {
        sanitizedKeys[key] = 'not-present';
      }
    }
    details.sanitizedKeys = sanitizedKeys;
    details.sessionHasRealtimeType = containsRealtimeType(session);
  }

  if (!transport) {
    return {
      status: 'fail',
      message: 'Transport not available',
      details,
      recommendations: [
        'Initialize the voice client before checking patch status',
        'Ensure connect() has been called',
      ],
    };
  }

  if (!details.hasDataChannel) {
    return {
      status: 'warning',
      message: 'DataChannel not available',
      details: {
        ...details,
        note: 'This is normal for WebSocket transport or before connection',
      },
      recommendations: [
        'For WebRTC: DataChannel becomes available after connect() completes',
        'For WebSocket: DataChannel patch is not needed',
      ],
    };
  }

  if (!details.patchApplied) {
    return {
      status: 'fail',
      message: 'DataChannel patch not applied',
      details,
      recommendations: [
        'The patch should be applied automatically during connection',
        'Check browser console for "DATACHANNEL PATCH APPLIED" message',
        'If missing, the session.type field will NOT be stripped',
        'Review VOICE_ASSISTANT_SESSION_TYPE_FIX.md for constructor patch steps',
        'Enable alternative initialization (useAlternativeInitialization: true) as a fallback',
      ],
    };
  }

  if (details.sanitizedKeys) {
    const keysWithType = Object.entries(details.sanitizedKeys)
      .filter(([, status]) => status === 'contains-type')
      .map(([key]) => key);

    if (keysWithType.length > 0) {
      return {
        status: 'fail',
        message: `Constructor patch missing for keys: ${keysWithType.join(', ')}`,
        details,
        recommendations: [
          'Ensure patchRealtimeSessionConstructor() runs before session.connect()',
          'Verify sanitizeSessionObject removes type fields from internal config',
          'See VOICE_ASSISTANT_SESSION_TYPE_FIX.md for verification checklist',
        ],
      };
    }
  }

  if (details.sessionHasRealtimeType) {
    return {
      status: 'fail',
      message: 'Session internals still contain type: "realtime" after patching',
      details,
      recommendations: [
        'Call client.getConnectionStatus() to verify constructor patch markers',
        'Double-check sanitizeSessionObject coverage on nested session keys',
        'Enable alternative initialization if constructor patch cannot be applied',
      ],
    };
  }

  if (details.sessionAttached && !details.constructorPatched) {
    return {
      status: 'warning',
      message: 'Constructor patch marker not detected on session',
      details,
      recommendations: [
        'Confirm patchRealtimeSessionConstructor() executed immediately after session creation',
        'Restart the client after clearing cached sessions',
        'Follow VOICE_ASSISTANT_SESSION_TYPE_FIX.md -> Latest Fix section',
      ],
    };
  }

  return {
    status: 'pass',
    message: 'DataChannel patch successfully applied and session sanitized',
    details: {
      ...details,
      note: 'Patch will intercept and strip session.type from all messages. Constructor marker detected.',
    },
  };
}

export function diagnoseSessionConstructorPatch(session: any): DetailedDiagnosticResult {
  const details: Record<string, any> = {
    sessionAvailable: Boolean(session),
  };

  if (!session) {
    return {
      status: 'fail',
      message: 'Realtime session not available for constructor diagnostics',
      details,
      recommendations: [
        'Ensure ModernRealtimeVoiceClient has been created',
        'Call client.connect() before running diagnostics',
      ],
    };
  }

  const constructorPatched = Boolean(session._constructorPatched);
  const sendEventPatched = Boolean(session._patchedForSessionType);
  const mergedConfigPatched = Boolean(session._patchedMergedSessionConfig);
  const applyConfigPatched = Boolean(session._patchedApplyConfig);

  const keysToInspect = ['_config', '_sessionConfig', '_pendingSessionUpdate', 'config'];
  const sanitizedKeys: Record<string, 'clean' | 'contains-type' | 'not-present'> = {};
  for (const key of keysToInspect) {
    if (Object.prototype.hasOwnProperty.call(session, key)) {
      const value = session[key];
      sanitizedKeys[key] = containsRealtimeType(value) ? 'contains-type' : 'clean';
    } else {
      sanitizedKeys[key] = 'not-present';
    }
  }

  const keysWithType = Object.entries(sanitizedKeys)
    .filter(([, status]) => status === 'contains-type')
    .map(([key]) => key);

  const sessionHasRealtimeType = containsRealtimeType(session);

  Object.assign(details, {
    constructorPatched,
    sendEventPatched,
    mergedConfigPatched,
    applyConfigPatched,
    sanitizedKeys,
    sessionHasRealtimeType,
  });

  if (sessionHasRealtimeType || keysWithType.length > 0) {
    return {
      status: 'fail',
      message: 'Session constructor patch incomplete - session.type detected in internals',
      details,
      recommendations: [
        `Sanitize keys containing session.type: ${keysWithType.join(', ') || 'root session object'}`,
        'Confirm sanitizeSessionObject() is used when patching _getMergedSessionConfig()',
        'Reference VOICE_ASSISTANT_SESSION_TYPE_FIX.md for cleanup steps',
      ],
    };
  }

  if (!constructorPatched) {
    return {
      status: 'warning',
      message: 'Constructor patch marker not found on RealtimeSession',
      details,
      recommendations: [
        'Run patchRealtimeSessionConstructor() immediately after new RealtimeSession()',
        'Verify ModernRealtimeVoiceClient constructor calls patch before connect()',
        'Use alternative initialization when constructor patch cannot be applied',
      ],
    };
  }

  if (!sendEventPatched || !mergedConfigPatched) {
    return {
      status: 'warning',
      message: 'Constructor patch detected but supplemental patches missing',
      details,
      recommendations: [
        'Ensure patchSessionSendEvent() runs after constructor patch',
        'Check verifyPatchStatus() logs to confirm sendEvent/merged config patching',
      ],
    };
  }

  return {
    status: 'pass',
    message: 'Constructor patch applied and session internals sanitized',
    details,
    recommendations: [
      'Keep constructor patch logging enabled for production diagnostics',
      'If issues recur, review VOICE_ASSISTANT_SESSION_TYPE_FIX.md troubleshooting tree',
    ],
  };
}

/**
 * Diagnose session configuration payload
 * Detects problematic fields that OpenAI API will reject
 */
export function diagnoseSessionConfiguration(payload: any): DetailedDiagnosticResult {
  const details: Record<string, any> = {
    hasPayload: Boolean(payload),
    payloadType: typeof payload,
  };

  if (!payload || typeof payload !== 'object') {
    return {
      status: 'fail',
      message: 'Invalid session payload',
      details,
      recommendations: ['Provide a valid session configuration object'],
    };
  }

  // Check for problematic session.type field
  const payloadString = JSON.stringify(payload);
  const hasSessionType =
    payloadString.includes('"type":"realtime"') ||
    (payload.type === 'realtime' && payload.modalities);

  details.hasSessionTypeField = hasSessionType;
  details.payloadKeys = Object.keys(payload);
  details.payloadSize = payloadString.length;

  if (hasSessionType) {
    return {
      status: 'fail',
      message: 'Detected problematic session.type field',
      details: {
        ...details,
        error: 'This will cause "Unknown parameter: session.type" error from OpenAI API',
      },
      recommendations: [
        'The OpenAI Agents SDK incorrectly injects type: "realtime" into session configs',
        'This field must be stripped before sending to the API',
        'Ensure DataChannel patch is applied (for WebRTC)',
        'Or use the alternative initialization strategy (useAlternativeInitialization: true)',
      ],
    };
  }

  // Check for other potentially problematic fields
  const problematicFields: string[] = [];
  if (payload.transport) problematicFields.push('transport');
  if (payload.type && !payload.function) problematicFields.push('type (non-tool)');

  details.problematicFields = problematicFields;

  if (problematicFields.length > 0) {
    return {
      status: 'warning',
      message: `Potentially problematic fields detected: ${problematicFields.join(', ')}`,
      details,
      recommendations: [
        'Remove transport field from session config',
        'Remove type field unless it is part of a tool definition',
      ],
    };
  }

  return {
    status: 'pass',
    message: 'Session configuration appears valid',
    details,
  };
}

export function diagnoseInitializationStrategy(
  config: ModernVoiceClientConfig,
  session?: any
): DetailedDiagnosticResult {
  const requestedStrategy = config.useAlternativeInitialization ? 'alternative' : 'standard';
  const constructorPatched = Boolean(session?._constructorPatched);
  const sessionPatched = Boolean(session?._patchedForSessionType);
  const dataChannelPatched = Boolean(session?._transport?._dataChannel?._patchedForSessionType);

  const details: Record<string, any> = {
    requestedStrategy,
    constructorPatched,
    sessionPatched,
    dataChannelPatched,
  };

  if (requestedStrategy === 'standard' && !constructorPatched) {
    return {
      status: 'warning',
      message: 'Standard initialization in use without constructor patch marker',
      details,
      recommendations: [
        'Enable constructor-level patching or switch to alternative initialization',
        'Known issue: @openai/agents v0.1.3 injects session.type during session.connect()',
        'Consult VOICE_ASSISTANT_SESSION_TYPE_FIX.md for migration guidance',
      ],
    };
  }

  if (requestedStrategy === 'alternative' && !constructorPatched && !sessionPatched) {
    return {
      status: 'warning',
      message: 'Alternative initialization requested but constructor patch markers missing',
      details,
      recommendations: [
        'Ensure rebuildSessionForAlternativeInitialization() ran after enabling strategy',
        'Verify that client.updateConfig({ useAlternativeInitialization: true }) succeeded before connect()',
      ],
    };
  }

  if (requestedStrategy === 'alternative') {
    return {
      status: 'pass',
      message: 'Alternative initialization active - constructor patch optional',
      details,
      recommendations: [
        'Keep alternative mode enabled until SDK releases a fix',
        'Monitor release notes from OpenAI for session.type bug resolution',
      ],
    };
  }

  return {
    status: 'pass',
    message: 'Standard initialization with constructor patch markers detected',
    details,
    recommendations: [
      'Retain constructor patch logging to catch regressions',
      'Switch to alternative initialization if session.type errors reappear',
    ],
  };
}

/**
 * Create a comprehensive diagnostic report for the voice system
 * Includes all new diagnostics for session.type fix
 */
export function createVoiceSystemDiagnosticReport(
  config: ModernVoiceClientConfig,
  transport: any,
  payload?: any,
  sessionOverride?: any
): {
  overall: 'pass' | 'warning' | 'fail';
  credentials: DetailedDiagnosticResult;
  dataChannel: DetailedDiagnosticResult;
  constructorPatch: DetailedDiagnosticResult;
  initializationStrategy: DetailedDiagnosticResult;
  sessionConfig?: DetailedDiagnosticResult;
  summary: string;
} {
  const session = sessionOverride ?? transport?._session ?? undefined;
  const credentials = diagnoseVoiceCredentials(config);
  const dataChannel = diagnoseDataChannelPatch(transport, session);
  const constructorPatch = diagnoseSessionConstructorPatch(session);
  const initializationStrategy = diagnoseInitializationStrategy(config, session);
  const sessionConfig = payload ? diagnoseSessionConfiguration(payload) : undefined;

  // Determine overall status
  const results = [credentials, dataChannel, constructorPatch, initializationStrategy, sessionConfig]
    .filter(Boolean) as DetailedDiagnosticResult[];
  const hasFail = results.some(r => r.status === 'fail');
  const hasWarning = results.some(r => r.status === 'warning');

  const overall = hasFail ? 'fail' : hasWarning ? 'warning' : 'pass';

  // Generate summary
  const summaryParts: string[] = [];
  if (credentials.status !== 'pass') {
    summaryParts.push(`Credentials: ${credentials.message}`);
  }
  if (dataChannel.status !== 'pass') {
    summaryParts.push(`DataChannel: ${dataChannel.message}`);
  }
  if (constructorPatch.status !== 'pass') {
    summaryParts.push(`Constructor Patch: ${constructorPatch.message}`);
  }
  if (initializationStrategy.status !== 'pass') {
    summaryParts.push(`Initialization: ${initializationStrategy.message}`);
  }
  if (sessionConfig && sessionConfig.status !== 'pass') {
    summaryParts.push(`Session Config: ${sessionConfig.message}`);
  }

  const summary =
    summaryParts.length > 0 ? summaryParts.join('; ') : 'All diagnostics passed';

  return {
    overall,
    credentials,
    dataChannel,
    constructorPatch,
    initializationStrategy,
    sessionConfig,
    summary,
  };
}
