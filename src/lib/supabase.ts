import { createClient } from '@supabase/supabase-js';

import { appEnv } from '@/lib/config/env';
import { devAuth } from '@/lib/dev-auth';

/**
 * Validate Supabase connection configuration
 */
function validateSupabaseConnection() {
  const supabaseUrl = appEnv.supabase.url;
  const supabaseAnonKey = appEnv.supabase.anonKey;

  // Basic validation without exposing secrets
  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Missing Supabase env vars', {
      hasUrl: !!supabaseUrl,
      hasAnonKey: !!supabaseAnonKey,
    });
    throw new Error('Missing Supabase environment variables. Please check your .env file.');
  }

  // Additional validation for local development
  if (appEnv.isDevelopment) {
    const localDockerUrls = ['http://127.0.0.1:54321', 'http://localhost:54321'];
    const isLocalDocker = localDockerUrls.some(url => supabaseUrl.startsWith(url));

    if (isLocalDocker) {
      console.info('✅ [Supabase] Local Docker connection detected');
    } else if (supabaseUrl.includes('supabase.co')) {
      console.warn('⚠️ [Supabase] Development environment using remote Supabase instance');
    }
  }

  return { supabaseUrl, supabaseAnonKey };
}

/**
 * Get sanitized connection information for debugging
 */
function getSupabaseConnectionInfo() {
  try {
    const host = new URL(appEnv.supabase.url).host;
    const keyLen = appEnv.supabase.anonKey.length;
    const isLocal = appEnv.supabase.url.includes('127.0.0.1') || appEnv.supabase.url.includes('localhost');

    return {
      host,
      keyLength: keyLen,
      isLocalDocker: isLocal,
      environment: appEnv.mode,
    };
  } catch (error) {
    console.warn('[Supabase] Could not parse connection info:', error);
    return null;
  }
}

// Validate and get connection configuration
const { supabaseUrl, supabaseAnonKey } = validateSupabaseConnection();

// Log connection info for debugging
const connectionInfo = getSupabaseConnectionInfo();
if (connectionInfo) {
  console.info('[Supabase] Connection info:', connectionInfo);
}

// Client-side Supabase instance MUST use the anon key
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  db: { schema: 'public' },
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  },
});

if (devAuth.enabled) {
  console.info('⚙️ [Supabase] Dev auth bypass enabled (VITE_SKIP_AUTH)');

  (supabase.auth.getSession as unknown) = async () => ({
    data: { session: devAuth.session },
    error: null,
  });

  (supabase.auth.getUser as unknown) = async (_jwt?: string) => ({
    data: { user: devAuth.user },
    error: null,
  });

  (supabase.auth.onAuthStateChange as unknown) = async (callback: unknown) => {
    const subscription = {
      unsubscribe: () => {},
    };

    if (typeof callback === 'function') {
      setTimeout(() => {
        try {
          (callback as (event: string, session: unknown) => void)('SIGNED_IN', devAuth.session);
        } catch (error) {
          console.warn('[Supabase] Dev auth callback error:', error);
        }
      }, 0);
    }

    return { data: { subscription }, error: null };
  };

  (supabase.auth.signOut as unknown) = async () => ({ error: null });
}

// Additional validation for local Docker connection issues
if (appEnv.isDevelopment && connectionInfo?.isLocalDocker) {
  // Test basic connectivity with a table that actually exists (non-blocking)
  supabase.from('products').select('count', { count: 'exact', head: true })
    .then(({ error }) => {
      if (error) {
        console.warn('⚠️ [Supabase] Local Docker connection test failed:', error.message);
        console.info('💡 [Supabase] Ensure local Supabase Docker is running: supabase start');
      } else {
        console.info('✅ [Supabase] Local Docker connection test successful');
      }
    })
    .catch(() => {
      // Silently handle connection test failures
    });
}

/**
 * Export connection validation functions for testing and debugging
 */
export { validateSupabaseConnection, getSupabaseConnectionInfo };

// Note: Service role key must NEVER be used in the browser. For privileged
// operations, use server-side scripts or dedicated service functions.
