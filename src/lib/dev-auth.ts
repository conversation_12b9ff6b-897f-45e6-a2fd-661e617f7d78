import type { Session, User } from '@supabase/supabase-js';

import { appEnv } from '@/lib/config/env';

const enabled = appEnv.isDevelopment && appEnv.featureFlags.skipAuth;

const nowIso = new Date().toISOString();

const devUser = {
  id: '00000000-0000-0000-0000-000000000000',
  aud: 'authenticated',
  role: 'authenticated',
  email: '<EMAIL>',
  email_confirmed_at: nowIso,
  phone: '',
  confirmed_at: nowIso,
  last_sign_in_at: nowIso,
  created_at: nowIso,
  updated_at: nowIso,
  identities: [],
  invited_at: null,
  new_email: null,
  new_phone: null,
  action_link: null,
  recovery_sent_at: null,
  email_change_sent_at: null,
  email_change_confirm_status: 0,
  phone_confirmed_at: null,
  app_metadata: {
    provider: 'local-dev',
    providers: ['local-dev'],
  },
  user_metadata: {
    name: 'Local Dev User',
  },
  factors: [],
} as unknown as User;

const futureExpiry = Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 365;

const devSession = {
  access_token: 'dev-access-token',
  token_type: 'bearer',
  expires_in: 60 * 60 * 24,
  expires_at: futureExpiry,
  refresh_token: 'dev-refresh-token',
  user: devUser,
  provider_token: null,
  provider_refresh_token: null,
} as unknown as Session;

export const devAuth = Object.freeze({
  enabled,
  user: devUser,
  session: devSession,
});
