/**
 * Environment Configuration for TempStick Production Deployment
 *
 * Manages environment variables, feature flags, and configuration
 * settings for different deployment environments.
 */

import { appEnv } from './env';

export interface EnvironmentConfig {
  supabase: {
    url: string;
    anonKey: string;
    serviceRoleKey?: string;
  };
  tempstick: {
    apiUrl: string;
    apiKey: string;
    webhookUrl?: string;
    syncInterval: number; // milliseconds
    allowedOrigins?: string[];
  };
  monitoring: {
    enabled: boolean;
    datadog?: {
      apiKey: string;
      appKey: string;
    };
    sentry?: {
      dsn: string;
    };
    logLevel: 'debug' | 'info' | 'warn' | 'error';
  };
  notifications: {
    email: {
      enabled: boolean;
      smtpHost?: string;
      smtpPort?: number;
      username?: string;
      password?: string;
      fromAddress: string;
    };
    sms: {
      enabled: boolean;
      provider: 'twilio' | 'aws-sns';
      config: Record<string, string>;
    };
    slack: {
      enabled: boolean;
      webhookUrl?: string;
      botToken?: string;
    };
  };
  features: {
    temperatureMonitoring: boolean;
    realTimeAlerts: boolean;
    advancedReporting: boolean;
    autoScaling: boolean;
    performanceMonitoring: boolean;
  };
  performance: {
    maxSensorsPerSync: number;
    maxDataPointsPerRequest: number;
    cacheTimeoutMs: number;
    connectionPoolSize: number;
  };
  security: {
    enableRateLimiting: boolean;
    maxRequestsPerMinute: number;
    enableCors: boolean;
    trustedDomains: string[];
  };
  realtime: {
    model: string;
    voice: string;
    transport: {
      directRealtime: boolean;
      useWebRtc: boolean;
    };
  };
}

/**
 * Get environment configuration based on current environment
 */
function getEnvironmentConfig(): EnvironmentConfig {
  const isDev = appEnv.isDevelopment;
  const isProd = appEnv.isProduction;

  // Base configuration
  const baseConfig: EnvironmentConfig = {
    supabase: {
      url: appEnv.supabase.url,
      anonKey: appEnv.supabase.anonKey,
      serviceRoleKey: appEnv.supabase.serviceRoleKey,
    },
    tempstick: {
      apiUrl: appEnv.tempstick.apiUrl,
      apiKey: appEnv.tempstick.apiKey,
      webhookUrl: appEnv.tempstick.webhookUrl,
      syncInterval: appEnv.tempstick.syncIntervalMs,
      allowedOrigins: appEnv.tempstick.allowedOrigins,
    },
    monitoring: {
      enabled: !isDev,
      datadog: {
        apiKey: appEnv.monitoring.datadogApiKey ?? '',
        appKey: appEnv.monitoring.datadogAppKey ?? '',
      },
      sentry: {
        dsn: appEnv.monitoring.sentryDsn ?? '',
      },
      logLevel: isDev ? 'debug' : isProd ? 'warn' : 'info',
    },
    notifications: {
      email: {
        enabled: !isDev,
        smtpHost: appEnv.notifications.smtp.host,
        smtpPort: appEnv.notifications.smtp.port,
        username: appEnv.notifications.smtp.username,
        password: appEnv.notifications.smtp.password,
        fromAddress: appEnv.notifications.smtp.from,
      },
      sms: {
        enabled: !isDev,
        provider: appEnv.notifications.sms.provider,
        config: {
          accountSid: appEnv.notifications.sms.accountSid ?? '',
          authToken: appEnv.notifications.sms.authToken ?? '',
          fromNumber: appEnv.notifications.sms.fromNumber ?? '',
        },
      },
      slack: {
        enabled: !isDev,
        webhookUrl: appEnv.notifications.slack.webhookUrl,
        botToken: appEnv.notifications.slack.botToken,
      },
    },
    features: {
      temperatureMonitoring: true,
      realTimeAlerts: !isDev,
      advancedReporting: true,
      autoScaling: isProd,
      performanceMonitoring: !isDev,
    },
    performance: {
      maxSensorsPerSync: isDev ? 5 : isProd ? 100 : 20,
      maxDataPointsPerRequest: isDev ? 100 : isProd ? 1000 : 500,
      cacheTimeoutMs: isDev ? 30000 : 300000, // 30s dev, 5min prod
      connectionPoolSize: isDev ? 5 : isProd ? 20 : 10,
    },
    security: {
      enableRateLimiting: !isDev,
      maxRequestsPerMinute: isDev ? 1000 : isProd ? 100 : 200,
      enableCors: true,
      trustedDomains: isDev
        ? ['http://localhost:5177', 'http://localhost:3000']
        : isProd
          ? ['https://seafoodmanager.com', 'https://www.seafoodmanager.com']
          : ['https://staging.seafoodmanager.com'],
    },
    realtime: {
      model: appEnv.realtime.model,
      voice: appEnv.realtime.voice,
      transport: {
        directRealtime: appEnv.featureFlags.directRealtime,
        useWebRtc: appEnv.featureFlags.useWebRtc,
      },
    },
  };

  return baseConfig;
}

function redactSecret(value?: string): string {
  if (!value) {
    return 'not-set';
  }

  if (value.length <= 4) {
    return `${value.charAt(0)}***`;
  }

  return `${value.slice(0, 4)}…${value.slice(-2)}`;
}

/**
 * Validate required environment variables
 */
function validateEnvironment(): void {
  const config = getEnvironmentConfig();
  const errors: string[] = [];

  // Required variables
  if (!config.supabase.url) {
    errors.push('VITE_SUPABASE_URL is required');
  }
  if (!config.supabase.anonKey) {
    errors.push('VITE_SUPABASE_ANON_KEY is required');
  }
  if (!config.tempstick.apiKey) {
    errors.push('VITE_TEMPSTICK_API_KEY is required');
  }
  if (!config.realtime.model) {
    errors.push('VITE_REALTIME_MODEL is required');
  }
  if (!config.realtime.voice) {
    errors.push('VITE_REALTIME_VOICE is required');
  }

  if (config.realtime.transport.directRealtime && !appEnv.openAi.apiKey) {
    errors.push('OPENAI_API_KEY is required when VITE_FEATURE_DIRECT_REALTIME is enabled');
  }

  // Production-specific validations
  if (appEnv.isProduction) {
    if (!config.monitoring.sentry?.dsn) {
      errors.push('VITE_SENTRY_DSN is required in production');
    }
    if (!config.notifications.email.smtpHost) {
      errors.push('VITE_SMTP_HOST is required in production');
    }
  }

  if (errors.length > 0) {
    console.error('Environment validation failed:');
    errors.forEach((error) => console.error(`  - ${error}`));
    throw new Error(`Missing required environment variables: ${errors.join(', ')}`);
  }
}

export async function checkExternalIntegrations(): Promise<void> {
  if (!isDevelopment()) {
    return;
  }

  if (typeof fetch !== 'function') {
    log('debug', 'Skipping integration diagnostics: fetch API not available in this environment');
    return;
  }

  const config = environmentConfig;

  log('info', 'Running development integration diagnostics', {
    tempstick: {
      apiUrl: config.tempstick.apiUrl,
      apiKey: redactSecret(config.tempstick.apiKey),
      allowedOrigins: config.tempstick.allowedOrigins ?? [],
    },
    realtime: {
      model: config.realtime.model,
      voice: config.realtime.voice,
      transport: config.realtime.transport,
      openAiKeyStatus: redactSecret(appEnv.openAi.apiKey),
    },
  });

  const withTimeout = async <T>(executor: (signal: AbortSignal) => Promise<T>, timeoutMs: number) => {
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), timeoutMs);
    try {
      return await executor(controller.signal);
    } finally {
      clearTimeout(timeout);
    }
  };

  try {
    const response = await withTimeout(
      (signal) => fetch('/api/tempstick/health', { method: 'GET', signal }),
      5000
    );
    if (response.ok) {
      log('info', 'TempStick proxy health check succeeded', { status: response.status });
    } else {
      log('warn', 'TempStick proxy health check returned non-200 status', { status: response.status });
    }
  } catch (error) {
    const message = error instanceof Error ? error.message : String(error);
    log('warn', 'TempStick proxy health check failed', message);
  }

  try {
    const params = new URLSearchParams({
      model: config.realtime.model,
      voice: config.realtime.voice,
    });
    const response = await withTimeout(
      (signal) => fetch(`/api/voice/ephemeral-token?${params.toString()}`, {
        method: 'POST',
        headers: { Accept: 'application/json' },
        signal,
      }),
      5000
    );

    if (response.ok) {
      log('info', 'OpenAI relay health check succeeded', { status: response.status });
    } else {
      log('warn', 'OpenAI relay health check returned non-200 status', { status: response.status });
    }
  } catch (error) {
    const message = error instanceof Error ? error.message : String(error);
    log('warn', 'OpenAI relay health check failed', message);
  }
}

/**
 * Get feature flag value
 */
export function getFeatureFlag(feature: keyof EnvironmentConfig['features']): boolean {
  return environmentConfig.features[feature];
}

/**
 * Get performance setting
 */
export function getPerformanceSetting<K extends keyof EnvironmentConfig['performance']>(
  setting: K
): EnvironmentConfig['performance'][K] {
  return environmentConfig.performance[setting];
}

/**
 * Check if environment is development
 */
export function isDevelopment(): boolean {
  return appEnv.isDevelopment;
}

/**
 * Check if environment is staging
 */
export function isStaging(): boolean {
  return appEnv.isStaging;
}

/**
 * Check if environment is production
 */
export function isProduction(): boolean {
  return appEnv.isProduction;
}

/**
 * Get current environment name
 */
export function getEnvironmentName(): string {
  if (isDevelopment()) return 'development';
  if (isStaging()) return 'staging';
  if (isProduction()) return 'production';
  return 'unknown';
}

/**
 * Environment-specific logging
 */
export function log(
  level: 'debug' | 'info' | 'warn' | 'error',
  message: string,
  ...args: unknown[]
): void {
  const config = getEnvironmentConfig();
  const logLevels = ['debug', 'info', 'warn', 'error'];
  const currentLevelIndex = logLevels.indexOf(config.monitoring.logLevel);
  const messageLevelIndex = logLevels.indexOf(level);

  if (messageLevelIndex >= currentLevelIndex) {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level.toUpperCase()}] [${getEnvironmentName()}]`;

    switch (level) {
      case 'debug':
        console.debug(prefix, message, ...args);
        break;
      case 'info':
        console.info(prefix, message, ...args);
        break;
      case 'warn':
        console.warn(prefix, message, ...args);
        break;
      case 'error':
        console.error(prefix, message, ...args);
        break;
    }
  }
}

/**
 * Initialize environment and validate configuration
 */
export function initializeEnvironment(): EnvironmentConfig {
  try {
    validateEnvironment();
    const config = getEnvironmentConfig();

    log('info', `Environment initialized: ${getEnvironmentName()}`);
    log('debug', 'Configuration loaded:', {
      features: config.features,
      performance: config.performance,
      monitoring: { enabled: config.monitoring.enabled },
    });

    if (appEnv.isDevelopment) {
      void checkExternalIntegrations().catch((error) => {
        const message = error instanceof Error ? error.message : String(error);
        log('warn', 'Integration diagnostics encountered an error', message);
      });
    }

    return config;
  } catch (error) {
    console.error('Failed to initialize environment:', error);
    throw error;
  }
}

// Export the configuration instance
export const environmentConfig = getEnvironmentConfig();

// Auto-initialize in non-test environments
if (typeof window !== 'undefined' && !appEnv.isVitest) {
  try {
    initializeEnvironment();
  } catch (error) {
    console.error('Environment initialization failed:', error);
  }
}

export default environmentConfig;
