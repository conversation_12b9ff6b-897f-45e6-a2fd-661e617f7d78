/**
 * Voice Authentication Manager
 * Centralized authentication management for voice operations
 * Handles session validation, database client management, and permission checking
 */

import type { SupabaseClient, User, Session } from '@supabase/supabase-js';
import { supabase } from './supabase';

export interface VoiceAuthStatus {
  isAuthenticated: boolean;
  user?: User;
  session?: Session;
  error?: string;
  isValidating?: boolean;
  lastValidated?: Date;
}

export interface VoiceAuthResult {
  success: boolean;
  status: VoiceAuthStatus;
  client?: SupabaseClient;
  message?: string;
}

/**
 * Voice Authentication Manager Class
 * Manages authentication state and database clients for voice operations
 */
export class VoiceAuthManager {
  private _status: VoiceAuthStatus = { isAuthenticated: false };
  private _client: SupabaseClient | null = null;
  private _callbacks: Set<(status: VoiceAuthStatus) => void> = new Set();
  private _validationTimer: NodeJS.Timeout | null = null;

  constructor() {
    // Start periodic validation
    this.startPeriodicValidation();
  }

  /**
   * Get current authentication status
   */
  get status(): VoiceAuthStatus {
    return { ...this._status };
  }

  /**
   * Get authenticated Supabase client
   */
  get client(): SupabaseClient {
    return this._client || supabase;
  }

  /**
   * Check if user is authenticated for voice operations
   */
  get isAuthenticated(): boolean {
    return this._status.isAuthenticated;
  }

  /**
   * Initialize authentication for voice operations
   */
  async initialize(): Promise<VoiceAuthResult> {
    try {
      this.updateStatus({ isValidating: true });

      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError) {
        const status: VoiceAuthStatus = {
          isAuthenticated: false,
          error: `Session error: ${sessionError.message}`,
          lastValidated: new Date(),
        };
        this.updateStatus(status);
        return { success: false, status, message: 'Failed to get session' };
      }

      if (!session?.user) {
        const status: VoiceAuthStatus = {
          isAuthenticated: false,
          error: 'No active user session',
          lastValidated: new Date(),
        };
        this.updateStatus(status);
        return { success: false, status, message: 'User not authenticated' };
      }

      // Validate database connectivity
      const connectivityResult = await this.validateDatabaseConnectivity(supabase);
      if (!connectivityResult.success) {
        const status: VoiceAuthStatus = {
          isAuthenticated: false,
          user: session.user,
          session,
          error: connectivityResult.error,
          lastValidated: new Date(),
        };
        this.updateStatus(status);
        return { success: false, status, message: 'Database connectivity failed' };
      }

      // Validate voice permissions
      const permissionResult = await this.validateVoicePermissions(session.user);
      if (!permissionResult.success) {
        const status: VoiceAuthStatus = {
          isAuthenticated: false,
          user: session.user,
          session,
          error: permissionResult.error,
          lastValidated: new Date(),
        };
        this.updateStatus(status);
        return { success: false, status, message: 'Voice permissions validation failed' };
      }

      // All validations passed
      this._client = supabase;
      const status: VoiceAuthStatus = {
        isAuthenticated: true,
        user: session.user,
        session,
        lastValidated: new Date(),
      };
      this.updateStatus(status);

      console.log('✅ Voice authentication initialized successfully:', session.user.email);
      console.log('   Note: This confirms DATABASE authentication is working.');
      console.log('   If voice assistant fails to connect, the issue is likely with OpenAI API connection, not database access.');
      return {
        success: true,
        status,
        client: this._client,
        message: `Voice authentication active for ${session.user.email} - Database access verified`
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const status: VoiceAuthStatus = {
        isAuthenticated: false,
        error: errorMessage,
        lastValidated: new Date(),
      };
      this.updateStatus(status);

      console.error('🚫 Voice authentication initialization failed:', error);
      return { success: false, status, message: 'Authentication initialization failed' };
    }
  }

  /**
   * Validate database connectivity
   */
  async validateDatabaseConnectivity(client: SupabaseClient): Promise<{ success: boolean; error?: string }> {
    try {
      // Test basic connectivity
      const { error } = await client
        .from('inventory_events')
        .select('id')
        .limit(1);

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows, which is OK
        return { success: false, error: `Database connectivity test failed: ${error.message}` };
      }

      console.log('✅ Database connectivity verified - RLS policies allow voice operations');
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Database connectivity test failed'
      };
    }
  }

  /**
   * Validate voice-specific permissions
   */
  async validateVoicePermissions(user: User): Promise<{ success: boolean; error?: string }> {
    try {
      // Test if user can access voice inventory functions
      const testData = {
        event_type: 'receiving',
        name: '__voice_test__',
        category: 'finfish',
        quantity: 0,
        unit: 'test',
        created_by_voice: true,
        metadata: { test: true }
      };

      // Try to perform a dry-run insert (this tests RLS policies without actually inserting)
      const { error } = await supabase
        .from('inventory_events')
        .insert([testData])
        .select()
        .single();

      // If the error is about RLS policy violations, that's what we're checking for
      if (error && (error.code === '42501' || error.message.includes('RLS'))) {
        console.error('❌ Voice inventory operations blocked by RLS policies');
        console.error('   This is a DATABASE PERMISSION issue, not an OpenAI API issue');
        return {
          success: false,
          error: 'Voice inventory operations blocked by RLS policies - run migration to fix'
        };
      }

      // Other errors are acceptable for this test (we're not actually trying to insert)
      console.log('✅ Voice permissions validated - RLS policies allow inventory operations');
      return { success: true };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Permission validation failed'
      };
    }
  }

  /**
   * Refresh authentication status
   */
  async refresh(): Promise<VoiceAuthResult> {
    return await this.initialize();
  }

  /**
   * Handle session refresh and re-authentication
   */
  async handleSessionRefresh(): Promise<VoiceAuthResult> {
    try {
      const { data: { session }, error } = await supabase.auth.refreshSession();

      if (error || !session) {
        const status: VoiceAuthStatus = {
          isAuthenticated: false,
          error: 'Session refresh failed',
          lastValidated: new Date(),
        };
        this.updateStatus(status);
        return { success: false, status, message: 'Session refresh failed' };
      }

      return await this.initialize();
    } catch (error) {
      const status: VoiceAuthStatus = {
        isAuthenticated: false,
        error: error instanceof Error ? error.message : 'Session refresh error',
        lastValidated: new Date(),
      };
      this.updateStatus(status);
      return { success: false, status, message: 'Session refresh error' };
    }
  }

  /**
   * Subscribe to authentication status changes
   */
  subscribe(callback: (status: VoiceAuthStatus) => void): () => void {
    this._callbacks.add(callback);

    // Return unsubscribe function
    return () => {
      this._callbacks.delete(callback);
    };
  }

  /**
   * Check if user has specific voice inventory permissions
   */
  async checkInventoryPermissions(operation: 'create' | 'read' | 'update' | 'delete'): Promise<boolean> {
    if (!this.isAuthenticated) {
      return false;
    }

    try {
      // For now, assume authenticated users have all permissions
      // This can be expanded to check specific role-based permissions
      return true;
    } catch (error) {
      console.error('Error checking inventory permissions:', error);
      return false;
    }
  }

  /**
   * Get authentication headers for API requests
   */
  getAuthHeaders(): Record<string, string> {
    if (!this._status.session?.access_token) {
      return {};
    }

    return {
      'Authorization': `Bearer ${this._status.session.access_token}`,
    };
  }

  /**
   * Start periodic validation of authentication status
   */
  private startPeriodicValidation(): void {
    // Validate every 5 minutes
    this._validationTimer = setInterval(async () => {
      if (this.isAuthenticated) {
        await this.refresh();
      }
    }, 5 * 60 * 1000);
  }

  /**
   * Stop periodic validation
   */
  destroy(): void {
    if (this._validationTimer) {
      clearInterval(this._validationTimer);
      this._validationTimer = null;
    }
    this._callbacks.clear();
  }

  /**
   * Update authentication status and notify subscribers
   */
  private updateStatus(status: Partial<VoiceAuthStatus>): void {
    this._status = { ...this._status, ...status };

    // Notify all subscribers
    this._callbacks.forEach(callback => {
      try {
        callback(this._status);
      } catch (error) {
        console.error('Error in voice auth status callback:', error);
      }
    });
  }
}

// Singleton instance
export const voiceAuthManager = new VoiceAuthManager();

// Convenience functions for common operations
export async function initializeVoiceAuth(): Promise<VoiceAuthResult> {
  return await voiceAuthManager.initialize();
}

export function getVoiceAuthStatus(): VoiceAuthStatus {
  return voiceAuthManager.status;
}

export function getVoiceAuthClient(): SupabaseClient {
  return voiceAuthManager.client;
}

export function isVoiceAuthenticated(): boolean {
  return voiceAuthManager.isAuthenticated;
}

export function subscribeToVoiceAuth(callback: (status: VoiceAuthStatus) => void): () => void {
  return voiceAuthManager.subscribe(callback);
}

// Helper function to handle common authentication errors
export function getAuthErrorMessage(error: string | Error): string {
  const message = typeof error === 'string' ? error : error.message;

  if (message.includes('JWT') || message.includes('token')) {
    return 'Authentication token expired - please log in again';
  }

  if (message.includes('RLS') || message.includes('policy')) {
    return 'Database access permissions insufficient - check RLS policies';
  }

  if (message.includes('network') || message.includes('fetch')) {
    return 'Network connection failed - check internet connectivity';
  }

  if (message.includes('session')) {
    return 'User session invalid - please log in again';
  }

  return message;
}
