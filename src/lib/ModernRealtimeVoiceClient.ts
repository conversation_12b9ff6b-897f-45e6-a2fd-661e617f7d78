import {
  OpenAIRealtimeWebRTC,
  OpenAIRealtimeWebSocket,
  RealtimeAgent,
  RealtimeSession,
} from '@openai/agents/realtime';
import type { RealtimeSessionOptions } from '@openai/agents/realtime';
import {
  queryInventoryTool,
  queryRecentEventsTool,
  queryVendorsTool,
  queryCustomersTool,
  queryPartnersTool,
  calculateStockLevelsTool,
  addInventoryTool,
  updateInventoryTool,
  getTemperatureTool,
  fillFormFieldTool,
  submitFormTool,
  clearFormTool,
  getFormFieldsTool,
  getCurrentViewTool,
  setAuthenticatedClient
} from './realtime-tools';
import { voiceAuthManager } from './voice-auth-manager';
import { Logger } from './debug-utils';

type SDKRealtimeSession = RealtimeSession & {
  sendEvent?: (event: Record<string, unknown>) => Promise<void> | void;
  update?: (config: Record<string, unknown>) => Promise<void> | void;
};

/**
 * Modern 2025 OpenAI Agents SDK Voice Client for Seafood Inventory Management
 * Uses the official @openai/agents package with RealtimeAgent and RealtimeSession
 */

export interface ModernVoiceClientConfig {
  apiKey?: string;
  ephemeralToken?: string;
  relayUrl?: string;
  transport?: 'webrtc' | 'websocket';
  model?: string;
  voice?: string;
  enableDebugLogs?: boolean;
  useInsecureApiKey?: boolean;
  disableFallback?: boolean;
  forceTransport?: 'webrtc' | 'websocket';
  useAlternativeInitialization?: boolean; // Alternative strategy to avoid manual session.update() calls
}

export interface ModernVoiceClientEvents {
  onConnected: () => void;
  onDisconnected: () => void;
  onListening: () => void;
  onProcessing: () => void;
  onTranscript: (transcript: string, isFinal: boolean) => void;
  onResponse: (text: string) => void;
  onError: (error: string) => void;
  onToolCall: (toolName: string, args: Record<string, unknown>, result: unknown) => void;
}

// Tools are now imported from realtime-tools.ts using the official SDK patterns

export class ModernRealtimeVoiceClient {
  private agent!: RealtimeAgent;
  private session: RealtimeSession | null = null;
  private config: ModernVoiceClientConfig;
  private events: Partial<ModernVoiceClientEvents> = {};
  private isConnected = false;
  private transport: OpenAIRealtimeWebRTC | OpenAIRealtimeWebSocket;
  private pendingSessionConfig: Record<string, unknown> | null = null;
  private audioContext: AudioContext | null = null;
  private audioQueueTime = 0;
  private micStream: MediaStream | null = null;
  private micAudioContext: AudioContext | null = null;
  private micSource: MediaStreamAudioSourceNode | null = null;
  private micProcessor: ScriptProcessorNode | null = null;
  private micGainNode: GainNode | null = null;
  private userSpeaking = false;
  private lastVoiceActivity = 0;
  private readonly silenceTimeoutMs = 800;
  private readonly voiceActivityThreshold = 0.015;
  private connectionState: 'disconnected' | 'connecting' | 'connected' | 'failed' = 'disconnected';
  private iceConnectionState: string = 'new';
  private toolRegistrationStatus: 'pending' | 'success' | 'failed' = 'pending';
  private isResponseInProgress = false;
  private responseQueue: string[] = [];
  private log = Logger.voice('ModernRealtimeVoiceClient');
  private connectionLog: string[] = [];
  private patchInvocationCounters = {
    constructor: 0,
    sessionSend: 0,
    transport: 0,
    dataChannel: 0,
  };
  private constructorPatchFailed = false;

  constructor(config: ModernVoiceClientConfig, events: Partial<ModernVoiceClientEvents> = {}) {
    this.config = config;
    this.events = events;

    // Validate tool registration and authentication setup
    this.validateToolSetup();

    if (this.config.useAlternativeInitialization) {
      this.initializeAgentWithFullConfig();
    }

    if (!this.agent) {
      // CRITICAL FIX: Create RealtimeAgent with MINIMAL configuration
      // DO NOT pass instructions, voice, model, or session config here
      // Passing instructions causes the SDK to automatically send session.update
      // with 'type: realtime' which OpenAI API rejects with "Unknown parameter" error
      //
      // Tools MUST be registered here for proper tool invocation
      // Instructions will be sent via session.update after connection instead
      this.agent = new RealtimeAgent({
        name: 'SeafoodInventoryAssistant',
        tools: this.getAssistantTools(),
      });
    }

    this.logConnectionEvent('info', '🔍 [CONSTRUCTOR] RealtimeAgent created', {
      toolsRegistered: this.agent.tools?.length ?? 0,
    });

    // Determine transport mode respecting explicit config values
    const targetModel = this.config.model ?? 'gpt-4o-realtime-preview-2024-12-17';

    let resolvedTransport: 'webrtc' | 'websocket';
    if (this.config.forceTransport === 'webrtc' || this.config.forceTransport === 'websocket') {
      resolvedTransport = this.config.forceTransport;
    } else if (this.config.transport === 'webrtc' || this.config.transport === 'websocket') {
      resolvedTransport = this.config.transport;
    } else if (this.config.relayUrl) {
      resolvedTransport = 'websocket';
    } else {
      resolvedTransport = 'websocket';
    }

    this.config.transport = resolvedTransport;
    const useWebSocket = resolvedTransport === 'websocket';

    // Expose model for fetch patch to ensure ?model=... is always present when rewriting
    if (typeof window !== 'undefined') {
      try {
        (window as unknown as Record<string, unknown>).__openai_realtime_model = targetModel;
      } catch {
        // Ignore errors setting debug model
      }
    }

    // Determine API key for transport configuration
    const isRelay = Boolean(this.config.relayUrl);
    const apiKey = this.config.ephemeralToken
      ?? this.config.apiKey
      ?? (isRelay ? 'relay' : undefined);

    if (useWebSocket) {
      this.transport = new OpenAIRealtimeWebSocket({
        model: targetModel,
        apiKey,
        useInsecureApiKey: this.config.useInsecureApiKey ?? true,
      });
      this.logConnectionEvent('info', '🔍 [CONSTRUCTOR] WebSocket transport created', {
        model: targetModel,
        isRelay,
      });
    } else {
      // WebRTC configuration: include API key and model for proper authentication
      this.transport = new OpenAIRealtimeWebRTC({
        model: targetModel,
        apiKey,
        useInsecureApiKey: this.config.useInsecureApiKey ?? true,
        // SDK handles media stream setup automatically
      });
      this.logConnectionEvent('info', '🔍 [CONSTRUCTOR] WebRTC transport created', {
        model: targetModel,
        isRelay,
      });
    }

    // Configure session options conditionally based on transport mode
    const isWebRTC = !useWebSocket;
    const sessionOptions: Partial<RealtimeSessionOptions> = isWebRTC ? {
      // For WebRTC, pass the transport instance to ensure proper wiring
      transport: this.transport,
    } : {
      // WebSocket relay mode needs transport instance
      transport: this.transport,
    };

    this.session = new RealtimeSession(this.agent, sessionOptions);
    this.logConnectionEvent('info', '🔍 [CONSTRUCTOR] RealtimeSession instantiated', {
      transport: this.isUsingWebRTCTransport() ? 'webrtc' : 'websocket',
    });
    this.patchRealtimeSessionConstructor();
    this.patchSessionSendEvent();
    this.setupEventListeners();

    if (this.config.enableDebugLogs) {
      this.log.info('🎤 Modern RealtimeVoiceClient initialized with OpenAI Agents SDK');
    }
  }

  private logConnectionEvent(
    level: 'info' | 'warn' | 'error',
    message: string,
    data?: unknown
  ): void {
    const timestamp = new Date().toISOString();
    const normalizedLevel = level.toUpperCase();
    const sanitizedData = data !== undefined ? this.sanitizeLogData(data) : undefined;
    const entry = sanitizedData !== undefined
      ? `${timestamp} [${normalizedLevel}] ${message} ${JSON.stringify(sanitizedData)}`
      : `${timestamp} [${normalizedLevel}] ${message}`;
    this.connectionLog.push(entry);
    if (this.connectionLog.length > 500) {
      this.connectionLog.shift();
    }

    const shouldLog = level === 'error' || this.config.enableDebugLogs;
    const logger = (this.log as Record<string, (msg: string, payload?: unknown) => void>)[level];
    if (shouldLog && typeof logger === 'function') {
      if (sanitizedData !== undefined) {
        logger.call(this.log, message, sanitizedData);
      } else {
        logger.call(this.log, message);
      }
    }
  }

  private sanitizeLogData(data: unknown, visited = new WeakSet<object>()): unknown {
    if (data === null || data === undefined) {
      return data;
    }

    if (typeof data === 'string') {
      const secretRegex = /(sk-|session|token|key|authorization)/i;
      if (secretRegex.test(data)) {
        return '[REDACTED]';
      }
      return data.length > 1000 ? `${data.slice(0, 1000)}...` : data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeLogData(item, visited));
    }

    if (typeof data === 'object') {
      if (visited.has(data as object)) {
        return '[Circular]';
      }
      visited.add(data as object);
      const result: Record<string, unknown> = {};
      for (const [key, value] of Object.entries(data as Record<string, unknown>)) {
        if (/token|secret|key|authorization/i.test(key)) {
          result[key] = '[REDACTED]';
        } else {
          result[key] = this.sanitizeLogData(value, visited);
        }
      }
      return result;
    }

    return data;
  }

  private containsRealtimeType(value: unknown, visited = new WeakSet<object>()): boolean {
    if (value === null || value === undefined) {
      return false;
    }

    if (typeof value === 'object') {
      if (visited.has(value as object)) {
        return false;
      }
      visited.add(value as object);
    }

    if (Array.isArray(value)) {
      return value.some(item => this.containsRealtimeType(item, visited));
    }

    if (typeof value === 'object') {
      for (const [key, nested] of Object.entries(value as Record<string, unknown>)) {
        if (key === 'type' && nested === 'realtime') {
          return true;
        }
        if (this.containsRealtimeType(nested, visited)) {
          return true;
        }
      }
    }

    return false;
  }

  private dumpSessionState(): Record<string, unknown> {
    if (!this.session) {
      return { session: null };
    }

    const session = (this.session as unknown) as Record<string, unknown> & { [key: string]: unknown };
    const snapshot: Record<string, unknown> = {
      hasConstructorPatch: Boolean((session as any)._constructorPatched),
      hasSendEventPatch: Boolean((session as any)._patchedForSessionType),
      transport: this.isUsingWebRTCTransport() ? 'webrtc' : 'websocket',
    };

    const keysToCapture = ['_config', '_sessionConfig', 'config', '_pendingSessionUpdate'];

    for (const key of keysToCapture) {
      if (key in session) {
        snapshot[key] = this.sanitizeLogData(session[key]);
      }
    }

    try {
      if (typeof (session as any)._getMergedSessionConfig === 'function') {
        const merged = (session as any)._getMergedSessionConfig();
        snapshot.mergedConfig = this.sanitizeLogData(merged);
      }
    } catch (error) {
      snapshot.mergedConfigError = (error as Error).message;
    }

    return snapshot;
  }

  getConnectionLog(): string[] {
    return [...this.connectionLog];
  }

  private ensureSessionInternalsSanitized(context: string): void {
    if (!this.session) {
      this.logConnectionEvent('warn', '⚠️ [SANITIZE] Session unavailable for sanitation', { context });
      return;
    }

    const sdkSession = (this.session as unknown) as Record<string, unknown> & {
      [key: string]: unknown;
    };

    const keysToCheck = ['_config', '_sessionConfig', '_pendingSessionUpdate', 'config'];
    let removals = 0;

    for (const key of keysToCheck) {
      const value = sdkSession[key];
      if (value && typeof value === 'object' && this.containsRealtimeType(value)) {
        sdkSession[key] = this.sanitizeSessionObject(value, true);
        removals += 1;
        this.logConnectionEvent('info', '🔧 [SANITIZE] Removed session.type from internal structure', {
          context,
          key,
        });
      }
    }

    if (removals === 0) {
      this.logConnectionEvent('info', 'ℹ️ [SANITIZE] No session.type found during final check', { context });
    }

    this.logConnectionEvent('info', '🔍 [SANITIZE] Session state snapshot', {
      context,
      snapshot: this.dumpSessionState(),
    });
  }

  /**
   * Alternative initialization strategy: Create the RealtimeAgent with ALL configuration
   * upfront to avoid needing manual session.update() calls that trigger the SDK bug.
   *
   * This method initializes the agent with tools AND instructions in the constructor,
   * allowing the SDK to handle configuration internally without manual session updates.
   * This avoids the 'session.type' injection issue that occurs with session.update().
   *
   * Note: This is an experimental approach - the primary DataChannel patching method
   * is still the recommended solution.
   */
  private initializeAgentWithFullConfig(): void {
    if (this.config.enableDebugLogs) {
      this.log.info('🔧 Using alternative initialization - creating agent with full config upfront');
    }

    // Create agent with tools AND instructions in constructor
    // This way the SDK handles everything internally
    this.agent = new RealtimeAgent({
      name: 'SeafoodInventoryAssistant',
      tools: this.getAssistantTools(),
      instructions: this.getAssistantInstructions(),
      // Note: We can't pass model/voice here directly to RealtimeAgent constructor
      // Those must be configured via transport and session
    });

    if (this.config.enableDebugLogs) {
      this.log.info('✅ Agent initialized with full config - manual session.update should not be needed');
    }
  }

  private rebuildSessionForAlternativeInitialization(): void {
    const sessionOptions: Partial<RealtimeSessionOptions> = {
      transport: this.transport,
    };

    if (this.isUsingWebRTCTransport()) {
      this.pendingSessionConfig = this.buildSessionConfigurationPayload();
    }

    this.session = new RealtimeSession(this.agent, sessionOptions);
    this.logConnectionEvent('info', '🔄 Rebuilt session after enabling alternative initialization', {
      transport: this.isUsingWebRTCTransport() ? 'webrtc' : 'websocket',
    });
    this.patchRealtimeSessionConstructor();
    this.patchSessionSendEvent();
    this.setupEventListeners();
  }

  async connect(options?: { skipFallback?: boolean }): Promise<boolean> {
    const shouldAttemptFallback = !options?.skipFallback;
    try {
      // Validate credentials before attempting connection
      if (!this.validateCredentials()) {
        return false;
      }

      if (this.isConnected) {
        if (this.config.enableDebugLogs) {
          this.log.info('⚠️ Already connected, disconnecting first...');
        }
        await this.disconnect();
      }

      // Initialize voice authentication and set up database client
      await this.initializeVoiceAuthentication();

      // For WebRTC, verify microphone permissions first
      // For WebSocket, we need to prepare audio context
      if (typeof window !== 'undefined') {
        if (this.isUsingWebSocketTransport()) {
          await this.ensureAudioContext();
        } else {
          // WebRTC mode: Check microphone permissions before connecting
          // The SDK will need mic access for audio input
          await this.checkMicrophonePermission();
        }
      }

      if (this.config.enableDebugLogs) {
        this.log.info('🔌 Connecting to OpenAI Realtime API...');
      }

      if (!this.session) {
        throw new Error('Realtime session not initialized');
      }

      this.patchRealtimeFetchHeader();

      // CRITICAL: Try to patch transport BEFORE connection
      this.prepareTransportPatches();
      if (this.config.enableDebugLogs) {
        this.log.info('🧪 Pre-connection patch status:', this.verifyPatchStatus());
      }

      if (this.config.enableDebugLogs) {
        this.log.info('🔑 Using API credentials:', {
          hasEphemeralToken: !!this.config.ephemeralToken,
          ephemeralTokenPrefix: this.config.ephemeralToken?.slice(0, 8),
          hasApiKey: !!this.config.apiKey,
          isRelay: Boolean(this.config.relayUrl),
        });
      }

      // Get API key for session connection
      const isRelay = Boolean(this.config.relayUrl);
      const apiKey = this.config.ephemeralToken
        ?? this.config.apiKey
        ?? (isRelay ? 'relay' : undefined);

      if (!apiKey) {
        throw new Error('Missing API credentials for realtime session');
      }

      this.ensureSessionInternalsSanitized('pre-connect');
      const containsTypeAfterSanitize = this.session && this.containsRealtimeType(this.session as unknown as Record<string, unknown>);
      if (containsTypeAfterSanitize) {
        this.logConnectionEvent('warn', '⚠️ Pre-connection final check detected session.type - applying extra sanitation');
        this.ensureSessionInternalsSanitized('pre-connect-final-pass');
      } else {
        this.logConnectionEvent('info', '🔍 Pre-connection final check: session internal state sanitized');
      }
      const sessionStateBeforeConnect = this.dumpSessionState();
      this.logConnectionEvent('info', '🔌 About to call session.connect() - session state captured', sessionStateBeforeConnect);

      if (this.config.relayUrl) {
        await this.session.connect({
          apiKey,
          url: this.config.relayUrl,
        });
        // For WebSocket relay, we need to handle microphone manually
        await this.startMicrophoneCapture();
      } else {
        // For WebRTC, pass minimal config to avoid parameter issues during connection
        // Session configuration will be applied after connection is established
        const connectOptions = { apiKey };
        if (this.config.enableDebugLogs) {
          this.log.info('🔌 Connecting with options:', connectOptions);
        }
        await (this.session as SDKRealtimeSession).connect?.(connectOptions as unknown as RealtimeSessionOptions);
        if (!('connect' in this.session)) {
          await this.session.connect(connectOptions);
        }
        // IMPORTANT: For WebRTC mode, the SDK handles audio input/output automatically
        // via RTCPeerConnection. Do NOT manually capture microphone - it would interfere!
        // Manual capture is only needed for WebSocket relay mode.
      }

      this.logConnectionEvent('info', '✅ session.connect() completed - checking for DataChannel');
      this.logConnectionEvent('info', '🔍 Post-connect session snapshot', this.dumpSessionState());

      this.isConnected = true;
      this.events.onConnected?.();

      // CRITICAL: Apply DataChannel patch IMMEDIATELY after connection
      // The SDK may send automatic session updates, so we need to patch ASAP
      if (this.isUsingWebRTCTransport()) {
        // Poll for DataChannel availability (it might not be ready immediately)
        await this.patchDataChannelSendAsync();
        if (this.config.enableDebugLogs) {
          this.log.info('✅ DataChannel patch applied immediately after connection');
        }
      }

      // Now send session configuration - patch has already been applied
      if (this.config.enableDebugLogs) {
        this.log.info('✅ Connected - now sending session configuration with instructions');
      }
      await this.configureSessionAfterConnection();

      if (this.config.enableDebugLogs) {
        this.log.info('✅ Connected to OpenAI Realtime API with modern SDK');
      }

      return true;
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);

      // Enhanced WebRTC error detection and handling
      const isWebRTCError = message.includes('setRemoteDescription') ||
                           message.includes('Failed to parse SessionDescription') ||
                           message.includes('Expect line: v=') ||
                           message.includes('RTCPeerConnection');

      if (isWebRTCError) {
        this.log.error('❌ WebRTC connection failed:', error);
        this.logConnectionEvent('error', '❌ WebRTC SDP negotiation failed', {
          message,
          isWebRTCError: true,
        });
        const enhancedMessage = this.enhanceErrorMessage(message);
        this.events.onError?.(enhancedMessage);
        return false;
      }

      if (shouldAttemptFallback && this.shouldFallbackToWebSocket(message, error)) {
        const fallbackPrepared = this.prepareWebSocketFallbackTransport();
        if (fallbackPrepared) {
          if (this.config.enableDebugLogs) {
            this.log.warn('⚠️ Negotiation failed; switching to WebSocket transport and retrying once');
          }
          return this.connect({ skipFallback: true });
        }
      }
      const isSessionTypeError = message.includes('Unknown parameter');
      if (isSessionTypeError && !this.config.useAlternativeInitialization && !this.constructorPatchFailed) {
        this.constructorPatchFailed = true;
        this.logConnectionEvent('warn', '⚠️ Constructor patching failed, retrying with alternative initialization');
        this.config.useAlternativeInitialization = true;
        this.initializeAgentWithFullConfig();
        this.rebuildSessionForAlternativeInitialization();
        return this.connect({ skipFallback: options?.skipFallback ?? false });
      }
      const prefix = isSessionTypeError
        ? 'OpenAI Realtime API connection failed (session.type parameter issue)'
        : 'Connection failed';
      this.log.error('❌ Connection failed:', error);
      this.logConnectionEvent('error', '❌ Connection attempt failed', {
        message,
        isSessionTypeError,
      });
      const guidance = isSessionTypeError
        ? ' This is a known SDK bug. Check console for DataChannel patch logs.'
        : '';
      this.events.onError?.(`${prefix}: ${message}${guidance}`);
      return false;
    }
  }

  private async configureSessionAfterConnection(): Promise<void> {
    if (!this.session) return;

    // Skip manual configuration if using alternative initialization
    if (this.config.useAlternativeInitialization && this.agent.instructions) {
      this.log.info('ℹ️ Using alternative initialization - skipping manual session.update');
      this.log.info('   Agent was initialized with tools and instructions upfront');
      this.toolRegistrationStatus = 'success';
      return;
    }

    try {
      // CRITICAL FIX: Wait for transport connection to be fully established
      // before sending session configuration to avoid "Still in CONNECTING state" errors
      if (this.isUsingWebRTCTransport()) {
        await this.waitForWebRTCConnection();
      } else if (this.isUsingWebSocketTransport()) {
        await this.waitForWebSocketConnection();
      }

      this.logConnectionEvent('info', '📋 Building session configuration payload');
      const payload = this.buildSessionConfigurationPayload();
      const sdkSession = this.session as SDKRealtimeSession;

      this.logConnectionEvent('info', '📋 Session configuration payload ready', {
        keys: Object.keys(payload),
        hasInstructions: Boolean(payload.instructions),
        tools: Array.isArray(payload.tools) ? payload.tools.length : 0,
      });

      if (this.config.enableDebugLogs) {
        this.log.info('🔧 Applying session configuration:', {
          transport: this.isUsingWebRTCTransport() ? 'webrtc' : 'websocket',
          toolsCount: payload.tools?.length || 0,
          model: payload.model,
          voice: payload.voice
        });
      }

      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          const patchStatus = this.verifyPatchStatus();
          if (!patchStatus.dataChannelPatched) {
            this.log.warn('⚠️ DataChannel patch marker missing before session configuration', patchStatus);
          } else if (this.config.enableDebugLogs) {
            this.log.info('🛡️ Patch status confirmed before session configuration', patchStatus);
          }

          // CRITICAL FIX: Apply DataChannel patch RIGHT BEFORE sending session config
          // The DataChannel only becomes available AFTER session.connect() completes
          const transport = this.transport as { _dataChannel?: { send: (msg: string) => void; _patchedForSessionType?: boolean } };
          if (this.isUsingWebRTCTransport() && transport._dataChannel) {
            // Check if patch has already been applied by looking for our marker
            if (!transport._dataChannel._patchedForSessionType) {
              this.log.info('🔧 Applying DataChannel patch RIGHT BEFORE sending session config');
              this.patchDataChannelSend();
              // Mark as patched to avoid double-patching
              transport._dataChannel._patchedForSessionType = true;
            }
          }

          // CRITICAL FIX: Send configuration directly via transport's data channel
          // Bypass SDK's .update() and .sendEvent() which both inject 'type: realtime'
          const eventPayload: Record<string, unknown> = { type: 'session.update', session: payload };

          this.logConnectionEvent('info', '📤 Sending session.update via sanitized transport layer');

          if (this.config.enableDebugLogs) {
            this.log.info('📤 Bypassing SDK methods - sending directly via transport');
            this.log.info('📋 Final payload:', JSON.stringify(eventPayload, null, 2));
          }

          // Access the underlying transport's send method directly
          if (this.isUsingWebRTCTransport() && transport._dataChannel) {
            // For WebRTC, send directly via data channel
            let message = JSON.stringify(eventPayload);
            if (message.includes('"session":{"type"')) {
              this.log.warn('⚠️ Final message string contains session.type - applying fallback cleanup before send');
              message = message.replace(/"session":{"type":"realtime",/g, '"session":{');
              message = message.replace(/,"type":"realtime"/g, '');
            }
            transport._dataChannel.send(message);
            if (this.config.enableDebugLogs) {
              this.log.info('✅ Sent directly via WebRTC data channel');
            }
          } else if (sdkSession.sendEvent) {
            // Fallback to sendEvent if direct access not available
            // But intercept and fix any type injection
            const originalPayload = JSON.parse(JSON.stringify(eventPayload));
            const sanitizedPayload = this.sanitizeSessionUpdateEvent(originalPayload);
            await Promise.resolve(sdkSession.sendEvent(sanitizedPayload));
          } else {
            this.log.warn('⚠️ No method available to send session configuration');
            this.toolRegistrationStatus = 'failed';
            return;
          }

          this.toolRegistrationStatus = 'success';
          if (this.config.enableDebugLogs) {
            this.log.info('✅ Session configuration applied successfully');
          }
          this.logConnectionEvent('info', '✅ Session.update sent successfully');
          return;
        } catch (error) {
          retryCount++;
          if (retryCount >= maxRetries) {
            throw error;
          }

          if (this.config.enableDebugLogs) {
            this.log.warn(`⚠️ Session update attempt ${retryCount} failed, retrying...`, error);
          }

          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
        }
      }
    } catch (error) {
      this.toolRegistrationStatus = 'failed';
      this.log.error('❌ Failed to configure session after retries:', error);
      this.logConnectionEvent('error', '⚠️ Session configuration failed', {
        error: error instanceof Error ? error.message : String(error),
      });

      // Provide specific error guidance
      if (error instanceof Error) {
        if (error.message.includes('connection')) {
          this.events.onError?.('Session configuration failed: Connection not stable. Please reconnect.');
        } else if (error.message.includes('tools')) {
          this.events.onError?.('Tool registration failed: Inventory tools may not be available.');
        } else {
          this.events.onError?.(`Session configuration failed: ${error.message}`);
        }
      }
    }
  }

  /**
   * Validate that at least one credential type is provided
   * Returns false and triggers onError callback if no credentials found
   */
  private validateCredentials(): boolean {
    const hasApiKey = Boolean(this.config.apiKey);
    const hasEphemeralToken = Boolean(this.config.ephemeralToken);
    const hasRelayUrl = Boolean(this.config.relayUrl);

    if (!hasApiKey && !hasEphemeralToken && !hasRelayUrl) {
      const error = 'No API credentials provided. Please provide apiKey, ephemeralToken, or relayUrl.';
      this.log.error('❌ Credential validation failed:', {
        hasApiKey,
        hasEphemeralToken,
        hasRelayUrl,
        providedConfig: {
          apiKey: this.config.apiKey ? '[REDACTED]' : undefined,
          ephemeralToken: this.config.ephemeralToken ? '[REDACTED]' : undefined,
          relayUrl: this.config.relayUrl || undefined
        }
      });
      this.events.onError?.(error);
      return false;
    }

    if (this.config.enableDebugLogs) {
      this.log.info('✅ Credential validation passed:', {
        hasApiKey,
        hasEphemeralToken,
        hasRelayUrl
      });
    }
    return true;
  }

  private buildSessionConfigurationPayload(): Record<string, unknown> {
    this.log.info('🚀 buildSessionConfigurationPayload called');
    // Validate tools before building payload
    const tools = this.agent?.tools ?? this.getAssistantTools();
    this.log.info('🔍 Agent tools:', tools.length, tools.map((t: any) => ({
      name: t.name,
      hasInvoke: !!t.invoke      // SDK tools use .invoke method
    })));

    if (this.config.enableDebugLogs) {
      this.log.info('🔧 Building session configuration with tools:', {
        toolsCount: tools.length,
        toolNames: tools.map((tool: any) => tool.name || tool.function?.name || 'unnamed'),
        hasInstructions: !!this.agent.instructions
      });
    }

    // Ensure tools are properly formatted for OpenAI Realtime API
    const formattedTools = tools.map(tool => {
      if (tool.function) {
        // Tool is already in OpenAI format
        return tool;
      } else if (tool.name && tool.invoke) {
        // CRITICAL: SDK tools use .invoke method (from @openai/agents/realtime)
        // Convert from SDK tool format to OpenAI Realtime API format
        const formatted = {
          type: 'function',
          function: {
            name: tool.name,
            description: tool.description || `Execute ${tool.name}`,
            parameters: tool.parameters || { type: 'object', properties: {} }
          }
        };
        this.log.info('🔧 Formatted tool:', tool.name, 'has invoke:', Boolean(tool.invoke));
        return formatted;
      }
      this.log.warn('⚠️ Tool missing name or invoke method:', tool);
      return tool;
    });

    const payload: Record<string, unknown> = {
      // CRITICAL FIX: Model parameter removed from session config
      // The OpenAI Realtime API doesn't accept 'model' in session.update payloads
      // Model is correctly specified in transport configuration only
      // CRITICAL: Instructions must be sent here since we didn't pass them to RealtimeAgent
      // to avoid SDK auto-sending problematic 'type: realtime' parameter
      instructions: this.getAssistantInstructions(),
      tools: formattedTools,
      voice: this.config.voice ?? 'alloy',
      modalities: ['audio', 'text'],
      // NOTE: tool_choice removed - OpenAI Realtime API selects tools automatically
      // when they are provided in the tools array. Including tool_choice causes
      // "Unknown parameter" errors from the API.
      input_audio_format: 'pcm16',
      output_audio_format: 'pcm16',
      input_audio_transcription: {
        model: 'whisper-1'
      },
      // CRITICAL FIX: Enable server-side voice activity detection
      // This tells OpenAI when the user has stopped speaking and it should respond
      turn_detection: {
        type: 'server_vad',
        threshold: 0.5,
        prefix_padding_ms: 300,
        silence_duration_ms: 700
      }
    };

    // Ensure no 'type' field exists at root level of payload
    // The 'type' should only be in the event wrapper, not in the session config
    if ('type' in payload) {
      delete payload.type;
    }

    // CRITICAL: Apply recursive sanitization to remove any nested type fields
    const sanitizedPayload = this.sanitizeSessionObject(payload, true);

    // Final validation: check for any 'type' fields at any nesting level
    const payloadString = JSON.stringify(sanitizedPayload);
    const hasSessionType = payloadString.includes('"type":"realtime"') ||
                           (sanitizedPayload as any).type === 'realtime';

    if (hasSessionType) {
      this.log.error('❌ CRITICAL: session.type detected in final payload despite sanitization!');
      this.log.error('   Payload snippet:', payloadString.substring(0, 500));
    } else {
      this.log.info('✅ Payload validation passed - no session.type field detected');
    }

    this.log.info('📋 Session payload with tools:', {
      toolCount: formattedTools.length,
      tools: formattedTools.map((t: any) => t.function?.name || t.name),
      hasTypeField: 'type' in sanitizedPayload,
      allKeys: Object.keys(sanitizedPayload),
      payloadSize: payloadString.length
    });

    return sanitizedPayload;
  }

  private isUsingWebRTCTransport(): boolean {
    return this.transport instanceof OpenAIRealtimeWebRTC;
  }

  /**
   * CRITICAL FIX: Patches the WebRTC DataChannel's send() method to intercept
   * and strip the problematic 'session.type' field that the SDK injects.
   * 
   * The @openai/agents SDK automatically adds 'type: realtime' to session configs
   * via _getMergedSessionConfig(), but OpenAI's Realtime API rejects this with
   * "Unknown parameter: 'session.type'" error.
   * 
   * This method intercepts at the lowest level (DataChannel.send) to remove the
   * field before it goes over the wire, regardless of which SDK method is used.
   * 
   * Must be called AFTER connection (when _dataChannel exists) but BEFORE
   * sending session.update events.
   */
  /**
   * Recursively remove 'type' fields from session objects at any nesting level
   * This is necessary because the SDK may inject type fields at various depths
   */
  private sanitizeSessionObject(obj: any, isSessionRoot = false, parentKey?: string): any {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeSessionObject(item, false, parentKey));
    }

    const cleaned: any = {};
    for (const [key, value] of Object.entries(obj)) {
      if (key === 'type') {
        const stringValue = typeof value === 'string' ? value : undefined;
        const parentIsSession = parentKey === 'session' || parentKey?.endsWith('Session');
        const shouldStrip = isSessionRoot || parentIsSession || stringValue === 'realtime';

        if (shouldStrip) {
          this.log.info('🔧 Stripping session-level type field:', value);
          continue;
        }
      }

      cleaned[key] = this.sanitizeSessionObject(value, false, key);
    }
    return cleaned;
  }

  private sanitizeSessionUpdateEvent(event: Record<string, unknown>): Record<string, unknown> {
    const cloned = JSON.parse(JSON.stringify(event));

    if (cloned?.session && (cloned.session as any).type) {
      this.log.warn('⚠️ sanitizeSessionUpdateEvent detected session.type before cleanup:', (cloned.session as any).type);
    }

    if (cloned?.session) {
      if ((cloned.session as any).type) {
        this.log.info('🔧 Stripping session.type from sendEvent payload');
        delete (cloned.session as any).type;
      }
      cloned.session = this.sanitizeSessionObject(cloned.session, true);
    }

    // Final guard: ensure serialized payload has no session.type remnants
    let payloadString: string;
    try {
      payloadString = JSON.stringify(cloned);
      if (payloadString.includes('"type":"realtime"')) {
        this.log.warn('⚠️ session.type detected after sanitization; applying string-level cleanup for sendEvent payload');
        const cleanedString = payloadString
          .replace(/"type":"realtime",?/g, '')
          .replace(/,\s*\}/g, '}')
          .replace(/\{\s*,/g, '{');
        this.log.info('🔧 Final JSON cleanup applied for sendEvent payload');
        return JSON.parse(cleanedString);
      }
    } catch (error) {
      this.log.warn('⚠️ Failed to serialize sanitized session event, returning original clone', error);
      return cloned;
    }

    return cloned;
  }

  private patchRealtimeSessionConstructor(): void {
    if (!this.session) {
      this.logConnectionEvent('warn', '⚠️ [PATCH: Constructor] Session unavailable for constructor patch');
      return;
    }

    const sdkSession = this.session as Record<string, unknown> & {
      [key: string]: unknown;
    };

    if ((sdkSession as any)._constructorPatched) {
      this.logConnectionEvent('info', 'ℹ️ [PATCH: Constructor] Patch already applied');
      return;
    }

    this.patchInvocationCounters.constructor += 1;
    const invocation = this.patchInvocationCounters.constructor;
    this.logConnectionEvent('info', '🔧 [PATCH: Constructor] Starting patch application', { invocation });

    const timestamp = new Date().toISOString();
    let sanitizedKeys: string[] = [];

    try {
      const keysToSanitize = ['_config', '_sessionConfig', 'config', '_initialSessionConfig'];
      for (const key of keysToSanitize) {
        if (Object.prototype.hasOwnProperty.call(sdkSession, key)) {
          const target = sdkSession[key];
          if (target && typeof target === 'object') {
            const hadTypeBefore = this.containsRealtimeType(target);
            const sanitized = this.sanitizeSessionObject(target, true);
            (sdkSession as any)[key] = sanitized;
            const hasTypeAfter = this.containsRealtimeType(sanitized);
            sanitizedKeys.push(`${key}:${hadTypeBefore && !hasTypeAfter ? 'cleaned' : 'unchanged'}`);
            this.logConnectionEvent('info', '🔍 [PATCH: Constructor] Sanitized session key', {
              key,
              hadTypeBefore,
              hasTypeAfter,
            });
          }
        }
      }

      const patchFunction = (
        fn: unknown,
        descriptor: string,
        sanitizer: (value: Record<string, unknown>) => Record<string, unknown>
      ) => {
        if (typeof fn !== 'function') {
          return fn;
        }

        if ((fn as any)._patchedForSessionType) {
          this.logConnectionEvent('info', 'ℹ️ [PATCH: Constructor] Function already patched', {
            descriptor,
          });
          return fn;
        }

        const wrapped = (...args: unknown[]) => {
          if (args.length > 0 && typeof args[0] === 'object' && args[0]) {
            args[0] = sanitizer(args[0] as Record<string, unknown>);
          }
          return (fn as any)(...args);
        };

        (wrapped as any)._patchedForSessionType = true;
        this.logConnectionEvent('info', '✅ [PATCH: Constructor] Function patched', { descriptor });
        return wrapped;
      };

      if (typeof (sdkSession as any)._applyConfig === 'function') {
        (sdkSession as any)._applyConfig = patchFunction(
          (sdkSession as any)._applyConfig,
          '_applyConfig',
          (value) => this.sanitizeSessionObject(value, true)
        );
      }

      if (typeof (sdkSession as any)._getConfig === 'function') {
        const originalGetConfig = (sdkSession as any)._getConfig.bind(sdkSession);
        if (!(originalGetConfig as any)._patchedForSessionType) {
          (sdkSession as any)._getConfig = () => {
            const config = originalGetConfig();
            return this.sanitizeSessionObject(config, true);
          };
          (sdkSession as any)._getConfig._patchedForSessionType = true;
          this.logConnectionEvent('info', '✅ [PATCH: Constructor] _getConfig patched');
        }
      }

      if (typeof (sdkSession as any)._getMergedSessionConfig === 'function' && !(sdkSession as any)._patchedMergedSessionConfig) {
        const originalGetMerged = (sdkSession as any)._getMergedSessionConfig.bind(sdkSession);
        (sdkSession as any)._getMergedSessionConfig = (...args: unknown[]) => {
          const mergedConfig = originalGetMerged(...args);
          return this.sanitizeSessionObject(mergedConfig, true);
        };
        (sdkSession as any)._patchedMergedSessionConfig = true;
        this.logConnectionEvent('info', '✅ [PATCH: Constructor] _getMergedSessionConfig patched');
      }

      (sdkSession as any)._constructorPatched = true;
      this.logConnectionEvent('info', '✅ Session constructor patched', {
        timestamp,
        sanitizedKeys,
      });
      this.log.info('🧪 Constructor patch verification:', this.verifyPatchStatus());
    } catch (error) {
      this.constructorPatchFailed = true;
      this.logConnectionEvent('error', '⚠️ Session constructor patch failed', {
        timestamp,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  private patchSessionSendEvent(): void {
    if (!this.session) {
      this.logConnectionEvent('warn', '⚠️ [PATCH: SessionSendEvent] Session unavailable');
      return;
    }

    const sdkSession = this.session as SDKRealtimeSession & {
      _patchedForSessionType?: boolean;
      _patchedSessionSendV2?: boolean;
      _transport?: { send?: (payload: unknown) => unknown };
    };

    if (sdkSession._patchedSessionSendV2) {
      this.logConnectionEvent('info', 'ℹ️ [PATCH: SessionSendEvent] Patch already applied');
      return;
    }

    this.patchInvocationCounters.sessionSend += 1;
    const invocation = this.patchInvocationCounters.sessionSend;
    this.logConnectionEvent('info', '🔧 [PATCH: SessionSendEvent] Starting patch application', { invocation });

    const patchTransportSend = (transport: any, label: string) => {
      if (!transport || typeof transport.send !== 'function') {
        this.logConnectionEvent('info', 'ℹ️ [PATCH: SessionSendEvent] No transport send method to patch', { label });
        return;
      }

      if ((transport.send as any)._patchedForSessionType) {
        this.logConnectionEvent('info', 'ℹ️ [PATCH: SessionSendEvent] Transport send already patched', { label });
        return;
      }

      const originalTransportSend = transport.send.bind(transport);
      transport.send = (payload: unknown) => {
        if (typeof payload === 'string') {
          try {
            const parsed = JSON.parse(payload);
            const sanitized = this.sanitizeSessionUpdateEvent(parsed);
            return originalTransportSend(JSON.stringify(sanitized));
          } catch {
            // Fall back to original payload
            return originalTransportSend(payload);
          }
        }
        if (payload && typeof payload === 'object' && 'session' in (payload as Record<string, unknown>)) {
          const sanitized = this.sanitizeSessionUpdateEvent(payload as Record<string, unknown>);
          return originalTransportSend(sanitized);
        }
        return originalTransportSend(payload);
      };
      (transport.send as any)._patchedForSessionType = true;
      this.logConnectionEvent('info', '✅ [PATCH: SessionSendEvent] Transport send patched', { label });
    };

    try {
      if (typeof (sdkSession as any)._getMergedSessionConfig === 'function' && !(sdkSession as any)._patchedMergedSessionConfig) {
        const originalGetMerged = (sdkSession as any)._getMergedSessionConfig.bind(sdkSession);
        (sdkSession as any)._getMergedSessionConfig = (...args: unknown[]) => {
          try {
            const mergedConfig = originalGetMerged(...args);
            if (!mergedConfig || typeof mergedConfig !== 'object') {
              return mergedConfig;
            }

            const sanitized = this.sanitizeSessionObject(mergedConfig, true);
            if ((sanitized as Record<string, unknown>).type) {
              delete (sanitized as Record<string, unknown>).type;
            }
            return sanitized;
          } catch (error) {
            this.logConnectionEvent('warn', '⚠️ [PATCH: SessionSendEvent] Failed to sanitize merged config', {
              error: error instanceof Error ? error.message : String(error),
            });
            return originalGetMerged(...args);
          }
        };
        (sdkSession as any)._patchedMergedSessionConfig = true;
        this.logConnectionEvent('info', '✅ [PATCH: SessionSendEvent] _getMergedSessionConfig patched');
      }

      if (typeof sdkSession.sendEvent === 'function' && !(sdkSession.sendEvent as any)._patchedForSessionType) {
        const originalSendEvent = sdkSession.sendEvent.bind(sdkSession);
        sdkSession.sendEvent = async (event: Record<string, unknown>) => {
          if (event?.type === 'session.update' || Object.prototype.hasOwnProperty.call(event, 'session')) {
            const sanitized = this.sanitizeSessionUpdateEvent(event);
            return originalSendEvent(sanitized);
          }
          return originalSendEvent(event);
        };
        (sdkSession.sendEvent as any)._patchedForSessionType = true;
        this.logConnectionEvent('info', '✅ [PATCH: SessionSendEvent] sendEvent patched');
      }

      if (typeof (sdkSession as any).update === 'function' && !((sdkSession as any).update as any)._patchedForSessionType) {
        const originalUpdate = (sdkSession as any).update.bind(sdkSession);
        (sdkSession as any).update = async (sessionUpdate: Record<string, unknown>) => {
          const sanitizedSession = this.sanitizeSessionObject(sessionUpdate, true);
          return originalUpdate(sanitizedSession);
        };
        ((sdkSession as any).update as any)._patchedForSessionType = true;
        this.logConnectionEvent('info', '✅ [PATCH: SessionSendEvent] update patched');
      }

      patchTransportSend((sdkSession as any)._transport, 'session._transport');
      patchTransportSend(this.transport as any, 'client.transport');

      sdkSession._patchedForSessionType = true;
      sdkSession._patchedSessionSendV2 = true;
      this.logConnectionEvent('info', '🔧 Constructor patches applied: Session sendEvent/update/transport sanitized');
      this.logConnectionEvent('info', '🧪 Patch verification snapshot', this.verifyPatchStatus());
    } catch (error) {
      this.logConnectionEvent('error', '⚠️ [PATCH: SessionSendEvent] Patch failed', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  private prepareTransportPatches(): void {
    const transport = this.transport as any;

    this.patchInvocationCounters.transport += 1;
    const invocation = this.patchInvocationCounters.transport;
    this.logConnectionEvent('info', '🔧 [PATCH: Transport] Preparing transport patches', { invocation });

    if (!transport) {
      this.logConnectionEvent('warn', '⚠️ [PATCH: Transport] Transport unavailable');
      return;
    }

    const sanitizeIfPresent = (target: any, label: string) => {
      if (!target) {
        return;
      }
      const hadType = this.containsRealtimeType(target);
      if (!hadType) {
        this.logConnectionEvent('info', 'ℹ️ [PATCH: Transport] No session.type found', { label });
        return;
      }
      const sanitized = this.sanitizeSessionObject(target, true);
      this.logConnectionEvent('info', '✅ [PATCH: Transport] Sanitized transport structure', {
        label,
        removedType: hadType,
      });
      return sanitized;
    };

    if (transport._session) {
      const sanitizedSession = sanitizeIfPresent(transport._session, 'transport._session');
      if (sanitizedSession) {
        transport._session = sanitizedSession;
      }
    }

    if (transport._config) {
      const sanitizedConfig = sanitizeIfPresent(transport._config, 'transport._config');
      if (sanitizedConfig) {
        transport._config = sanitizedConfig;
      }
    }

    const dataChannel = transport._dataChannel;
    if (dataChannel && !dataChannel._patchedForSessionType) {
      this.logConnectionEvent('info', '🔧 [PATCH: Transport] DataChannel available, applying patch immediately');
      this.patchDataChannelSend();
      dataChannel._patchedForSessionType = true;
    } else if (dataChannel) {
      this.logConnectionEvent('info', 'ℹ️ [PATCH: Transport] DataChannel already patched');
    } else {
      this.logConnectionEvent('info', '⏳ [PATCH: Transport] DataChannel not yet available');
    }

    this.logConnectionEvent('info', '🧪 [PATCH: Transport] Verification snapshot', this.verifyPatchStatus());
  }

  /**
   * Enhanced DataChannel patching with aggressive type field removal
   * Intercepts ALL messages and strips problematic 'session.type' fields
   */
  private patchDataChannelSend(): void {
    const transport = this.transport as any;

    // Verify data channel is available
    if (!transport._dataChannel) {
      this.logConnectionEvent('warn', '⚠️ [PATCH: DataChannel] DataChannel not available for patching');
      return;
    }

    this.patchInvocationCounters.dataChannel += 1;
    const invocation = this.patchInvocationCounters.dataChannel;
    this.logConnectionEvent('info', '🔧 [PATCH: DataChannel] Applying DataChannel interceptor', { invocation });

    // Track patch invocations and modifications for diagnostics
    let patchInvocationCount = 0;
    let modifiedMessageCount = 0;

    // Save original send method
    const originalSend = transport._dataChannel.send.bind(transport._dataChannel);

    // Replace with enhanced interceptor
    transport._dataChannel.send = (message: unknown) => {
      if (typeof message !== 'string') {
        return originalSend(message);
      }

      patchInvocationCount++;
      const timestamp = new Date().toISOString();

      // ALWAYS log to verify patch is working
      this.log.info(`🔍 [${timestamp}] DATACHANNEL SEND INTERCEPTED (invocation #${patchInvocationCount}):`,
        message.length > 500 ? `${message.substring(0, 500)}...` : message);

      try {
        const parsed = JSON.parse(message);
        let wasModified = false;

        // Log the full message structure before cleaning
        if (this.config.enableDebugLogs) {
          this.log.info('📋 Message before cleaning:', JSON.stringify(parsed, null, 2));
        }

        // Remove session.type if present at top level
        if (parsed.session?.type) {
          this.log.info(`🔧 [${timestamp}] *** REMOVING session.type at TOP LEVEL ***:`, parsed.session.type);
          delete parsed.session.type;
          wasModified = true;
        }

        // Recursively sanitize the entire session object
        if (parsed.session) {
          const originalSession = JSON.stringify(parsed.session);
          parsed.session = this.sanitizeSessionObject(parsed.session, true);
          if (JSON.stringify(parsed.session) !== originalSession) {
            this.log.info('🔧 *** RECURSIVE SANITIZATION APPLIED ***');
            wasModified = true;
          }
        }

        // Final string-level check as fallback
        let cleaned = JSON.stringify(parsed);
        if (cleaned.includes('"session":{"type":')) {
          this.log.warn('⚠️ session.type detected in final string! Applying string-level fix...');
          cleaned = cleaned.replace(/"type":"realtime",?/g, '');
          cleaned = cleaned.replace(/,"type":"realtime"/g, '');
          cleaned = cleaned.replace(/{"type":"realtime",/g, '{');
          wasModified = true;
        }

        if (wasModified) {
          modifiedMessageCount++;
          this.log.info('✅ Message cleaned and sanitized', {
            timestamp,
            patchInvocationCount,
            modifiedMessageCount,
          });
          if (this.config.enableDebugLogs) {
            this.log.info('📋 Message after cleaning:', cleaned);
          }
        } else {
          this.log.info('ℹ️ No session.type found in message:', parsed.type || 'unknown');
        }

        return originalSend(cleaned);
      } catch (error) {
        this.log.info(`⚠️ [${timestamp}] Could not parse message for cleaning, sending as-is:`, error);
        return originalSend(message);
      }
    };

    // ALWAYS log to verify patch was applied
    this.logConnectionEvent('info', '✅ [PATCH: DataChannel] Enhanced interceptor applied');
    this.log.info('   - Patch includes: top-level removal, recursive sanitization, and string-level fallback');
    this.log.info('   - Interceptor counters active for diagnostics (invocations vs modifications)');
    transport._dataChannel._patchedForSessionType = true;
    this.logConnectionEvent('info', '🧪 [PATCH: DataChannel] Verification snapshot', this.verifyPatchStatus());
  }

  /**
   * Async version that polls for DataChannel availability before patching
   * Used when transport is recreated after initial connection
   */
  private async patchDataChannelSendAsync(): Promise<void> {
    const transport = this.transport as any;
    const maxAttempts = 50; // Increased: 50 attempts × 200ms = 10 seconds max wait
    const delayMs = 200; // Increased delay for slower connections

    if (this.config.enableDebugLogs) {
      this.log.info('🔍 Waiting for DataChannel to be available for patching...');
    }

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      if (transport._dataChannel) {
        if (this.config.enableDebugLogs) {
          this.log.info(`✅ DataChannel available after ${attempt} attempts (${attempt * delayMs}ms)`);
        }

        // Check if patch has already been applied
        if (transport._dataChannel._patchedForSessionType) {
          if (this.config.enableDebugLogs) {
            this.log.info('ℹ️ DataChannel patch already applied - skipping');
          }
          return;
        }

        // Apply the patch and mark as patched
        this.patchDataChannelSend();
        transport._dataChannel._patchedForSessionType = true;
        return;
      }

      if (this.config.enableDebugLogs && attempt % 10 === 0) {
        this.log.info(`⏳ Still waiting for DataChannel... (attempt ${attempt}/${maxAttempts})`);
      }

      // Wait before next attempt
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }

    // DataChannel never became available - log warning but don't throw
    // Allow connection to proceed, it may work without the patch
    this.log.warn(`⚠️ DataChannel not available after ${maxAttempts * delayMs}ms - proceeding without patch`);
    this.log.warn('   Voice assistant may experience issues if SDK injects session.type');
  }

  private isUsingWebSocketTransport(): boolean {
    return this.transport instanceof OpenAIRealtimeWebSocket;
  }

  private shouldFallbackToWebSocket(message: string, error: unknown): boolean {
    // FORCE WebRTC ONLY - Remove WebSocket fallback completely
    if (this.config.enableDebugLogs) {
      this.log.info('🚫 WebRTC ONLY mode - WebSocket fallback disabled');
    }
    return false; // Never fallback to WebSocket

    const normalized = message.toLowerCase();
    const negotiationIndicators = [
      'unknown parameter',
      'negotiation',
      'sdp',
      'peerconnection',
      'ice',
      'datachannel',
      'webrtc'
    ];

    // Don't fallback for authentication or tool-related errors
    const noFallbackIndicators = [
      'authentication',
      'auth',
      'token',
      'permission',
      'rls',
      'tool',
      'function'
    ];

    if (noFallbackIndicators.some((indicator) => normalized.includes(indicator))) {
      if (this.config.enableDebugLogs) {
        this.log.info('🚫 Error not suitable for transport fallback:', message);
      }
      return false;
    }

    if (negotiationIndicators.some((indicator) => normalized.includes(indicator))) {
      if (this.config.enableDebugLogs) {
        this.log.info('✅ WebRTC negotiation error detected, fallback allowed');
      }
      return true;
    }

    const errorCodes = this.extractErrorCodes(error);
    const shouldFallback = errorCodes.some((code) => {
      const lowerCode = code.toLowerCase();
      return negotiationIndicators.some((indicator) => lowerCode.includes(indicator));
    });

    if (this.config.enableDebugLogs) {
      this.log.info('🔍 Fallback analysis:', {
        message: normalized,
        errorCodes,
        shouldFallback,
        negotiationIndicators: negotiationIndicators.filter(i => normalized.includes(i))
      });
    }

    return shouldFallback;
  }

  private extractErrorCodes(error: unknown): string[] {
    if (!error || typeof error !== 'object') {
      return [];
    }

    const maybeError = error as Record<string, unknown>;
    const codes: string[] = [];

    const directCode = maybeError.code;
    if (typeof directCode === 'string' || typeof directCode === 'number') {
      codes.push(String(directCode));
    }

    const nestedError = maybeError.error as Record<string, unknown> | undefined;
    if (nestedError) {
      const nestedCode = nestedError.code;
      if (typeof nestedCode === 'string' || typeof nestedCode === 'number') {
        codes.push(String(nestedCode));
      }

      const nestedMessage = nestedError.message;
      if (typeof nestedMessage === 'string') {
        codes.push(nestedMessage);
      }
    }

    return codes;
  }

  private async waitForWebRTCConnection(): Promise<void> {
    if (!this.isUsingWebRTCTransport()) {
      return;
    }

    // Wait for WebRTC peer connection to be established
    let attempts = 0;
    const maxAttempts = 30; // 3 seconds with 100ms intervals

    while (attempts < maxAttempts) {
      try {
        // Check if we can access the peer connection state through the transport
        const transport = this.transport as any;

        if (transport.peerConnection) {
          const connectionState = transport.peerConnection.connectionState;
          const iceConnectionState = transport.peerConnection.iceConnectionState;

          this.connectionState = connectionState === 'connected' ? 'connected' : 'connecting';
          this.iceConnectionState = iceConnectionState;

          if (this.config.enableDebugLogs) {
            this.log.info('🔗 WebRTC connection state:', {
              connectionState,
              iceConnectionState,
              attempt: attempts + 1
            });
          }

          if (connectionState === 'connected' &&
              (iceConnectionState === 'connected' || iceConnectionState === 'completed')) {
            if (this.config.enableDebugLogs) {
              this.log.info('✅ WebRTC connection fully established');
            }
            return;
          }

          if (connectionState === 'failed' || iceConnectionState === 'failed') {
            throw new Error(`WebRTC connection failed: ${connectionState}/${iceConnectionState}`);
          }
        }

        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      } catch (error) {
        if (this.config.enableDebugLogs) {
          this.log.warn('⚠️ Error checking WebRTC connection state:', error);
        }
        // Continue attempting to avoid false failures
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }
    }

    if (this.config.enableDebugLogs) {
      this.log.warn('⚠️ WebRTC connection state verification timed out, proceeding anyway');
    }
  }

  private async waitForWebSocketConnection(): Promise<void> {
    if (!this.isUsingWebSocketTransport()) {
      return;
    }

    // Wait for WebSocket connection to be fully open
    let attempts = 0;
    const maxAttempts = 30; // 3 seconds with 100ms intervals

    while (attempts < maxAttempts) {
      try {
        // Access the WebSocket through the transport's connectionState
        const transport = this.transport as any;

        if (transport.connectionState?.websocket) {
          const ws = transport.connectionState.websocket;
          const readyState = ws.readyState;

          if (this.config.enableDebugLogs) {
            const stateNames = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
            this.log.info('🔗 WebSocket connection state:', {
              readyState,
              stateName: stateNames[readyState] || 'UNKNOWN',
              attempt: attempts + 1
            });
          }

          // WebSocket.OPEN = 1
          if (readyState === 1) {
            if (this.config.enableDebugLogs) {
              this.log.info('✅ WebSocket connection fully established');
            }
            return;
          }

          // WebSocket.CLOSED = 3 or WebSocket.CLOSING = 2
          if (readyState === 3 || readyState === 2) {
            throw new Error(`WebSocket connection failed: readyState=${readyState}`);
          }
        }

        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      } catch (error) {
        if (this.config.enableDebugLogs) {
          this.log.warn('⚠️ Error checking WebSocket connection state:', error);
        }
        // Continue attempting to avoid false failures
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }
    }

    if (this.config.enableDebugLogs) {
      this.log.warn('⚠️ WebSocket connection state verification timed out, proceeding anyway');
    }
  }

  private prepareWebSocketFallbackTransport(): boolean {
    try {
      this.config.transport = 'websocket';
      const targetModel = this.config.model ?? 'gpt-4o-realtime-preview-2024-12-17';
      const isRelay = Boolean(this.config.relayUrl);
      const apiKey = this.config.ephemeralToken
        ?? this.config.apiKey
        ?? (isRelay ? 'relay' : undefined);

      if (!apiKey) {
        return false;
      }

      this.transport = new OpenAIRealtimeWebSocket({
        model: targetModel,
        apiKey,
        useInsecureApiKey: this.config.useInsecureApiKey ?? true,
      });

      const sessionOptions: Partial<RealtimeSessionOptions> = {
        transport: this.transport,
      };

      this.session = new RealtimeSession(this.agent, sessionOptions);
      this.patchRealtimeSessionConstructor();
      this.patchSessionSendEvent();
      this.isConnected = false;
      this.setupEventListeners();
      this.pendingSessionConfig = null;
      return true;
    } catch (fallbackError) {
      this.log.error('Failed to prepare WebSocket fallback transport:', fallbackError);
      return false;
    }
  }

  private setupEventListeners(): void {
    if (!this.session) return;

    // Official RealtimeSession event handlers following the guide patterns
    this.session.on('audio', (audioEvent: any) => {
      void this.handleAudioEvent(audioEvent);
    });

    // History updates for conversation management
    this.session.on('history_updated', (history: any) => {
      if (this.config.enableDebugLogs) {
        this.log.info('📚 History updated:', history.length, 'items');
      }

      // Extract both user transcript and assistant response from history
      const lastItem = history[history.length - 1];

      // DEBUG: Log last item structure to understand format
      if (this.config.enableDebugLogs && lastItem) {
        this.log.info('🔍 Last history item structure:', {
          type: lastItem.type,
          role: lastItem.role,
          hasToolCalls: !!lastItem.tool_calls,
          hasFunctionCall: !!lastItem.function_call,
          contentType: Array.isArray(lastItem.content) ? 'array' : typeof lastItem.content,
          keys: Object.keys(lastItem).slice(0, 10) // First 10 keys
        });
      }

      // Check for tool calls (function_call or tool_calls in the history)
      if (lastItem?.type === 'function_call' || lastItem?.function_call || lastItem?.tool_calls) {
        // Extract tool call information
        const toolCall = lastItem.function_call || lastItem.tool_calls?.[0];
        if (toolCall) {
          const toolName = toolCall.name || toolCall.function?.name || 'unknown';
          const args = typeof toolCall.arguments === 'string'
            ? JSON.parse(toolCall.arguments)
            : toolCall.arguments || {};

          if (this.config.enableDebugLogs) {
            this.log.info('🔧 Tool call detected in history:', { toolName, args });
          }

          // Fire onToolCall event
          this.events.onToolCall?.(toolName, args, undefined);
        }
      }

      // Check if last item has tool_calls in content array (alternative format)
      if (lastItem?.type === 'message' && Array.isArray(lastItem.content)) {
        const toolCallItems = lastItem.content.filter((item: any) =>
          item.type === 'function_call' || item.type === 'tool_call'
        );

        toolCallItems.forEach((toolCallItem: any) => {
          const toolName = toolCallItem.name || toolCallItem.function?.name || 'unknown';
          const args = typeof toolCallItem.arguments === 'string'
            ? JSON.parse(toolCallItem.arguments)
            : toolCallItem.arguments || {};

          if (this.config.enableDebugLogs) {
            this.log.info('🔧 Tool call detected in message content:', { toolName, args });
          }

          this.events.onToolCall?.(toolName, args, undefined);
        });
      }

      if (lastItem?.type === 'message') {
        // Handle both string and object content
        let content = '';
        if (typeof lastItem.content === 'string') {
          content = lastItem.content;
        } else if (Array.isArray(lastItem.content)) {
          // Extract text from content array (common in OpenAI format)
          content = lastItem.content
            .filter((item: any) => item.type === 'text')
            .map((item: any) => item.text || item.content || '')
            .join(' ');
        } else if (lastItem.content?.text) {
          content = lastItem.content.text;
        } else if (lastItem.content?.transcript) {
          content = lastItem.content.transcript;
        }

        // Determine if this is user input or assistant response
        if (lastItem.role === 'user') {
          this.events.onTranscript?.(content, true);
        } else if (lastItem.role === 'assistant') {
          if (this.config.enableDebugLogs) {
            this.log.info('🤖 Assistant response extracted:', {
              contentLength: content.length,
              hasContent: !!content,
              preview: content.substring(0, 100),
              rawContent: Array.isArray(lastItem.content) ? lastItem.content : 'not array'
            });
          }

          if (content) {
            this.events.onResponse?.(content);
          } else if (this.config.enableDebugLogs) {
            this.log.warn('⚠️ Assistant message had no text content');
          }

          // CRITICAL FIX: Mark response as complete when assistant message is added to history
          // This allows the next message to be sent
          if (this.isResponseInProgress) {
            if (this.config.enableDebugLogs) {
              this.log.info('✅ Response completed - ready for next message');
            }
            this.isResponseInProgress = false;
            this.processQueue();
          }
        }
      }
    });

    // Audio interruption handling
    this.session.on('audio_interrupted', () => {
      if (this.config.enableDebugLogs) {
        this.log.info('🛑 Audio interrupted');
      }
      if (this.audioContext) {
        this.audioQueueTime = this.audioContext.currentTime;
      }
    });

    // Tool approval requests (if using tools with needsApproval: true)
    this.session.on('tool_approval_requested', (_context: any, _agent: any, request: any) => {
      if (this.config.enableDebugLogs) {
        this.log.info('🔧 Tool approval requested:', request);
      }
      // Auto-approve for now - in production you might want to show UI
      this.session?.approve(request.approvalItem);
    });

    // Debug tool execution by monitoring history for tool calls
    // The official SDK handles tool execution automatically, but we can monitor the results

    // Guardrail events
    this.session.on('guardrail_tripped', (event: any) => {
      if (this.config.enableDebugLogs) {
        this.log.info('🚨 Guardrail tripped:', event);
      }
      this.events.onError?.(`Guardrail violation: ${event.details?.name || 'Unknown'}`);
    });

    // Connection state changes
    // Note: 'connected' and 'disconnected' events may not be available in current SDK version
    // TODO: Check SDK documentation for correct event names
    try {
      this.session.on('connected' as any, () => {
        this.isConnected = true;
        if (this.config.enableDebugLogs) {
          this.log.info('🔌 Connected to OpenAI Realtime API');
        }
      });

      this.session.on('disconnected' as any, () => {
        this.isConnected = false;
        this.events.onDisconnected?.();
        if (this.config.enableDebugLogs) {
          this.log.info('🔌 Disconnected from OpenAI Realtime API');
        }
      });
    } catch (error) {
      if (this.config.enableDebugLogs) {
        this.log.warn('Some event handlers not available in current SDK version:', error);
      }
    }

    // Enhanced error handling with specific guidance for authentication and tool issues
    this.session.on('error', (error: any) => {
      this.log.error('❌ Session error:', error);

      // Extract and categorize error information
      let errorMessage = 'Unknown session error';
      if (error && typeof error === 'object') {
        const errorObj = error as any;
        if (errorObj.error?.message) {
          errorMessage = errorObj.error.message;
        } else if (errorObj.error?.code) {
          errorMessage = `Error ${errorObj.error.code}: ${errorObj.error.message || 'Unknown error'}`;
        } else if (errorObj.message) {
          errorMessage = errorObj.message;
        } else {
          errorMessage = JSON.stringify(error);
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      } else {
        errorMessage = String(error);
      }

      // Log raw error for debugging
      if (this.config.enableDebugLogs) {
        this.log.error('🔍 Raw OpenAI error before enhancement:', errorMessage);
      }

      // Categorize and enhance error messages with specific guidance
      const enhancedMessage = this.enhanceErrorMessage(errorMessage);

      this.log.error('❌ Enhanced error message:', enhancedMessage);
      this.events.onError?.(enhancedMessage);
    });
  }

  private async handleAudioEvent(event: unknown): Promise<void> {
    if (typeof window === 'undefined') return;

    try {
      if (this.config.enableDebugLogs) {
        this.log.info('🔊 Audio event received');
      }

      const buffer = this.extractAudioBuffer(event);
      if (!buffer) {
        if (this.config.enableDebugLogs) {
          this.log.warn('🎧 Ignoring audio event without buffer payload');
        }
        return;
      }

      if (this.config.enableDebugLogs) {
        this.log.info('🔊 Playing audio buffer:', buffer.byteLength, 'bytes');
      }

      const audioContext = await this.ensureAudioContext();
      if (!audioContext) return;

      this.scheduleAudioPlayback(audioContext, buffer);
      this.events.onProcessing?.();
    } catch (error) {
      this.log.error('Failed to handle realtime audio event:', error);
      this.events.onError?.('Failed to play audio output');
    }
  }

  private extractAudioBuffer(event: unknown): ArrayBuffer | null {
    if (!event) return null;

    const maybeWithData = event as { data?: ArrayBuffer | ArrayLike<number> };
    if (maybeWithData?.data instanceof ArrayBuffer) {
      return maybeWithData.data;
    }

    if (ArrayBuffer.isView(maybeWithData?.data)) {
      const view = maybeWithData?.data as ArrayBufferView;
      return view.buffer.slice(view.byteOffset, view.byteOffset + view.byteLength);
    }

    if (event instanceof ArrayBuffer) {
      return event;
    }

    return null;
  }

  private async ensureAudioContext(): Promise<AudioContext | null> {
    if (typeof window === 'undefined') return null;
    if (!this.audioContext) {
      try {
        this.audioContext = new AudioContext({ sampleRate: 24000 });
      } catch {
        // Fallback to default constructor in case the sample rate is unsupported
        this.audioContext = new AudioContext();
      }
      this.audioQueueTime = this.audioContext.currentTime;
    }

    if (this.audioContext.state === 'suspended') {
      try {
        await this.audioContext.resume();
      } catch (error) {
        this.log.warn('Unable to resume AudioContext:', error);
        return null;
      }
    }

    return this.audioContext;
  }

  private scheduleAudioPlayback(audioContext: AudioContext, buffer: ArrayBuffer): void {
    if (buffer.byteLength === 0) return;

    const floatData = this.decodePcmToFloat(buffer);
    if (!floatData) return;

    const sampleRate = 24000;
    const audioBuffer = audioContext.createBuffer(1, floatData.length, sampleRate);
    audioBuffer.copyToChannel(floatData, 0);

    const source = audioContext.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(audioContext.destination);

    const startAt = Math.max(this.audioQueueTime, audioContext.currentTime + 0.05);
    source.start(startAt);
    this.audioQueueTime = startAt + audioBuffer.duration;

    source.addEventListener('ended', () => {
      if (this.audioContext && this.audioQueueTime <= this.audioContext.currentTime + 0.05) {
        this.audioQueueTime = this.audioContext.currentTime;
      }
    });
  }

  private decodePcmToFloat(buffer: ArrayBuffer): Float32Array | null {
    if (buffer.byteLength % 2 !== 0) {
      this.log.warn('Unexpected audio buffer length, ignoring chunk');
      return null;
    }

    const view = new DataView(buffer);
    const totalSamples = buffer.byteLength / 2;
    const float32 = new Float32Array(totalSamples);

    for (let i = 0; i < totalSamples; i += 1) {
      const sample = view.getInt16(i * 2, true);
      float32[i] = sample / 0x8000;
    }

    return float32;
  }

  async disconnect(): Promise<void> {
    try {
      if (this.session) {
        this.session.close();
      }
      this.isConnected = false;
      this.events.onDisconnected?.();
      this.stopMicrophoneCapture();
      if (this.audioContext) {
        await this.audioContext.close().catch(() => undefined);
        this.audioContext = null;
        this.audioQueueTime = 0;
      }
    } catch (error) {
      this.log.error('Error during disconnect:', error);
    }
  }


  // Send a text message (useful for testing or mixed input)
  async sendMessage(message: string): Promise<void> {
    if (!this.session || !this.isConnected) {
      throw new Error('Not connected to session');
    }

    try {
      if (this.config.enableDebugLogs) {
        this.log.info('📤 Sending message:', message.substring(0, 100));
      }

      // Use the SDK's built-in sendMessage which handles the conversation flow properly
      this.session.sendMessage(message);

    } catch (error) {
      // Check if error is about existing response in progress
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('already has an active response')) {
        this.log.warn('⚠️ Response already in progress, message not sent');
        // Don't throw - just log the warning
        return;
      }
      throw error;
    }
  }

  // Process queued messages
  private processQueue(): void {
    if (this.responseQueue.length > 0 && !this.isResponseInProgress) {
      const nextMessage = this.responseQueue.shift();
      if (nextMessage) {
        if (this.config.enableDebugLogs) {
          this.log.info('📨 Processing queued message:', nextMessage.substring(0, 50));
        }
        this.sendMessage(nextMessage).catch(error => {
          this.log.error('Error processing queued message:', error);
        });
      }
    }
  }

  // Official RealtimeSession conversation history management
  clearConversationHistory(): void {
    if (!this.session) {
      this.log.warn('Cannot clear history: session not initialized');
      return;
    }

    try {
      this.session.updateHistory([]);
      if (this.config.enableDebugLogs) {
        this.log.info('🗑️ Conversation history cleared');
      }
    } catch (error) {
      this.log.error('Failed to clear conversation history:', error);
    }
  }

  // Get current conversation history from the session
  getConversationHistory(): unknown[] {
    if (!this.session) {
      return [];
    }

    try {
      return this.session.history || [];
    } catch (error) {
      this.log.error('Failed to get conversation history:', error);
      return [];
    }
  }

  // Update configuration
  updateConfig(newConfig: Partial<ModernVoiceClientConfig>): void {
    const oldConfig = this.config;
    this.config = { ...this.config, ...newConfig };

    // If credentials changed (ephemeralToken or apiKey), recreate transport
    const credentialsChanged =
      newConfig.ephemeralToken !== undefined && newConfig.ephemeralToken !== oldConfig.ephemeralToken ||
      newConfig.apiKey !== undefined && newConfig.apiKey !== oldConfig.apiKey;

    // CRITICAL FIX: Recreate transport with credentials regardless of connection state
    // The previous check `&& !this.isConnected` prevented credential updates after connection started
    if (credentialsChanged) {
      if (this.config.enableDebugLogs) {
        this.log.info('🔄 Credentials updated - recreating transport with new credentials', {
          wasConnected: this.isConnected,
          hasEphemeralToken: !!this.config.ephemeralToken,
          hasApiKey: !!this.config.apiKey
        });
      }

      // Recreate transport with new credentials
      const targetModel = this.config.model ?? 'gpt-4o-realtime-preview-2024-12-17';
      const useWebSocket = this.config.transport === 'websocket' || Boolean(this.config.relayUrl);
      const isRelay = Boolean(this.config.relayUrl);
      const apiKey = this.config.ephemeralToken
        ?? this.config.apiKey
        ?? (isRelay ? 'relay' : undefined);

      if (useWebSocket) {
        this.transport = new OpenAIRealtimeWebSocket({
          model: targetModel,
          apiKey,
          useInsecureApiKey: this.config.useInsecureApiKey ?? true,
        });
      } else {
        this.transport = new OpenAIRealtimeWebRTC({
          model: targetModel,
          apiKey,
          useInsecureApiKey: this.config.useInsecureApiKey ?? true,
        });
      }

      // Recreate session with new transport
      const isWebRTC = !useWebSocket;
      const sessionOptions: Partial<RealtimeSessionOptions> = {
        transport: this.transport,
      };

      if (isWebRTC) {
        this.pendingSessionConfig = this.buildSessionConfigurationPayload();
      }

      this.session = new RealtimeSession(this.agent, sessionOptions);
      this.patchRealtimeSessionConstructor();
      this.patchSessionSendEvent();
      this.setupEventListeners();

      // NOTE: DataChannel patching will be applied automatically when configureSessionAfterConnection()
      // is called, right before sending the session.update message

      if (this.config.enableDebugLogs) {
        this.log.info('✅ Transport and session recreated with new credentials');
      }
    }
  }

  getConnectionStatus(): {
    isConnected: boolean;
    transport: 'webrtc' | 'websocket';
    connectionState: string;
    iceConnectionState: string;
    toolRegistrationStatus: 'pending' | 'success' | 'failed';
    hasCredentials: boolean;
    credentialType?: 'apiKey' | 'ephemeralToken' | 'relay';
    patchApplied: boolean;
    patchDetails: {
      constructorPatched: boolean;
      sessionPatched: boolean;
      dataChannelPatched: boolean;
    };
    initializationStrategy: 'standard' | 'alternative';
  } {
    const transport = this.transport as { _dataChannel?: { _patchedForSessionType?: boolean } };
    const session: any = this.session;
    const constructorPatched = !!session?._constructorPatched;
    const sessionPatched = !!session?._patchedForSessionType;
    const dataChannelPatched = !!transport?._dataChannel?._patchedForSessionType;

    return {
      isConnected: this.isConnected,
      transport: this.isUsingWebRTCTransport() ? 'webrtc' : 'websocket',
      connectionState: this.connectionState,
      iceConnectionState: this.iceConnectionState,
      toolRegistrationStatus: this.toolRegistrationStatus,
      hasCredentials: Boolean(this.config.apiKey || this.config.ephemeralToken || this.config.relayUrl),
      credentialType: this.config.ephemeralToken ? 'ephemeralToken' :
                      this.config.apiKey ? 'apiKey' :
                      this.config.relayUrl ? 'relay' : undefined,
      patchApplied: constructorPatched || sessionPatched || dataChannelPatched,
      patchDetails: {
        constructorPatched,
        sessionPatched,
        dataChannelPatched,
      },
      initializationStrategy: this.config.useAlternativeInitialization ? 'alternative' : 'standard',
    };
  }

  private getAssistantTools(): NonNullable<ConstructorParameters<typeof RealtimeAgent>[0]['tools']> {
    return [
      calculateStockLevelsTool,
      queryInventoryTool,
      queryRecentEventsTool,
      queryVendorsTool,
      queryCustomersTool,
      queryPartnersTool,
      addInventoryTool,
      updateInventoryTool,
      getTemperatureTool,
      getCurrentViewTool,
      fillFormFieldTool,
      submitFormTool,
      clearFormTool,
      getFormFieldsTool,
    ];
  }

  private getAssistantInstructions(): string {
    return `You are a professional seafood inventory assistant for Pacific Cloud Seafoods. Your primary role is to help process voice commands for inventory management and form filling with extremely high accuracy for seafood industry terminology.

CORE EXPERTISE:
- 200+ seafood species recognition including scientific names, common names, and regional aliases
- Seafood processing methods: Fresh, Frozen, IQF, Live, Smoked, Cured, H&G, Fillets
- Quality grades: Premium, Grade A, Sashimi Grade, Select, Restaurant Quality
- Market forms: Whole, Fillets, H&G, Portions, Steaks, Loins, Clusters, Picked meat
- Industry units: lbs, kg, cases, dozens, pieces, each, bags
- Vendor/supplier recognition for major seafood companies
- HACCP compliance terms and temperature requirements

INVENTORY QUERY PRIORITIES (CRITICAL):
1. For "what do we have?", "what's in stock?", "how much X?" → ALWAYS use calculate_stock_levels tool FIRST
2. This tool analyzes all inventory_events (receiving, sales, disposal) to calculate accurate current stock
3. The tool returns product name, quantity, unit, and category for each item
4. query_inventory tool only shows product catalog, not actual stock levels
5. query_recent_events shows transaction history, not current stock

PROCESSING PRIORITIES:
1. Species identification with confidence scoring
2. Quantity and unit extraction with conversions
3. Event type classification (receiving, sale, disposal, physical count)
4. Vendor/customer identification
5. Quality and condition assessment
6. HACCP data extraction (temperature, dates, conditions)

FORM FILLING CAPABILITIES:
- You can fill out forms that users are currently viewing via voice commands
- Use get_form_fields to see what fields are available in the active form
- Use fill_form_field to populate individual fields with values
- Use submit_form when the user asks to save or submit the form
- Use clear_form when the user asks to reset or start over
- Always confirm field values before submitting
- If a form field is not available, tell the user which fields are available

RESPONSE GUIDELINES:
- Be concise and natural - avoid reading back long lists of data
- Use function calls immediately when you have enough information
- After tool calls, provide brief confirmation (e.g., "Done" or "Added 10 pounds of salmon")
- For inventory queries: give a quick summary (e.g., "You have 3 items in stock" NOT reading each one)
- For inventory additions: brief confirmation only (e.g., "Added" or "Recorded")
- Only ask for clarification if critical details are truly missing
- For form filling: fill fields quietly, only confirm when complete
- Be professional but conversational - like a helpful colleague, not a verbose assistant
- Prioritize food safety and HACCP compliance
- Keep responses under 10 words when possible

IMPORTANT: If you encounter authentication or database access errors when using tools, inform the user that they need to check their login status and database permissions.`;
  }

  /**
   * Ensure WebRTC offer/answer requests include the required beta header for the GA API.
   */
  private patchRealtimeFetchHeader(): void {
    if (typeof window === 'undefined') return;
    const marker = '__openai_realtime_ga_patched__';
    if ((window as unknown as Record<string, unknown>)[marker]) return;

    const originalFetch: typeof window.fetch = window.fetch.bind(window) as typeof window.fetch;
    (window as unknown as Record<string, unknown>)[marker] = true;

    const patchedFetch: typeof window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      try {
        let urlStr: string | undefined;
        if (typeof input === 'string') {
          urlStr = input;
        } else if (input instanceof URL) {
          urlStr = input.toString();
        } else if (typeof Request !== 'undefined' && input instanceof Request) {
          urlStr = input.url;
        }

        if (urlStr?.includes('/v1/realtime')) {
          const baseHeaders = new Headers(
            init?.headers ?? (typeof Request !== 'undefined' && input instanceof Request ? input.headers : undefined)
          );

          if (urlStr.includes('/v1/realtime/sessions')) {
            baseHeaders.set('Content-Type', 'application/json');
          }

          // Ensure correct headers for SDP negotiation endpoint
          if (urlStr.includes('/v1/realtime/calls')) {
            baseHeaders.set('Accept', 'application/sdp');
            // For POST offer->answer exchange, Content-Type must be application/sdp
            const method = (init?.method ?? (typeof Request !== 'undefined' && input instanceof Request ? input.method : 'GET')).toUpperCase();
            if (method === 'POST') {
              baseHeaders.set('Content-Type', 'application/sdp');
            }
            if (!baseHeaders.has('OpenAI-Beta')) {
              baseHeaders.set('OpenAI-Beta', 'realtime=v1');
            }
          }

          const performFetch = async (requestInput: RequestInfo | URL, requestInit?: RequestInit) => {
            const response = await originalFetch(requestInput, requestInit);
            if (!response.ok) {
              try {
                const clone = response.clone();
                const text = await clone.text();
                this.log.warn(`Realtime fetch failure: status=${response.status} body=${text.slice(0, 200)}`);
              } catch (err) {
                this.log.warn(`Realtime fetch failure: status=${response.status} (no body) error=${String(err)}`);
              }
            }
            return response;
          };

          // Rewrite OpenAI calls negotiation to local proxy to avoid CORS
          let finalUrlStr: string = urlStr ?? '';
          try {
            const abs = new URL(urlStr, window.location.origin);
            const isCalls = abs.pathname === '/v1/realtime/calls';
            const isSessions = abs.pathname === '/v1/realtime/sessions';
            const isOpenAI = abs.host.includes('api.openai.com');
            if (isCalls && isOpenAI) {
              let search = abs.search;
              if (!search || search.length === 0) {
                const modelOverride = (typeof window !== 'undefined'
                  ? (window as unknown as Record<string, unknown>).__openai_realtime_model as string | undefined
                : undefined);
                if (modelOverride) {
                  search = `?model=${encodeURIComponent(modelOverride)}`;
                }
              }
              finalUrlStr = `/api/openai/realtime/calls${search || ''}`;
              const currentBetaHeader = baseHeaders.get('OpenAI-Beta');
              if (!currentBetaHeader?.includes('realtime=v1')) {
                baseHeaders.set('OpenAI-Beta', [currentBetaHeader, 'realtime=v1'].filter(Boolean).join(', '));
              }
            } else if (isSessions && isOpenAI) {
              baseHeaders.set('Content-Type', 'application/json');
              finalUrlStr = `/api/openai/realtime/sessions${abs.search || ''}`;
              const currentBetaHeader = baseHeaders.get('OpenAI-Beta');
              if (!currentBetaHeader?.includes('assistants=v2')) {
                baseHeaders.set('OpenAI-Beta', [currentBetaHeader, 'assistants=v2'].filter(Boolean).join(', '));
              }
              if (!baseHeaders.get('OpenAI-Beta')?.includes('realtime=v1')) {
                const existing = baseHeaders.get('OpenAI-Beta');
                baseHeaders.set('OpenAI-Beta', [existing, 'realtime=v1'].filter(Boolean).join(', '));
              }
            } else {
              finalUrlStr = abs.toString();
            }
          } catch {
            // If URL parsing fails, keep original string
          }

          const isRequest = typeof Request !== 'undefined' && input instanceof Request;
          if (isRequest) {
            const req = input as Request;
            const method = req.method.toUpperCase();
            let body: BodyInit | undefined;
            if (method !== 'GET' && method !== 'HEAD') {
              try {
                // Clone to preserve original request and read as text (works for SDP and JSON)
                body = await req.clone().text();
                body = this.sanitizeRealtimeSessionRequestBody(urlStr ?? finalUrlStr, body);
              } catch {
                body = undefined;
              }
            }

            // CRITICAL FIX: Merge original request headers (including Authorization) with base headers
            // This ensures the Authorization header from the SDK is preserved when proxying
            const mergedHeaders = new Headers(baseHeaders);
            req.headers.forEach((value, key) => {
              // Only override base headers if not already set
              if (!mergedHeaders.has(key)) {
                mergedHeaders.set(key, value);
              }
            });

            // DEBUG: Log if this is an SDP call to verify Authorization header is present
            if (finalUrlStr.includes('/api/openai/realtime/calls')) {
              const safeHeaders = Array.from(mergedHeaders.entries()).map(([k, v]) => ({
                key: k,
                value: /authorization|api|cookie|secret|token/i.test(k) ? '[REDACTED]' : v
              }));
              console.log('🔍 SDP proxy request:', {
                url: finalUrlStr,
                method,
                hasAuth: mergedHeaders.has('Authorization'),
                contentType: mergedHeaders.get('Content-Type'),
                bodyType: typeof body,
                bodyLength: typeof body === 'string' ? body.length : 0,
                bodyPreview: typeof body === 'string' ? body.substring(0, 100) : null,
                headers: safeHeaders,
              });
            }

            const updatedRequest = new Request(finalUrlStr, { method, headers: mergedHeaders, body });
            return performFetch(updatedRequest);
          }

          // CRITICAL FIX: Merge init headers with baseHeaders to preserve Authorization
          const mergedInitHeaders = new Headers(baseHeaders);
          if (init?.headers) {
            const initHeaders = new Headers(init.headers);
            initHeaders.forEach((value, key) => {
              if (!mergedInitHeaders.has(key)) {
                mergedInitHeaders.set(key, value);
              }
            });
          }

          const outgoingInit: RequestInit = {
            ...init,
            headers: mergedInitHeaders,
          };

          if (init?.body && typeof init.body === 'string') {
            outgoingInit.body = this.sanitizeRealtimeSessionRequestBody(urlStr ?? finalUrlStr, init.body);
            
            // Debug log the sanitized payload before fetch call
            if (this.config.enableDebugLogs && finalUrlStr.includes('/api/realtime/sessions')) {
              try {
                const sanitizedPayload = JSON.parse(outgoingInit.body as string);
                this.log.info('🔍 Sanitized realtime session POST payload:', JSON.stringify(sanitizedPayload, null, 2));
                this.log.info('🔍 Confirming no session block present:', !sanitizedPayload?.session);
              } catch (error) {
                this.log.info('🔍 Non-JSON payload being sent to realtime sessions endpoint:', outgoingInit.body);
              }
            }
          }

          return performFetch(finalUrlStr, outgoingInit);
        }
      } catch (error) {
        this.log.warn('Realtime fetch patch fallback:', error);
      }

      return originalFetch(input as RequestInfo, init);
    };

    (window as unknown as Record<string, unknown>).fetch = patchedFetch as unknown as typeof window.fetch;
  }

  /**
   * Check and request microphone permission for WebRTC mode
   * This is called before connecting to ensure the browser has permission
   */
  private async checkMicrophonePermission(): Promise<void> {
    if (typeof window === 'undefined') {
      this.log.warn('⚠️ Window is undefined, skipping microphone permission check');
      return;
    }

    if (!navigator.mediaDevices?.getUserMedia) {
      const error = 'getUserMedia not available - microphone access not supported in this browser';
      this.log.error('❌', error);
      throw new Error(error);
    }

    try {
      this.log.info('🎤 Checking microphone permission for WebRTC mode...');
      
      // Request microphone permission
      // We need to actually request access to prompt the user
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        }
      });

      // Permission granted - log success and immediately close the stream
      // The OpenAI SDK will create its own stream when needed
      this.log.info('✅ Microphone permission granted');
      this.log.info(`🎤 Found ${stream.getAudioTracks().length} audio track(s)`);
      
      // Stop all tracks to release the microphone
      stream.getTracks().forEach(track => {
        track.stop();
        this.log.debug(`🛑 Stopped track: ${track.label}`);
      });
      
      this.log.info('✅ Microphone permission verified and released for SDK use');
    } catch (error) {
      this.log.error('❌ Microphone permission check failed:', error);
      
      let errorMessage = 'Microphone permission denied or unavailable';
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          errorMessage = 'Microphone permission denied. Please allow microphone access in your browser settings and try again.';
        } else if (error.name === 'NotFoundError') {
          errorMessage = 'No microphone found. Please connect a microphone and try again.';
        } else if (error.name === 'NotReadableError') {
          errorMessage = 'Microphone is already in use by another application. Please close other apps using the microphone and try again.';
        } else {
          errorMessage = `Microphone error: ${error.message}`;
        }
      }
      
      // This will be caught by the connect() method and trigger onError
      throw new Error(errorMessage);
    }
  }

  private async startMicrophoneCapture(): Promise<void> {
    console.log('🎤 startMicrophoneCapture() called');
    if (typeof window === 'undefined') {
      console.log('⚠️ Window is undefined, skipping microphone');
      return;
    }
    if (this.micStream) {
      console.log('⚠️ Microphone already active');
      return;
    }
    if (!navigator.mediaDevices?.getUserMedia) {
      console.log('❌ getUserMedia not available');
      return;
    }

    try {
      this.micStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 24000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      });

      this.micAudioContext = new AudioContext({ sampleRate: 24000 });
      this.micSource = this.micAudioContext.createMediaStreamSource(this.micStream);
      this.micProcessor = this.micAudioContext.createScriptProcessor(2048, 1, 1);
      
      // CRITICAL FIX: Don't use gain node for output - it's only for processing
      // Connecting processor directly prevents audio from being muted
      this.micProcessor.onaudioprocess = (event) => {
        const channelData = event.inputBuffer.getChannelData(0);
        if (this.config.enableDebugLogs && Math.random() < 0.01) { // Log 1% of frames
          const maxSample = Math.max(...Array.from(channelData).map(Math.abs));
          const rms = this.calculateRms(channelData);
          this.log.info(`🎤 Audio frame: ${channelData.length} samples, max: ${maxSample.toFixed(4)}, RMS: ${rms.toFixed(4)}`);
        }
        this.processMicrophoneFrame(channelData);
      };

      // Connect microphone source to processor for capture
      // Do NOT connect to audio destination to prevent echo/feedback
      this.micSource.connect(this.micProcessor);

      if (this.config.enableDebugLogs) {
        this.log.info('🎙️ Microphone capture started for realtime session');
        this.log.info(`🎤 Audio context state: ${this.micAudioContext.state}`);
        this.log.info(`🎤 Audio context sample rate: ${this.micAudioContext.sampleRate}`);
        this.log.info(`🎤 Stream tracks: ${this.micStream.getAudioTracks().length}`);
      }
    } catch (error) {
      this.log.error('Failed to access microphone:', error);
      let errorMessage = 'Microphone permission denied or unavailable';
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          errorMessage = 'Microphone permission denied. Please allow microphone access and try again.';
        } else if (error.name === 'NotFoundError') {
          errorMessage = 'No microphone found. Please connect a microphone and try again.';
        } else {
          errorMessage = `Microphone error: ${error.message}`;
        }
      }
      this.events.onError?.(errorMessage);
    }
  }

  private stopMicrophoneCapture(): void {
    this.userSpeaking = false;
    this.lastVoiceActivity = 0;

    if (this.micProcessor) {
      this.micProcessor.disconnect();
      this.micProcessor.onaudioprocess = null;
      this.micProcessor = null;
    }

    if (this.micSource) {
      this.micSource.disconnect();
      this.micSource = null;
    }

    // Removed gain node since we don't use it anymore

    if (this.micAudioContext) {
      void this.micAudioContext.close().catch(() => undefined);
      this.micAudioContext = null;
    }

    if (this.micStream) {
      this.micStream.getTracks().forEach((track) => track.stop());
      this.micStream = null;
    }
  }

  private processMicrophoneFrame(channelData: Float32Array): void {
    if (!this.session || !this.isConnected) {
      return;
    }

    // Validate audio data
    if (!channelData || channelData.length === 0) {
      if (this.config.enableDebugLogs) {
        this.log.warn('🎤 Received empty audio frame, skipping');
      }
      return;
    }

    const pcm = this.floatToPcm16(channelData);
    if (!pcm || pcm.length === 0) {
      if (this.config.enableDebugLogs) {
        this.log.warn('🎤 PCM conversion failed, skipping frame');
      }
      return;
    }

    // Check for silence to avoid sending empty audio
    const rms = this.calculateRms(channelData);
    const now = performance.now();

    // Only send audio if there's actual sound
    if (rms >= 0.001) { // Threshold for meaningful audio
      try {
        this.session.sendAudio(pcm.buffer);
        if (this.config.enableDebugLogs && Math.random() < 0.1) { // Log 10% of sends
          this.log.info(`🎤 Sent ${pcm.length} audio samples, RMS: ${rms.toFixed(4)}`);
        }
      } catch (error) {
        this.log.error('Failed to stream audio to realtime session:', error);
        return;
      }
    }

    // Voice activity detection
    if (rms >= this.voiceActivityThreshold) {
      this.lastVoiceActivity = now;
      if (!this.userSpeaking) {
        this.userSpeaking = true;
        this.events.onListening?.();
        if (this.config.enableDebugLogs) {
          this.log.info('🎤 Voice activity detected');
        }
      }
    } else if (this.userSpeaking && now - this.lastVoiceActivity > this.silenceTimeoutMs) {
      this.userSpeaking = false;
      this.events.onProcessing?.();
      if (this.config.enableDebugLogs) {
        this.log.info('🎤 Voice activity ended');
      }
    }
  }

  private floatToPcm16(channelData: Float32Array): Int16Array | null {
    if (!channelData || channelData.length === 0) {
      return null;
    }

    // Check if the audio data contains any meaningful signal
    let hasSignal = false;
    for (let i = 0; i < channelData.length; i++) {
      if (Math.abs(channelData[i]) > 0.0001) {
        hasSignal = true;
        break;
      }
    }

    if (!hasSignal) {
      return null; // Don't convert silent audio
    }

    const pcm = new Int16Array(channelData.length);
    for (let i = 0; i < channelData.length; i += 1) {
      const sample = Math.max(-1, Math.min(1, channelData[i]));
      pcm[i] = sample < 0 ? sample * 0x8000 : sample * 0x7fff;
    }
    return pcm;
  }

  private calculateRms(data: Float32Array): number {
    if (!data || data.length === 0) return 0;
    let sumSquares = 0;
    for (let i = 0; i < data.length; i += 1) {
      const sample = data[i];
      sumSquares += sample * sample;
    }
    return Math.sqrt(sumSquares / data.length);
  }

  private sanitizeRealtimeSessionRequestBody(rawUrl: string | undefined, body: BodyInit | undefined): BodyInit | undefined {
    if (!rawUrl || !rawUrl.includes('/v1/realtime/sessions') || typeof body !== 'string') {
      return body;
    }

    try {
      const payload = JSON.parse(body);

      if (payload?.session?.type) {
        if (this.config.enableDebugLogs) {
          this.log.warn('🚨 Detected session.type in POST body - removing to prevent API error');
        }
        delete payload.session.type;
      }

      if (payload?.session?.transport) {
        delete payload.session.transport;
      }

      if (payload?.type === 'session.type') {
        if (this.config.enableDebugLogs) {
          this.log.warn('🚨 Detected session.type parameter in POST body - removing to prevent API error');
        }
        delete payload.type;
      }

      return JSON.stringify(payload);
    } catch {
      return body;
    }
  }

  /**
   * Validate tool setup and authentication readiness
   */
  private validateToolSetup(): void {
    try {
      // Validate that all required tools are properly imported
      const requiredTools = [queryInventoryTool, addInventoryTool, updateInventoryTool, getTemperatureTool];
      const missingTools = requiredTools.filter(tool => !tool);

      if (missingTools.length > 0) {
        this.log.warn('⚠️ Some voice inventory tools are not properly imported:', missingTools.length);
      }

      // Validate tool structure - OpenAI Agents SDK tools use 'invoke' not 'execute'
      requiredTools.forEach((tool, index) => {
        if (tool && typeof tool.invoke !== 'function') {
          this.log.warn(`⚠️ Tool ${index} is missing invoke function`);
        }
      });

      if (this.config.enableDebugLogs) {
        this.log.info('✅ Voice inventory tools validation completed');
      }
    } catch (error) {
      this.log.error('🚫 Tool setup validation failed:', error);
    }
  }

  /**
   * Initialize voice authentication and set up database client
   */
  private async initializeVoiceAuthentication(): Promise<void> {
    try {
      if (this.config.enableDebugLogs) {
        this.log.info('🔐 Initializing voice authentication...');
      }

      // Initialize voice authentication manager
      const authResult = await voiceAuthManager.initialize();

      if (!authResult.success) {
        this.log.warn('⚠️ Voice authentication initialization failed:', authResult.status.error);
        this.log.warn('Voice inventory operations may be limited without authentication');
        return;
      }

      // Set authenticated client for realtime tools
      if (authResult.client) {
        await setAuthenticatedClient(authResult.client);
        if (this.config.enableDebugLogs) {
          this.log.info('✅ Voice authentication ready for inventory operations');
        }
      }
    } catch (error) {
      this.log.error('🚫 Voice authentication setup failed:', error);
      this.log.warn('Voice inventory operations may not work properly without authentication');
    }
  }

  /**
   * Enhance error messages with specific guidance for common issues
   */
  private enhanceErrorMessage(originalMessage: string): string {
    const message = originalMessage.toLowerCase();

    // WebRTC SDP parsing errors
    if (message.includes('setremotedescription') || message.includes('failed to parse sessiondescription') || message.includes('expect line: v=')) {
      return `WebRTC connection failed: The server returned invalid SDP data, likely due to an authentication or API configuration issue. This usually means the OpenAI API returned a JSON error response instead of proper SDP. Check your API key configuration and ensure the ephemeral token is valid. (Original: ${originalMessage})`;
    }

    // Authentication-related errors
    if (message.includes('authentication') || message.includes('unauthorized') || message.includes('401')) {
      return `Authentication failed - please log in and ensure your session is valid. Voice inventory operations require user authentication. (Original: ${originalMessage})`;
    }

    if (message.includes('jwt') || message.includes('token') && !message.includes('unknown parameter')) {
      return `Authentication token issue - please refresh your login and try connecting again. (Original: ${originalMessage})`;
    }

    // Database access errors
    if (message.includes('rls') || message.includes('policy') || message.includes('permission') || message.includes('403')) {
      return `Database access denied - Row Level Security policies may be blocking voice operations. Check RLS policies and run the voice inventory migration. (Original: ${originalMessage})`;
    }

    if (message.includes('database') || message.includes('relation') || message.includes('table')) {
      return `Database connectivity issue - check your connection and ensure database tables exist. (Original: ${originalMessage})`;
    }

    // Tool execution errors
    if (message.includes('tool') && !message.includes('unknown parameter')) {
      return `Voice tool execution failed - this may be due to authentication or database access issues. Check your login status. (Original: ${originalMessage})`;
    }

    // Network/connectivity errors
    if (message.includes('network') || message.includes('fetch') || message.includes('connection') || message.includes('timeout')) {
      return `Network connectivity issue - check your internet connection and try again. (Original: ${originalMessage})`;
    }

    // OpenAI API errors - but preserve "Unknown parameter" errors
    if (message.includes('rate limit') || message.includes('quota')) {
      return `OpenAI API rate limit exceeded - please wait a moment before trying again. (Original: ${originalMessage})`;
    }

    if (message.includes('unknown parameter')) {
      // Check specifically for session.type error
      if (originalMessage.includes('session.type')) {
        return `OpenAI API rejected 'session.type' parameter: This is a known SDK bug where the @openai/agents library injects an invalid 'type: "realtime"' field into session configurations. The DataChannel patch should have stripped this field. Please check browser console for DataChannel patch logs. If the patch wasn't applied, try reconnecting. (Original error: ${originalMessage})`;
      }
      return `OpenAI API rejected session configuration: ${originalMessage}. An unsupported parameter was sent in the session update. Check for invalid fields in the configuration.`;
    }

    if (message.includes('invalid') && !message.includes('unknown parameter')) {
      return `OpenAI API validation error: ${originalMessage}. Check your configuration.`;
    }

    // WebRTC/Audio specific errors
    if (message.includes('webrtc') || message.includes('media') || message.includes('microphone')) {
      return `Audio/microphone access issue - please allow microphone permissions and try again. (Original: ${originalMessage})`;
    }

    // Return original message with context note (don't mask API errors)
    return `${originalMessage}`;
  }

  /**
   * Get diagnostic information for troubleshooting
   */
  public getDiagnosticInfo(): Record<string, any> {
    return {
      isConnected: this.isConnected,
      transport: this.isUsingWebRTCTransport() ? 'WebRTC' : 'WebSocket',
      hasEphemeralToken: !!this.config.ephemeralToken,
      hasApiKey: !!this.config.apiKey,
      isRelay: !!this.config.relayUrl,
      voiceAuthStatus: voiceAuthManager.status,
      agentTools: this.agent.tools?.length || 0,
      audioContext: !!this.audioContext,
      sessionActive: !!this.session,
    };
  }

  private verifyPatchStatus(): { dataChannelPatched: boolean; sessionEventPatched: boolean; details: string } {
    const transport = this.transport as any;
    const session = this.session as any;

    const dataChannelExists = Boolean(transport?._dataChannel);
    const sessionExists = Boolean(this.session);

    const dataChannelPatched = Boolean(transport?._dataChannel?._patchedForSessionType);
    const sessionEventPatched = Boolean(session?._patchedForSessionType);
    const mergedConfigPatched = Boolean(session?._patchedMergedSessionConfig);

    return {
      dataChannelPatched,
      sessionEventPatched,
      details: `DataChannel: ${dataChannelExists ? 'exists' : 'missing'}, Session: ${sessionExists ? 'exists' : 'missing'}, MergedConfigPatched: ${mergedConfigPatched}`
    };
  }
}

// Factory function for easy creation
export function createModernRealtimeVoiceClient(
  config: ModernVoiceClientConfig,
  events?: Partial<ModernVoiceClientEvents>
): ModernRealtimeVoiceClient {
  return new ModernRealtimeVoiceClient(config, events);
}
