/**
 * Supabase Service Role Client for Admin Operations
 * Used for temperature sensor data access that bypasses RLS
 */

import { createClient, type SupabaseClient } from '@supabase/supabase-js';

import { appEnv } from '@/lib/config/env';

/**
 * Validate service role client configuration
 */
function validateServiceRoleConfig() {
  const supabaseUrl = appEnv.supabase.url;
  const serviceRoleKey = appEnv.supabase.serviceRoleKey;

  if (!supabaseUrl) {
    throw new Error('Missing Supabase URL: VITE_SUPABASE_URL is required');
  }

  if (!serviceRoleKey) {
    throw new Error(
      'Missing Supabase service role key: SUPABASE_SERVICE_ROLE_KEY or VITE_SUPABASE_SERVICE_ROLE_KEY is required'
    );
  }

  // Additional validation for local development
  if (appEnv.isDevelopment) {
    const isLocalDocker = supabaseUrl.includes('127.0.0.1') || supabaseUrl.includes('localhost');

    if (isLocalDocker) {
      console.info('✅ [Supabase Service] Local Docker service role client configured');
    } else {
      console.warn('⚠️ [Supabase Service] Development environment using remote Supabase service role');
    }

    // Log service role key length for debugging (safe)
    console.info('[Supabase Service] Service role key length:', serviceRoleKey.length);
  }

  return { supabaseUrl, serviceRoleKey };
}

/**
 * Validate that the service role client can properly connect
 */
async function validateServiceRoleConnection(client: SupabaseClient) {
  try {
    // Test basic connectivity with a simple query
    const { error } = await client
      .from('sensors')
      .select('count', { count: 'exact', head: true });

    if (error) {
      console.warn('⚠️ [Supabase Service] Connection test failed:', error.message);
      if (appEnv.isDevelopment) {
        console.info('💡 [Supabase Service] Ensure local Supabase Docker is running: supabase start');
      }
      return false;
    }

    console.info('✅ [Supabase Service] Connection test successful');
    return true;
  } catch (error) {
    console.warn('[Supabase Service] Connection test error:', error);
    return false;
  }
}

// Validate configuration before creating client
const { supabaseUrl, serviceRoleKey } = validateServiceRoleConfig();

// Use global cache to avoid creating multiple clients in the same environment
const globalForSupabaseService = globalThis as typeof globalThis & {
  __supabaseServiceClient?: SupabaseClient;
  __supabaseServiceConfig?: {
    supabaseUrl: string;
    serviceRoleKey: string;
  };
};

const cachedClient = globalForSupabaseService.__supabaseServiceClient;
const cachedConfig = globalForSupabaseService.__supabaseServiceConfig;

const shouldReuseCachedClient =
  cachedClient &&
  cachedConfig &&
  cachedConfig.supabaseUrl === supabaseUrl &&
  cachedConfig.serviceRoleKey === serviceRoleKey;

const serviceClient = shouldReuseCachedClient
  ? cachedClient!
  : createClient(supabaseUrl, serviceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });

if (!shouldReuseCachedClient) {
  globalForSupabaseService.__supabaseServiceClient = serviceClient;
  globalForSupabaseService.__supabaseServiceConfig = { supabaseUrl, serviceRoleKey };
}

export const supabaseService = serviceClient;

// Test connection in development mode (non-blocking)
if (appEnv.isDevelopment && !shouldReuseCachedClient) {
  validateServiceRoleConnection(serviceClient).catch(() => {
    // Silently handle connection test failures
  });
}

/**
 * Fetch all active sensors using service role (bypasses RLS)
 */
export async function fetchAllSensors() {
  const { data: sensors, error } = await supabaseService
    .from('sensors')
    .select(
      `
      id,
      sensor_id,
      name,
      device_name,
      location_description,
      is_online,
      battery_level,
      is_active,
      updated_at
    `
    )
    .eq('is_active', true)
    .order('name');

  if (error) {
    console.error('Service role sensor fetch error:', error);
    throw new Error(`Failed to fetch sensors: ${error.message}`);
  }

  return sensors ?? [];
}

/**
 * Fetch latest temperature reading for a sensor using service role
 */
export async function fetchLatestReading(sensorId: number) {
  const { data: reading, error } = await supabaseService
    .from('temperature_readings')
    .select('temp_celsius, humidity, recorded_at')
    .eq('sensor_id', sensorId)
    .order('recorded_at', { ascending: false })
    .limit(1)
    .maybeSingle();

  if (error) {
    console.error(`Service role reading fetch error for sensor ${sensorId}:`, error);
    return null;
  }

  return reading;
}

/**
 * Fetch active alerts for a sensor using service role
 */
export async function fetchActiveAlerts(sensorId: number) {
  const { data: alerts, error } = await supabaseService
    .from('temperature_alerts')
    .select('id, title, message, severity, created_at')
    .eq('sensor_id', sensorId)
    .eq('alert_status', 'active')
    .order('created_at', { ascending: false });

  if (error) {
    console.error(`Service role alerts fetch error for sensor ${sensorId}:`, error);
    return [];
  }

  return alerts ?? [];
}
