import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { customerSchema } from '../../lib/validation';
import { createCustomer, updateCustomer } from '../../lib/api';
import { Mic } from 'lucide-react';
import { useVoiceForm } from '../../hooks/useVoiceForm';
import type { Customer } from '../../types';

interface CustomerFormProps {
  customer?: Customer;
  onSuccess: () => void;
  onCancel: () => void;
}

type CustomerFormData = {
  name: string;
  contactName?: string;
  email?: string;
  phone?: string;
  address?: string;
  channelType: 'wholesale' | 'retail' | 'distributor';
  status: 'active' | 'inactive';
  paymentTerms?: string;
  creditLimit?: number;
  customerSource?: string;
};

interface VoiceInputData {
  product?: string;
  vendor?: string;
  quantity?: number;
  unit?: string;
}

export default function CustomerForm({ customer, onSuccess, onCancel }: CustomerFormProps) {
  const [showVoiceInput, setShowVoiceInput] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const form = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: customer ?? {
      status: 'active',
      channelType: 'wholesale',
    },
  });

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = form;

  // Enable voice control for this form
  useVoiceForm('customer-form', form, {
    name: { label: 'Business Name', type: 'text', required: true },
    contactName: { label: 'Contact Name', type: 'text', required: false },
    email: { label: 'Email Address', type: 'email', required: false },
    phone: { label: 'Phone Number', type: 'tel', required: false },
    address: { label: 'Address', type: 'text', required: false },
    channelType: { label: 'Channel Type', type: 'select', required: true },
    status: { label: 'Status', type: 'select', required: true },
    paymentTerms: { label: 'Payment Terms', type: 'text', required: false },
    creditLimit: { label: 'Credit Limit', type: 'number', required: false },
    customerSource: { label: 'Customer Source', type: 'text', required: false },
  });

  const channelType = watch('channelType');
  const isRetail = channelType === 'retail';

  const handleVoiceData = (data: VoiceInputData) => {
    // Map voice input data to customer fields
    if (data.product) setValue('name', data.product); // Business name from product
    if (data.vendor) setValue('contactName', data.vendor); // Contact name from vendor
    setShowVoiceInput(false);
  };

  const handleVoiceError = (error: string) => {
    console.error('Voice input error:', error);
    setShowVoiceInput(false);
  };

  const onSubmit = async (data: CustomerFormData) => {
    setSubmitError(null);
    try {
      console.log('Submitting customer data:', data);
      let result: {
        success: boolean;
        error?: Error | string | { message?: string };
        data?: Customer;
      };

      if (customer?.id) {
        console.log('Updating existing customer:', customer.id);
        result = await updateCustomer(customer.id, data);
      } else {
        console.log('Creating new customer');
        result = await createCustomer(data);
      }

      if (!result.success) {
        console.error('Error saving customer:', result.error);
        const errorMessage =
          result.error && typeof result.error === 'object' && 'message' in result.error
            ? (result.error as { message?: string }).message
            : typeof result.error === 'string'
              ? result.error
              : 'Failed to save customer';
        setSubmitError(errorMessage ?? 'Failed to save customer');
        return;
      }

      console.log('Customer saved successfully:', result.data);
      onSuccess();
    } catch (error) {
      console.error('Error saving customer:', error);
      setSubmitError(error instanceof Error ? error.message : 'An unexpected error occurred');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-end">
        <button
          type="button"
          onClick={() => setShowVoiceInput(!showVoiceInput)}
          className="flex items-center gap-2 px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
        >
          <Mic className="w-5 h-5" />
          {showVoiceInput ? 'Hide Voice Input' : 'Use Voice Input'}
        </button>
      </div>

      {showVoiceInput && (
        <div className="bg-gray-50 rounded-lg p-4">
          <p className="text-sm text-gray-600">Voice input feature coming soon. Please use the main voice assistant for now.</p>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {submitError && (
            <div className="md:col-span-2 bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-sm text-red-600">{submitError}</p>
            </div>
          )}

          <div>
            <label htmlFor="customer-name" className="block text-sm font-medium text-gray-700">
              Business Name {!isRetail && <span className="text-red-500">*</span>}
            </label>
            <input
              id="customer-name"
              type="text"
              {...register('name')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.name ? 'true' : 'false'}
              aria-describedby={errors.name ? 'customer-name-error' : undefined}
            />
            {errors.name && (
              <p id="customer-name-error" className="mt-1 text-sm text-red-600">
                {errors.name.message}
              </p>
            )}
          </div>

          <div>
            <label
              htmlFor="customer-contactName"
              className="block text-sm font-medium text-gray-700"
            >
              Customer Name <span className="text-red-500">*</span>
            </label>
            <input
              id="customer-contactName"
              type="text"
              {...register('contactName')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.contactName ? 'true' : 'false'}
              aria-describedby={errors.contactName ? 'customer-contactName-error' : undefined}
            />
            {errors.contactName && (
              <p id="customer-contactName-error" className="mt-1 text-sm text-red-600">
                {errors.contactName.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="customer-source" className="block text-sm font-medium text-gray-700">
              Referral Source
            </label>
            <input
              id="customer-source"
              type="text"
              {...register('customerSource')}
              placeholder="How did you hear about us?"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>

          <div>
            <label htmlFor="customer-email" className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <input
              id="customer-email"
              type="email"
              {...register('email')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.email ? 'true' : 'false'}
              aria-describedby={errors.email ? 'customer-email-error' : undefined}
            />
            {errors.email && (
              <p id="customer-email-error" className="mt-1 text-sm text-red-600">
                {errors.email.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="customer-phone" className="block text-sm font-medium text-gray-700">
              Phone
            </label>
            <input
              id="customer-phone"
              type="tel"
              {...register('phone')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.phone ? 'true' : 'false'}
              aria-describedby={errors.phone ? 'customer-phone-error' : undefined}
            />
            {errors.phone && (
              <p id="customer-phone-error" className="mt-1 text-sm text-red-600">
                {errors.phone.message}
              </p>
            )}
          </div>

          <div className="md:col-span-2">
            <label htmlFor="customer-address" className="block text-sm font-medium text-gray-700">
              Address
            </label>
            <input
              id="customer-address"
              type="text"
              {...register('address')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.address ? 'true' : 'false'}
              aria-describedby={errors.address ? 'customer-address-error' : undefined}
            />
            {errors.address && (
              <p id="customer-address-error" className="mt-1 text-sm text-red-600">
                {errors.address.message}
              </p>
            )}
          </div>

          <div>
            <label
              htmlFor="customer-channelType"
              className="block text-sm font-medium text-gray-700"
            >
              Channel Type
            </label>
            <select
              id="customer-channelType"
              {...register('channelType')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.channelType ? 'true' : 'false'}
              aria-describedby={errors.channelType ? 'customer-channelType-error' : undefined}
            >
              <option value="wholesale">Wholesale</option>
              <option value="retail">Retail</option>
              <option value="distributor">Distributor</option>
            </select>
            {errors.channelType && (
              <p id="customer-channelType-error" className="mt-1 text-sm text-red-600">
                {errors.channelType.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="customer-status" className="block text-sm font-medium text-gray-700">
              Status
            </label>
            <select
              id="customer-status"
              {...register('status')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.status ? 'true' : 'false'}
              aria-describedby={errors.status ? 'customer-status-error' : undefined}
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
            {errors.status && (
              <p id="customer-status-error" className="mt-1 text-sm text-red-600">
                {errors.status.message}
              </p>
            )}
          </div>

          <div>
            <label
              htmlFor="customer-paymentTerms"
              className="block text-sm font-medium text-gray-700"
            >
              Payment Terms
            </label>
            <input
              id="customer-paymentTerms"
              type="text"
              {...register('paymentTerms')}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.paymentTerms ? 'true' : 'false'}
              aria-describedby={errors.paymentTerms ? 'customer-paymentTerms-error' : undefined}
            />
            {errors.paymentTerms && (
              <p id="customer-paymentTerms-error" className="mt-1 text-sm text-red-600">
                {errors.paymentTerms.message}
              </p>
            )}
          </div>

          <div>
            <label
              htmlFor="customer-creditLimit"
              className="block text-sm font-medium text-gray-700"
            >
              Credit Limit
            </label>
            <input
              id="customer-creditLimit"
              type="number"
              min="0"
              {...register('creditLimit', { valueAsNumber: true })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              aria-invalid={errors.creditLimit ? 'true' : 'false'}
              aria-describedby={errors.creditLimit ? 'customer-creditLimit-error' : undefined}
            />
            {errors.creditLimit && (
              <p id="customer-creditLimit-error" className="mt-1 text-sm text-red-600">
                {errors.creditLimit.message}
              </p>
            )}
          </div>
        </div>

        <div className="flex justify-end gap-4">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
          >
            {isSubmitting ? 'Saving...' : customer ? 'Update Customer' : 'Create Customer'}
          </button>
        </div>
      </form>
    </div>
  );
}
