import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../lib/supabase';
import { RefreshCw, Package, TrendingUp, TrendingDown } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import type { Product } from '../types/schema';
import ProductDetails from './ProductDetails';

import { VoiceAssistantManager } from './voice-assistant';

interface StockItem {
  product_name: string;
  current_quantity: number;
  unit: string;
  last_updated: string;
  event_count: number;
}

export default function CurrentStockView() {
  const { user, session } = useAuth();
  const [stockItems, setStockItems] = useState<StockItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [highlightedProduct, setHighlightedProduct] = useState<string | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  const fetchCurrentStock = useCallback(async () => {
    if (!session || !user) return;

    console.log('📊 Fetching current_stock view...');
    const { data, error } = await supabase
      .from('current_stock')
      .select('*')
      .order('product_name', { ascending: true });

    if (error) {
      console.error('❌ Error fetching current_stock:', error);
      return;
    }

    console.log('✅ Current stock data:', data);
    setStockItems(data || []);
    setLastRefresh(new Date());
    setLoading(false);
  }, [session, user]);

  const handleManualRefresh = async () => {
    setRefreshing(true);
    console.log('🔄 Triggering manual refresh of current_stock view...');

    try {
      const { error } = await supabase.rpc('refresh_current_stock');
      if (error) {
        console.error('❌ Refresh error:', error);
      } else {
        console.log('✅ View refreshed successfully');
      }
    } catch (err) {
      console.error('❌ Refresh failed:', err);
    }

    // Re-fetch data
    await fetchCurrentStock();
    setRefreshing(false);
  };

  const handleProductClick = async (productName: string) => {
    if (!session || !user) return;

    console.log('📦 Fetching product details for:', productName);
    const { data, error } = await supabase
      .from('Products')
      .select('*')
      .eq('name', productName)
      .single();

    if (error) {
      console.error('❌ Error fetching product:', error);
      return;
    }

    if (data) {
      setSelectedProduct(data);
    }
  };

  useEffect(() => {
    fetchCurrentStock();
  }, [fetchCurrentStock]);

  // Subscribe to inventory_events changes
  useEffect(() => {
    if (!session || !user) return;

    console.log('👂 Subscribing to inventory_events realtime updates...');
    const channel = supabase
      .channel('current-stock-sync')
      .on(
        'postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'inventory_events' },
        (payload) => {
          console.log('📦 New inventory event detected:', payload);
          // Wait a moment for the view to refresh, then re-fetch
          setTimeout(() => {
            console.log('🔄 Auto-refreshing current_stock after new event...');
            fetchCurrentStock();
          }, 1000);
        }
      )
      .subscribe((status) => {
        console.log('📡 Realtime subscription status:', status);
      });

    return () => {
      supabase.removeChannel(channel);
    };
  }, [session, user, fetchCurrentStock]);

  // Also listen for custom inventory-updated events (fallback for voice assistant)
  useEffect(() => {
    const handleInventoryUpdate = (event: Event) => {
      const customEvent = event as CustomEvent<{ eventType: string; source: string; timestamp: number }>;
      console.log('🎤 CurrentStockView refreshing after voice update:', customEvent.detail);
      fetchCurrentStock();
    };

    window.addEventListener('inventory-updated', handleInventoryUpdate);
    
    return () => {
      window.removeEventListener('inventory-updated', handleInventoryUpdate);
    };
  }, [fetchCurrentStock]);

  // Listen for product highlighting requests
  useEffect(() => {
    const handleHighlightProduct = (event: Event) => {
      const customEvent = event as CustomEvent<{ productName: string }>;
      console.log('🎯 Highlighting product:', customEvent.detail.productName);
      setHighlightedProduct(customEvent.detail.productName);
      setSearchTerm(''); // Clear search to ensure product is visible
      
      // Scroll to product after a brief delay for rendering
      setTimeout(() => {
        const element = document.getElementById(`product-${customEvent.detail.productName}`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
      
      // Clear highlight after 3 seconds
      setTimeout(() => {
        setHighlightedProduct(null);
      }, 3000);
    };

    window.addEventListener('highlight-product', handleHighlightProduct);
    
    return () => {
      window.removeEventListener('highlight-product', handleHighlightProduct);
    };
  }, []);

  const filteredItems = stockItems.filter(item =>
    item.product_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalProducts = filteredItems.length;
  const totalQuantity = filteredItems.reduce((sum, item) => sum + item.current_quantity, 0);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading inventory...</span>
      </div>
    );
  }

  // Show product details if a product is selected
  if (selectedProduct) {
    return (
      <>
        <VoiceAssistantManager />
        <ProductDetails 
          product={selectedProduct} 
          onBack={() => setSelectedProduct(null)} 
        />
      </>
    );
  }

  return (
    <>
      {/* Voice Assistant - floating button + sidebar */}
      <VoiceAssistantManager />

      <div className="space-y-6">
      {/* Header with Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Products</p>
              <p className="text-2xl font-bold text-gray-900">{totalProducts}</p>
            </div>
            <Package className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Stock</p>
              <p className="text-2xl font-bold text-gray-900">{totalQuantity.toFixed(1)}</p>
            </div>
            <TrendingUp className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Last Updated</p>
              <p className="text-sm font-medium text-gray-900">
                {lastRefresh ? lastRefresh.toLocaleTimeString() : 'Never'}
              </p>
            </div>
            <button
              onClick={handleManualRefresh}
              disabled={refreshing}
              className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50"
              title="Refresh stock levels"
            >
              <RefreshCw className={`w-5 h-5 ${refreshing ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white p-4 rounded-lg shadow">
        <input
          type="text"
          placeholder="Search products..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Stock Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Product
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Current Stock
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Unit
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Events
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Updated
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredItems.length === 0 ? (
              <tr>
                <td colSpan={5} className="px-6 py-12 text-center text-gray-500">
                  {searchTerm ? 'No products match your search' : 'No inventory items found'}
                </td>
              </tr>
            ) : (
              filteredItems.map((item) => {
                const isHighlighted = highlightedProduct === item.product_name;
                return (
                <tr
                  key={item.product_name}
                  id={`product-${item.product_name}`}
                  onClick={() => handleProductClick(item.product_name)}
                  className={`transition-all duration-500 cursor-pointer ${
                    isHighlighted 
                      ? 'bg-green-100 ring-2 ring-green-500 shadow-lg' 
                      : 'hover:bg-gray-50'
                  }`}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Package className="w-5 h-5 text-gray-400 mr-2" />
                      <div className="text-sm font-medium text-gray-900">
                        {item.product_name}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className={`text-sm font-semibold ${
                        item.current_quantity > 50 ? 'text-green-600' :
                        item.current_quantity > 20 ? 'text-yellow-600' :
                        'text-red-600'
                      }`}>
                        {item.current_quantity.toFixed(1)}
                      </span>
                      {item.current_quantity > 50 && <TrendingUp className="w-4 h-4 text-green-600 ml-2" />}
                      {item.current_quantity <= 20 && <TrendingDown className="w-4 h-4 text-red-600 ml-2" />}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.unit}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.event_count} events
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(item.last_updated).toLocaleDateString()} {new Date(item.last_updated).toLocaleTimeString()}
                  </td>
                </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>

      {/* Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <p className="text-sm text-blue-800">
          <strong>Real-time Updates:</strong> This view automatically refreshes when inventory events are added via voice or manual entry.
          Stock levels are calculated from all inventory events (receiving, sales, disposal, physical counts).
        </p>
      </div>
    </div>
    </>
  );
}
