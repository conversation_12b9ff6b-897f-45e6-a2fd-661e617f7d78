import { format } from 'date-fns';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from './ui/dialog';
import { formatCurrency, toTitleCase } from '@/lib/type-utils';

export interface InventoryEventDetails {
  id: string;
  created_at: string;
  event_type: string;
  name: string | null;
  quantity: number;
  unit_price: number | null;
  total_amount: number | null;
  notes: string | null;
  metadata: Record<string, unknown> | null;
  product_id: string;
  created_by_voice?: boolean | null;
  voice_confidence_score?: number | null;
  raw_transcript?: string | null;
  audio_recording_url?: string | null;
  // optional convenience field with resolved product name
  productName?: string;
}

interface EventDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  event: InventoryEventDetails | null;
}

export function EventDetailsDialog({ open, onOpenChange, event }: EventDetailsDialogProps) {
  if (!event) return null;

  const createdAt = new Date(event.created_at);
  const unit = String(event.metadata?.unit ?? 'units');
  const batch = (event.metadata?.batch_number ?? event.metadata?.tlc) as string | undefined;
  const productDisplay = event.productName ?? event.name ?? event.product_id ?? 'Unknown Product';
  const voiceScorePercent =
    typeof event.voice_confidence_score === 'number'
      ? Math.round(event.voice_confidence_score * 100)
      : null;

  const kv = (label: string, value: string | number | null | undefined) => (
    <div className="flex items-start justify-between py-1">
      <span className="text-gray-500 dark:text-gray-400">{label}</span>
      <span className="font-medium text-gray-900 dark:text-gray-100 ml-4 text-right break-all">{value ?? '—'}</span>
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-gray-900 dark:text-gray-100">
            {toTitleCase(event.event_type)} • {format(createdAt, 'MMM d, yyyy h:mm a')}
          </DialogTitle>
          <DialogDescription className="text-gray-600 dark:text-gray-300">
            Review full event details
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="rounded-lg border p-3">
            {kv('Product', productDisplay)}
            {kv('Quantity', `${event.quantity} ${unit}`)}
            {kv('Unit Price', event.unit_price != null ? formatCurrency(event.unit_price) : null)}
            {kv('Total', event.total_amount != null ? formatCurrency(event.total_amount) : null)}
            {kv('Batch / TLC', batch ?? null)}
          </div>

          {(Boolean(event.notes) || Boolean(event.raw_transcript)) && (
            <div className="rounded-lg border p-3">
              <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">Notes</div>
              <div className="text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                {event.notes ?? event.raw_transcript}
              </div>
            </div>
          )}

          {(Boolean(event.created_by_voice) ||
            Boolean(event.voice_confidence_score) ||
            Boolean(event.audio_recording_url)) && (
            <div className="rounded-lg border p-3">
              <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">Voice Metadata</div>
              {kv('Created by Voice', event.created_by_voice ? 'Yes' : 'No')}
              {voiceScorePercent != null && kv('Confidence', `${voiceScorePercent}%`)}
              {event.audio_recording_url && (
                <div className="flex items-start justify-between py-1">
                  <span className="text-gray-500 dark:text-gray-400">Audio</span>
                  <a
                    href={event.audio_recording_url}
                    target="_blank"
                    rel="noreferrer"
                    className="text-blue-600 hover:underline ml-4 break-all"
                  >
                    Open recording
                  </a>
                </div>
              )}
            </div>
          )}

          {event.metadata && (
            <div className="rounded-lg border p-3">
              <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">Additional Metadata</div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                {Object.entries(event.metadata)
                  .filter(([k]) => !['unit', 'batch_number', 'tlc'].includes(k))
                  .map(([key, value]) => (
                    <div key={key} className="flex items-start justify-between">
                      <span className="text-gray-500 dark:text-gray-400 mr-2">{toTitleCase(key)}</span>
                      <span className="text-gray-900 dark:text-gray-100 break-all text-right">
                        {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                      </span>
                    </div>
                  ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default EventDetailsDialog;
