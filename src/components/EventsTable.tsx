import { useState, useEffect, useRef, KeyboardEvent } from 'react';
import { format } from 'date-fns';
import { Mic, Volume2, AlertTriangle } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useNavigationContext } from '../contexts/NavigationContext';
import EventDetailsDialog from './EventDetailsDialog';

interface InventoryEvent {
  id: string;
  created_at: string;
  event_type: string;
  name: string | null;
  quantity: number;
  unit_price: number | null;
  total_amount: number | null;
  notes: string | null;
  metadata: Record<string, unknown> | null;
  product_id: string;
  // Voice-specific fields (optional until migration is applied)
  voice_confidence_score?: number | null;
  voice_confidence_breakdown?: Record<string, unknown> | null;
  raw_transcript?: string | null;
  audio_recording_url?: string | null;
  created_by_voice?: boolean | null;
}

interface Product {
  id: string;
  name: string;
}

interface EventsTableProps {
  eventTypeFilter?: string;
  voiceOnly?: boolean;
  limit?: number;
  showTitle?: boolean;
}

export default function EventsTable({
  eventTypeFilter,
  voiceOnly,
  limit = 50,
  showTitle = true,
}: EventsTableProps) {
  const [events, setEvents] = useState<InventoryEvent[]>([]);
  const [products, setProducts] = useState<Record<string, Product>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { viewFilters } = useNavigationContext();
  const [selected, setSelected] = useState<InventoryEvent | null>(null);
  const [selectedProductName, setSelectedProductName] = useState<string | undefined>(undefined);
  const [highlightedEventId, setHighlightedEventId] = useState<string | null>(null);
  const pendingHighlightRef = useRef<string | null>(null);
  const highlightTimeoutRef = useRef<number | null>(null);

  // Use prop filter or context filter - ensure it's a string
  const activeFilter =
    eventTypeFilter ??
    (typeof viewFilters.eventType === 'string' ? viewFilters.eventType : undefined);

  useEffect(() => {
    return () => {
      if (highlightTimeoutRef.current) {
        window.clearTimeout(highlightTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    const fetchEvents = async () => {
      setLoading(true);
      setError(null);
      try {
        // Fetch events - only select columns that exist
        let eventsQuery = supabase
          .from('inventory_events')
          .select(
            `
            id,
            created_at,
            event_type,
            quantity,
            unit_price,
            total_amount,
            notes,
            metadata,
            product_id,
            name
          `
          )
          .order('created_at', { ascending: false });

        // Apply filter if specified
        if (activeFilter) {
          eventsQuery = eventsQuery.eq('event_type', activeFilter);
        }

        // Apply voice filter if specified (only if voice columns exist)
        if (voiceOnly) {
          // Skip voice filter for now - voice columns not migrated yet
          console.warn('Voice filter requested but voice columns not available');
        }

        if (limit) {
          eventsQuery = eventsQuery.limit(limit);
        }

        const { data: eventsData, error: eventsError } = await eventsQuery;

        if (eventsError) throw eventsError;

        // Try to fetch products for fallback, but don't fail if RLS blocks it
        try {
          const { data: productsData } = await supabase.from('Products').select('id, name');

          // Create products lookup
          const productsLookup = Object.fromEntries((productsData ?? []).map((p) => [p.id, p]));
          setProducts(productsLookup);
        } catch (productError) {
          console.warn('Could not fetch products (RLS may be blocking):', productError);
          setProducts({});
        }

        setEvents(eventsData ?? []);
      } catch (err) {
        console.error('Error fetching events:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch events');
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, [activeFilter, voiceOnly, limit]);

  useEffect(() => {
    const handleHighlightEvent = (event: Event) => {
      const customEvent = event as CustomEvent<{ eventId?: string }>;
      const eventId = customEvent.detail?.eventId;
      if (!eventId) return;
      pendingHighlightRef.current = eventId;
      setHighlightedEventId(eventId);
      if (highlightTimeoutRef.current) {
        window.clearTimeout(highlightTimeoutRef.current);
      }
      highlightTimeoutRef.current = window.setTimeout(() => {
        setHighlightedEventId(null);
      }, 10000);
    };

    window.addEventListener('highlight-inventory-event', handleHighlightEvent as EventListener);
    return () => {
      window.removeEventListener('highlight-inventory-event', handleHighlightEvent as EventListener);
    };
  }, []);

  useEffect(() => {
    const pendingEventId = pendingHighlightRef.current;
    if (!pendingEventId) return;
    const match = events.find((evt) => evt.id === pendingEventId);
    if (!match) return;

    setSelected(match);
    const pname = match.name ?? products[match.product_id]?.name;
    setSelectedProductName(pname ?? undefined);
    window.setTimeout(() => {
      document.getElementById(`inventory-event-${pendingEventId}`)?.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }, 100);

    pendingHighlightRef.current = null;
  }, [events, products]);

  const getEventTypeDisplay = (eventType: string) => {
    switch (eventType) {
      case 'receiving':
        return 'Receiving';
      case 'disposal':
        return 'Disposal';
      case 'physical_count':
        return 'Physical Count';
      case 'sale':
        return 'Sale';
      default:
        return eventType;
    }
  };

  const getEventTypeColor = (eventType: string) => {
    switch (eventType) {
      case 'receiving':
        return 'bg-green-100 text-green-800';
      case 'disposal':
        return 'bg-red-100 text-red-800';
      case 'physical_count':
        return 'bg-blue-100 text-blue-800';
      case 'sale':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getVoiceConfidenceIcon = (score: number | null) => {
    if (!score) return null;

    const confidencePercent = Math.round(score * 100);
    if (score >= 0.9) {
      return (
        <div title={`High confidence: ${confidencePercent}%`}>
          <Mic className="w-4 h-4 text-green-600" />
        </div>
      );
    }
    if (score >= 0.7) {
      return (
        <div title={`Medium confidence: ${confidencePercent}%`}>
          <Volume2 className="w-4 h-4 text-yellow-600" />
        </div>
      );
    }
    return (
      <div title={`Low confidence: ${confidencePercent}%`}>
        <AlertTriangle className="w-4 h-4 text-red-600" />
      </div>
    );
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        {showTitle && <h2 className="text-lg font-semibold mb-4">Events</h2>}
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        {showTitle && <h2 className="text-lg font-semibold mb-4">Events</h2>}
        <div className="bg-red-50 border border-red-200 rounded p-4 text-red-700">
          Error loading events: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      {/* Details dialog */}
      <EventDetailsDialog
        open={!!selected}
        onOpenChange={(o) => !o && (setSelected(null), setSelectedProductName(undefined))}
        event={
          selected
            ? {
                ...selected,
                productName:
                  selectedProductName ??
                  products[selected.product_id]?.name ??
                  selected.name ??
                  undefined,
              }
            : null
        }
      />
      {showTitle && (
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">
            {activeFilter ? `${getEventTypeDisplay(activeFilter)} Events` : 'All Events'}
          </h2>
          <span className="text-sm text-gray-500">
            {events.length} {events.length === 1 ? 'event' : 'events'}
          </span>
        </div>
      )}

      {events.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>
            No events found
            {activeFilter ? ` for ${getEventTypeDisplay(activeFilter).toLowerCase()}` : ''}.
          </p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full table-auto">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-2 font-medium text-gray-700">Date</th>
                <th className="text-left py-3 px-2 font-medium text-gray-700">Type</th>
                <th className="text-left py-3 px-2 font-medium text-gray-700">Product</th>
                <th className="text-left py-3 px-2 font-medium text-gray-700">Quantity</th>
                <th className="text-left py-3 px-2 font-medium text-gray-700">Unit Price</th>
                <th className="text-left py-3 px-2 font-medium text-gray-700">Total</th>
                <th className="text-left py-3 px-2 font-medium text-gray-700">Batch/TLC</th>
                <th className="text-left py-3 px-2 font-medium text-gray-700">Voice</th>
                <th className="text-left py-3 px-2 font-medium text-gray-700">Notes</th>
              </tr>
            </thead>
            <tbody>
              {events.map((event) => (
                <tr
                  key={event.id}
                  id={`inventory-event-${event.id}`}
                  className={`border-b border-gray-100 cursor-pointer focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 ${
                    highlightedEventId === event.id
                      ? 'bg-yellow-50 ring-1 ring-yellow-400'
                      : 'hover:bg-gray-50'
                  }`}
                  role="button"
                  tabIndex={0}
                  onClick={() => {
                    setSelected(event);
                    const pname = event.name ?? products[event.product_id]?.name;
                    setSelectedProductName(pname ?? undefined);
                  }}
                  onKeyDown={(e: KeyboardEvent<HTMLTableRowElement>) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      setSelected(event);
                      const pname = event.name ?? products[event.product_id]?.name;
                      setSelectedProductName(pname ?? undefined);
                    }
                  }}
                >
                  <td className="py-3 px-2 text-sm">
                    {format(new Date(event.created_at), 'MMM d, yyyy')}
                    <div className="text-xs text-gray-500">
                      {format(new Date(event.created_at), 'h:mm a')}
                    </div>
                  </td>
                  <td className="py-3 px-2">
                    <span
                      className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getEventTypeColor(event.event_type)}`}
                    >
                      {getEventTypeDisplay(event.event_type)}
                    </span>
                  </td>
                  <td className="py-3 px-2 text-sm">
                    {event.name ?? products[event.product_id]?.name ?? 'Unknown Product'}
                  </td>
                  <td className="py-3 px-2 text-sm">
                    {event.quantity} {String(event.metadata?.unit ?? 'units')}
                  </td>
                  <td className="py-3 px-2 text-sm">
                    {event.unit_price ? `$${event.unit_price.toFixed(2)}` : '-'}
                  </td>
                  <td className="py-3 px-2 text-sm font-medium">
                    {event.total_amount ? `$${event.total_amount.toFixed(2)}` : '-'}
                  </td>
                  <td className="py-3 px-2 text-sm">
                    {(event.metadata?.batch_number ?? event.metadata?.tlc) ? (
                      <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                        {String(event.metadata?.batch_number ?? event.metadata?.tlc ?? '')}
                      </span>
                    ) : (
                      '-'
                    )}
                  </td>
                  <td className="py-3 px-2 text-center">
                    {event.created_by_voice &&
                      event.voice_confidence_score &&
                      getVoiceConfidenceIcon(event.voice_confidence_score)}
                  </td>
                  <td className="py-3 px-2 text-sm text-gray-600 max-w-32 truncate">
                    {event.notes ?? event.raw_transcript ?? '-'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
