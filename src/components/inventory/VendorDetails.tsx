import React, { useState, useEffect } from 'react';
import {
  ArrowLeft,
  Star,
  TrendingUp,

  Package,

  DollarSign,
  Phone,
  Mail,
  MapPin,

  Award,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Progress } from '../ui/progress';
import type { Product, Vendor } from '../../types/schema';
import type { VendorPerformance } from '../../types/inventory';

interface VendorDetailsProps {
  vendorId: string;
  onBack: () => void;
  onProductClick: (product: Product) => void;
}

interface VendorDetailData extends Vendor {
  performance: VendorPerformance;
  recentOrders: Order[];
  products: Product[];
  qualityMetrics: QualityMetrics;
  complianceStatus: ComplianceStatus;
}

interface Order {
  id: string;
  orderDate: string;
  deliveryDate?: string;
  status: 'pending' | 'delivered' | 'delayed' | 'cancelled';
  totalValue: number;
  itemCount: number;
  qualityIssues: boolean;
}

interface QualityMetrics {
  defectRate: number;
  returnRate: number;
  freshnesScore: number;
  temperatureCompliance: number;
  certificationStatus: 'valid' | 'expired' | 'pending';
}

interface ComplianceStatus {
  haccp: boolean;
  gdst: boolean;
  msc: boolean;
  lastAudit: string;
  nextAudit: string;
  violations: number;
}

// Mock data for demonstration - in real app, this would come from API
const getMockVendorData = (vendorId: string): VendorDetailData => ({
  id: vendorId,
  name: 'Ocean Fresh Seafood Co.',
  contact_name: 'Captain John Smith',
  email: '<EMAIL>',
  phone: '+****************',
  address: '1234 Harbor Way, Seattle, WA 98101',
  status: 'active',
  payment_terms: 'Net 30',
  credit_limit: 50000,
  created_at: '2024-01-15T00:00:00Z',
  updated_at: '2024-12-01T00:00:00Z',
  
  performance: {
    rating: 4.2,
    onTimeDelivery: 87,
    qualityScore: 92,
    totalOrders: 156,
    averageOrderValue: 3250,
    lastDelivery: '2024-12-10T00:00:00Z'
  },
  
  recentOrders: [
    {
      id: 'ORD-001',
      orderDate: '2024-12-10T00:00:00Z',
      deliveryDate: '2024-12-12T00:00:00Z',
      status: 'delivered',
      totalValue: 4500,
      itemCount: 3,
      qualityIssues: false
    },
    {
      id: 'ORD-002',
      orderDate: '2024-12-05T00:00:00Z',
      deliveryDate: '2024-12-08T00:00:00Z',
      status: 'delayed',
      totalValue: 2800,
      itemCount: 2,
      qualityIssues: true
    }
  ],
  
  products: [
    {
      id: 'PROD-001',
      name: 'Atlantic Salmon',
      category_id: 'fish-category-id',
      amount: 150,
      condition: 'fresh',
      price: 12.50,
      supplier: 'Ocean Fresh Seafood Co.'
    },
    {
      id: 'PROD-002',
      name: 'King Crab',
      category_id: 'shellfish-category-id',
      amount: 75,
      condition: 'frozen',
      price: 45.00,
      supplier: 'Ocean Fresh Seafood Co.'
    }
  ],
  
  qualityMetrics: {
    defectRate: 2.1,
    returnRate: 1.5,
    freshnesScore: 92,
    temperatureCompliance: 98,
    certificationStatus: 'valid'
  },
  
  complianceStatus: {
    haccp: true,
    gdst: true,
    msc: false,
    lastAudit: '2024-09-15T00:00:00Z',
    nextAudit: '2025-03-15T00:00:00Z',
    violations: 0
  }
});

export default function VendorDetails({ vendorId, onBack, onProductClick }: VendorDetailsProps) {
  const [vendor, setVendor] = useState<VendorDetailData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setVendor(getMockVendorData(vendorId));
      setLoading(false);
    }, 500);
  }, [vendorId]);

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="h-48 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!vendor) {
    return (
      <div className="p-6">
        <button onClick={onBack} className="flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-4">
          <ArrowLeft className="w-4 h-4" />
          Back to Products
        </button>
        <div className="text-center py-12">
          <p className="text-gray-500">Vendor not found</p>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'delayed': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-6 space-y-6">
      <button
        onClick={onBack}
        className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
      >
        <ArrowLeft className="w-4 h-4" />
        Back to Products
      </button>

      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{vendor.name}</h1>
          <div className="flex items-center gap-2 mt-2">
            <Badge className={getStatusColor(vendor.status ?? 'unknown')}>
              {vendor.status?.toUpperCase()}
            </Badge>
            <div className="flex items-center gap-1">
              <Star className="w-4 h-4 text-yellow-500 fill-current" />
              <span className="font-medium">{vendor.performance.rating.toFixed(1)}</span>
              <span className="text-gray-500">({vendor.performance.totalOrders} orders)</span>
            </div>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Phone className="w-4 h-4 mr-2" />
            Call
          </Button>
          <Button variant="outline" size="sm">
            <Mail className="w-4 h-4 mr-2" />
            Email
          </Button>
          <Button size="sm">
            New Order
          </Button>
        </div>
      </div>

      {/* Performance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">On-Time Delivery</p>
                <p className="text-2xl font-bold text-green-600">{vendor.performance.onTimeDelivery}%</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Quality Score</p>
                <p className="text-2xl font-bold text-blue-600">{vendor.performance.qualityScore}%</p>
              </div>
              <Award className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg Order Value</p>
                <p className="text-2xl font-bold text-purple-600">${vendor.performance.averageOrderValue.toLocaleString()}</p>
              </div>
              <DollarSign className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Orders</p>
                <p className="text-2xl font-bold text-gray-900">{vendor.performance.totalOrders}</p>
              </div>
              <Package className="w-8 h-8 text-gray-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="products">Products ({vendor.products.length})</TabsTrigger>
          <TabsTrigger value="orders">Recent Orders</TabsTrigger>
          <TabsTrigger value="quality">Quality Metrics</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Phone className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="font-medium">{vendor.phone}</p>
                    <p className="text-sm text-gray-600">{vendor.contact_name}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Mail className="w-4 h-4 text-gray-500" />
                  <p className="font-medium">{vendor.email}</p>
                </div>
                
                <div className="flex items-center gap-3">
                  <MapPin className="w-4 h-4 text-gray-500" />
                  <p className="font-medium">{vendor.address}</p>
                </div>
              </CardContent>
            </Card>

            {/* Business Terms */}
            <Card>
              <CardHeader>
                <CardTitle>Business Terms</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment Terms</span>
                  <span className="font-medium">{vendor.payment_terms}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">Credit Limit</span>
                  <span className="font-medium">${vendor.credit_limit?.toLocaleString()}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Last Delivery</span>
                  <span className="font-medium">
                    {new Date(vendor.performance.lastDelivery).toLocaleDateString()}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="products" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {vendor.products.map((product) => (
              <Card key={product.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4" onClick={() => onProductClick(product)}>
                  <h3 className="font-semibold text-gray-900">{product.name}</h3>
                  <div className="mt-2 space-y-1 text-sm text-gray-600">
                    <div className="flex justify-between">
                      <span>Category:</span>
                      <span>{product.category_id}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Stock:</span>
                      <span>{product.amount} units</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Price:</span>
                      <span className="font-medium">${product.price?.toFixed(2)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="orders" className="space-y-4">
          <div className="space-y-4">
            {vendor.recentOrders.map((order) => (
              <Card key={order.id}>
                <CardContent className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold">{order.id}</h3>
                      <p className="text-sm text-gray-600 mt-1">
                        Ordered: {new Date(order.orderDate).toLocaleDateString()}
                        {order.deliveryDate && (
                          <span> • Delivered: {new Date(order.deliveryDate).toLocaleDateString()}</span>
                        )}
                      </p>
                    </div>
                    
                    <div className="text-right">
                      <Badge className={getOrderStatusColor(order.status)}>
                        {order.status.toUpperCase()}
                      </Badge>
                      {order.qualityIssues && (
                        <div className="flex items-center gap-1 mt-2 text-red-600">
                          <AlertTriangle className="w-4 h-4" />
                          <span className="text-sm">Quality Issues</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-3 flex justify-between text-sm">
                    <span>{order.itemCount} items</span>
                    <span className="font-medium">${order.totalValue.toLocaleString()}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="quality" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Quality Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between mb-2">
                    <span className="text-sm">Freshness Score</span>
                    <span className="font-medium">{vendor.qualityMetrics.freshnesScore}%</span>
                  </div>
                  <Progress value={vendor.qualityMetrics.freshnesScore} className="h-2" />
                </div>
                
                <div>
                  <div className="flex justify-between mb-2">
                    <span className="text-sm">Temperature Compliance</span>
                    <span className="font-medium">{vendor.qualityMetrics.temperatureCompliance}%</span>
                  </div>
                  <Progress value={vendor.qualityMetrics.temperatureCompliance} className="h-2" />
                </div>
                
                <div className="grid grid-cols-2 gap-4 mt-4">
                  <div>
                    <p className="text-sm text-gray-600">Defect Rate</p>
                    <p className="text-xl font-bold text-red-600">{vendor.qualityMetrics.defectRate}%</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Return Rate</p>
                    <p className="text-xl font-bold text-orange-600">{vendor.qualityMetrics.returnRate}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Compliance Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-3">
                  {vendor.complianceStatus.haccp ? (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  ) : (
                    <AlertTriangle className="w-5 h-5 text-red-600" />
                  )}
                  <div>
                    <p className="font-medium">HACCP</p>
                    <p className="text-sm text-gray-600">
                      {vendor.complianceStatus.haccp ? 'Compliant' : 'Non-compliant'}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  {vendor.complianceStatus.gdst ? (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  ) : (
                    <AlertTriangle className="w-5 h-5 text-red-600" />
                  )}
                  <div>
                    <p className="font-medium">GDST</p>
                    <p className="text-sm text-gray-600">
                      {vendor.complianceStatus.gdst ? 'Compliant' : 'Non-compliant'}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  {vendor.complianceStatus.msc ? (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  ) : (
                    <AlertTriangle className="w-5 h-5 text-orange-600" />
                  )}
                  <div>
                    <p className="font-medium">MSC Certified</p>
                    <p className="text-sm text-gray-600">
                      {vendor.complianceStatus.msc ? 'Certified' : 'Not certified'}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="pt-4 border-t">
                <div className="flex justify-between text-sm">
                  <span>Last Audit:</span>
                  <span>{new Date(vendor.complianceStatus.lastAudit).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between text-sm mt-2">
                  <span>Next Audit:</span>
                  <span>{new Date(vendor.complianceStatus.nextAudit).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between text-sm mt-2">
                  <span>Violations:</span>
                  <span className={vendor.complianceStatus.violations > 0 ? 'text-red-600 font-medium' : 'text-green-600'}>
                    {vendor.complianceStatus.violations}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}