/**
 * Alert Notification Toast Component
 *
 * Displays real-time temperature alert notifications as toast messages
 * with action buttons for immediate response.
 */

import React, { useEffect, useState } from 'react';
import { toast, Toaster } from 'sonner';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { AlertTriangle, Thermometer, CheckCircle, ArrowUp, MapPin, Clock } from 'lucide-react';
import { useTemperatureAlerts } from '../../hooks/useTempStick';
import { alertService } from '../../lib/alert-service';
import type { TemperatureAlert } from '../../types/tempstick';

interface AlertToastProps {
  alert: TemperatureAlert & {
    sensors?: {
      name: string;
      location?: string | null;
      storage_areas?: {
        name: string;
        haccp_control_point: boolean;
      } | null;
    };
  };
  onAcknowledge: () => void;
  onResolve: () => void;
  onEscalate: () => void;
}

function AlertToastContent({ alert, onAcknowledge, onResolve, onEscalate }: AlertToastProps) {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
      case 'emergency':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'warning':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'info':
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const formatAlertType = (type: string) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
  };

  return (
    <div className="w-full max-w-md">
      <div className="flex items-start space-x-3 mb-3">
        <div className={`p-1.5 rounded-full ${getSeverityColor(alert.severity)}`}>
          <AlertTriangle className="h-4 w-4" />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <h3 className="font-semibold text-sm text-gray-900 truncate">
              {formatAlertType(alert.alert_type)}
            </h3>
            <Badge className={`${getSeverityColor(alert.severity)} text-xs`}>
              {alert.severity.toUpperCase()}
            </Badge>
          </div>

          <div className="space-y-1 text-xs text-gray-600">
            {alert.sensors && (
              <>
                <div className="flex items-center">
                  <Thermometer className="h-3 w-3 mr-1" />
                  <span>{alert.sensors.name}</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="h-3 w-3 mr-1" />
                  <span>{alert.sensors.location}</span>
                </div>
              </>
            )}

            {alert.actual_value != null && (
              <div className="flex items-center">
                <span>Temperature: </span>
                <span className="font-medium">{alert.actual_value.toFixed(1)}°F</span>
              </div>
            )}

            <div className="flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              <span>{new Date(alert.created_at).toLocaleTimeString()}</span>
            </div>

            {alert.sensors?.storage_areas?.haccp_control_point && (
              <Badge variant="destructive" className="text-xs">
                HACCP Critical Control Point
              </Badge>
            )}
          </div>
        </div>
      </div>

      <div className="flex space-x-2">
        <Button size="sm" variant="outline" onClick={onAcknowledge} className="flex-1 text-xs h-7">
          <CheckCircle className="h-3 w-3 mr-1" />
          Acknowledge
        </Button>

        <Button size="sm" onClick={onResolve} className="flex-1 text-xs h-7">
          <CheckCircle className="h-3 w-3 mr-1" />
          Resolve
        </Button>

        {(alert.severity === 'critical' || alert.severity === 'emergency') && (
          <Button size="sm" variant="destructive" onClick={onEscalate} className="text-xs h-7">
            <ArrowUp className="h-3 w-3" />
          </Button>
        )}
      </div>
    </div>
  );
}

export function AlertNotificationSystem() {
  const { alerts, refetch } = useTemperatureAlerts(true);
  const [processedAlerts, setProcessedAlerts] = useState<Set<string>>(new Set());

  useEffect(() => {
    // Show toast notifications for new alerts
    alerts.forEach((alert) => {
      if (!processedAlerts.has(alert.id)) {
        const duration =
          alert.severity === 'critical' ? Infinity : alert.severity === 'emergency' ? 15000 : 10000;

        toast.custom(
          (t) => (
            <AlertToastContent
              alert={alert}
              onAcknowledge={async () => {
                await alertService.acknowledgeAlert(alert.id);
                toast.dismiss(t);
                refetch();
              }}
              onResolve={async () => {
                await alertService.resolveAlert(alert.id);
                toast.dismiss(t);
                refetch();
              }}
              onEscalate={async () => {
                await alertService.escalateAlert(alert.id);
                toast.dismiss(t);
              }}
            />
          ),
          {
            id: alert.id,
            duration,
            className:
              alert.severity === 'critical'
                ? 'border-2 border-red-500 shadow-lg'
                : alert.severity === 'high'
                  ? 'border-2 border-orange-400 shadow-lg'
                  : '',
          }
        );

        setProcessedAlerts((prev) => new Set(prev).add(alert.id));
      }
    });

    // Remove processed alerts that no longer exist (resolved)
    const currentAlertIds = new Set(alerts.map((a) => a.id));
    setProcessedAlerts((prev) => {
      const newSet = new Set();
      prev.forEach((id) => {
        if (currentAlertIds.has(id)) {
          newSet.add(id);
        }
      });
      return newSet;
    });
  }, [alerts, processedAlerts, refetch]);

  return (
    <Toaster
      position="top-right"
      expand={true}
      richColors={false}
      closeButton={true}
      toastOptions={{
        className: 'bg-white border border-gray-200',
        style: {
          padding: '12px',
        },
      }}
    />
  );
}

/**
 * Alert Statistics Component
 */
export function AlertStatistics({ timeRange = 'day' }: { timeRange?: 'day' | 'week' | 'month' }) {
  const [stats, setStats] = useState({
    total: 0,
    critical: 0,
    high: 0,
    resolved: 0,
    avgResolutionTime: 0,
    haccpViolations: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadStats = async () => {
      setLoading(true);
      try {
        const statistics = await alertService.getAlertStatistics(timeRange);
        setStats(statistics);
      } catch (error) {
        console.error('Failed to load alert statistics:', error);
      } finally {
        setLoading(false);
      }
    };

    loadStats();
  }, [timeRange]);

  if (loading) {
    return <div className="animate-pulse">Loading statistics...</div>;
  }

  const resolutionRate = stats.total > 0 ? ((stats.resolved / stats.total) * 100).toFixed(1) : '0';
  const avgTimeHours = (stats.avgResolutionTime / 60).toFixed(1);

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
      <div className="bg-white p-4 rounded-lg border">
        <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
        <div className="text-sm text-gray-600">Total Alerts</div>
      </div>

      <div className="bg-white p-4 rounded-lg border">
        <div className="text-2xl font-bold text-red-600">{stats.critical}</div>
        <div className="text-sm text-gray-600">Critical</div>
      </div>

      <div className="bg-white p-4 rounded-lg border">
        <div className="text-2xl font-bold text-orange-600">{stats.high}</div>
        <div className="text-sm text-gray-600">High Priority</div>
      </div>

      <div className="bg-white p-4 rounded-lg border">
        <div className="text-2xl font-bold text-green-600">{resolutionRate}%</div>
        <div className="text-sm text-gray-600">Resolved</div>
      </div>

      <div className="bg-white p-4 rounded-lg border">
        <div className="text-2xl font-bold text-blue-600">{avgTimeHours}h</div>
        <div className="text-sm text-gray-600">Avg Resolution</div>
      </div>

      <div className="bg-white p-4 rounded-lg border">
        <div className="text-2xl font-bold text-purple-600">{stats.haccpViolations}</div>
        <div className="text-sm text-gray-600">HACCP Violations</div>
      </div>
    </div>
  );
}
