/**
 * Enhanced Time Series Chart Component
 *
 * Provides interactive temperature visualization with:
 * - Continuous 24-hour time-series data display
 * - Multiple sensor support with different colored lines
 * - Interactive hover tooltips with exact values and timestamps
 * - Zoom and pan functionality
 * - Time bucketing for performance (15-min/hourly intervals)
 * - Auto-refresh for new data
 * - Responsive design for different screen sizes
 * - Proper loading states and error handling
 */

import React, { useState, useMemo, useCallback, useEffect } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Brush,
  ReferenceLine,
  Legend,
  TooltipProps,
} from 'recharts';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  RotateCcw,
  ZoomIn,
  ZoomOut,
  Eye,
  EyeOff,
  TrendingUp,
  AlertTriangle,
  Thermometer,
  RefreshCw,
} from 'lucide-react';
import { useCurrentUnit } from '@/contexts/UnitContext';
import { displayTemp, formatTemp } from '@/lib/units';

// Color palette for multiple sensors (up to 8 distinct colors)
const SENSOR_COLORS = [
  '#2563eb', // blue-600
  '#dc2626', // red-600
  '#16a34a', // green-600
  '#ca8a04', // yellow-600
  '#7c3aed', // violet-600
  '#ea580c', // orange-600
  '#0891b2', // cyan-600
  '#be123c', // rose-600
];

// Temperature threshold lines for different storage types
const TEMP_THRESHOLDS = {
  freezer: { min: -10, max: 10, label: 'Freezer Range', color: '#3b82f6' },
  refrigerator: { min: 32, max: 40, label: 'Refrigerator Range', color: '#10b981' },
  danger: { min: 41, max: 140, label: 'Danger Zone', color: '#ef4444' },
};

export interface TimeSeriesDataPoint {
  timestamp: number;
  timestampISO: string;
  timeFormatted: string;
  [sensorId: string]: number | string;
}

export interface SensorInfo {
  id: string;
  name: string;
  isOnline: boolean;
  currentTemp?: number;
  lastReading?: Date;
}

export interface EnhancedTimeSeriesChartProps {
  /** Raw sensor data points */
  data: Array<{
    sensorId: string;
    sensorName?: string;
    temperature: number; // in Celsius
    humidity?: number;
    timestamp: Date;
    batteryLevel?: number;
    signalStrength?: number;
  }>;
  /** Sensor information for display */
  sensors?: SensorInfo[];
  /** Chart height in pixels */
  height?: number;
  /** Time range for data display */
  timeRange?: '1h' | '6h' | '24h' | '7d' | '30d';
  /** Show brush for zooming */
  showBrush?: boolean;
  /** Show temperature thresholds */
  showThresholds?: boolean;
  /** Show violation markers */
  showViolations?: boolean;
  /** Enable zoom controls */
  enableZoom?: boolean;
  /** Auto-refresh interval in seconds (0 to disable) */
  autoRefreshInterval?: number;
  /** Callback for manual refresh */
  onRefresh?: () => void;
  /** Loading state */
  isLoading?: boolean;
  /** Error state */
  error?: string | null;
  /** Additional CSS classes */
  className?: string;
}

export const EnhancedTimeSeriesChart: React.FC<EnhancedTimeSeriesChartProps> = ({
  data = [],
  sensors = [],
  height = 400,
  timeRange = '24h',
  showBrush = true,
  showThresholds = true,
  showViolations = true,
  enableZoom = true,
  autoRefreshInterval = 30,
  onRefresh,
  isLoading = false,
  error = null,
  className = '',
}) => {
  const currentUnit = useCurrentUnit();

  // Chart state
  const [zoomDomain, setZoomDomain] = useState<[number, number] | null>(null);
  const [visibleSensors, setVisibleSensors] = useState<Set<string>>(new Set());
  const [chartType, setChartType] = useState<'line' | 'step'>('line');
  const [showDataPoints, setShowDataPoints] = useState(false);
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Time bucketing configuration based on range
  const bucketConfig = useMemo(() => {
    const configs = {
      '1h': { interval: 2 * 60 * 1000, format: 'HH:mm' }, // 2 minutes
      '6h': { interval: 10 * 60 * 1000, format: 'HH:mm' }, // 10 minutes
      '24h': { interval: 15 * 60 * 1000, format: 'HH:mm' }, // 15 minutes
      '7d': { interval: 60 * 60 * 1000, format: 'MM/dd HH:mm' }, // 1 hour
      '30d': { interval: 4 * 60 * 60 * 1000, format: 'MM/dd' }, // 4 hours
    };
    return configs[timeRange] ?? configs['24h'];
  }, [timeRange]);

  // Process and bucket data for chart display
  const { chartData, sensorIds, violationPoints, dataStats } = useMemo(() => {
    if (!data.length) return { chartData: [], sensorIds: [], violationPoints: [], dataStats: null };

    // Get unique sensor IDs from data
    const uniqueSensorIds = Array.from(new Set(data.map((p) => p.sensorId)));

    // Time bucketing: group data by time intervals
    const buckets = new Map<number, Record<string, number | string>>();

    data.forEach((point) => {
      const timestamp = point.timestamp.getTime();
      const bucketTime = Math.floor(timestamp / bucketConfig.interval) * bucketConfig.interval;

      if (!buckets.has(bucketTime)) {
        buckets.set(bucketTime, {
          timestamp: bucketTime,
          timestampISO: new Date(bucketTime).toISOString(),
          timeFormatted: new Date(bucketTime).toLocaleString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            ...(timeRange === '7d' || timeRange === '30d'
              ? { month: 'short', day: 'numeric' }
              : {}),
          }),
        });
      }

      const bucket = buckets.get(bucketTime);
      if (!bucket) return;

      // Convert temperature to current unit and store
      const tempInUnit = displayTemp(point.temperature, currentUnit);

      // For multiple readings in the same bucket, take the average
      const existingTemp = bucket[point.sensorId] as number;
      if (typeof existingTemp === 'number') {
        bucket[point.sensorId] = (existingTemp + tempInUnit) / 2;
      } else {
        bucket[point.sensorId] = tempInUnit;
      }
    });

    // Convert to array and sort by timestamp
    const processedData = Array.from(buckets.values()).sort(
      (a, b) => (a.timestamp as number) - (b.timestamp as number)
    );

    // Find violation points (temperatures outside safe ranges)
    const violations = data
      .filter((point) => {
        const tempF =
          currentUnit === 'celsius' ? (point.temperature * 9) / 5 + 32 : point.temperature;
        return tempF < 32 || tempF > 45; // Basic food safety range
      })
      .map((point) => ({
        timestamp: point.timestamp.getTime(),
        temperature: displayTemp(point.temperature, currentUnit),
        sensorId: point.sensorId,
        sensorName: point.sensorName ?? point.sensorId,
      }));

    // Calculate data statistics
    const temps = data.map((d) => displayTemp(d.temperature, currentUnit));
    const stats =
      temps.length > 0
        ? {
            min: Math.min(...temps),
            max: Math.max(...temps),
            avg: temps.reduce((sum, t) => sum + t, 0) / temps.length,
            count: temps.length,
            timeSpan:
              data.length > 0
                ? {
                    start: new Date(Math.min(...data.map((d) => d.timestamp.getTime()))),
                    end: new Date(Math.max(...data.map((d) => d.timestamp.getTime()))),
                  }
                : null,
          }
        : null;

    return {
      chartData: processedData,
      sensorIds: uniqueSensorIds,
      violationPoints: violations,
      dataStats: stats,
    };
  }, [data, bucketConfig, currentUnit, timeRange]);

  // Initialize visible sensors when data changes
  useEffect(() => {
    if (sensorIds.length > 0 && visibleSensors.size === 0) {
      setVisibleSensors(new Set(sensorIds));
    }
  }, [sensorIds, visibleSensors.size]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefreshEnabled || !onRefresh || autoRefreshInterval <= 0) return;

    const interval = setInterval(() => {
      onRefresh();
      setLastRefresh(new Date());
    }, autoRefreshInterval * 1000);

    return () => clearInterval(interval);
  }, [autoRefreshEnabled, onRefresh, autoRefreshInterval]);

  // Chart interaction handlers
  const handleZoomIn = useCallback(() => {
    if (!chartData.length) return;
    const dataLength = chartData.length;
    const currentStart = zoomDomain?.[0] ?? 0;
    const currentEnd = zoomDomain?.[1] ?? dataLength - 1;
    const range = currentEnd - currentStart;
    const newRange = Math.max(Math.floor(range * 0.7), 10);
    const center = Math.floor((currentStart + currentEnd) / 2);
    const newStart = Math.max(0, center - Math.floor(newRange / 2));
    const newEnd = Math.min(dataLength - 1, newStart + newRange);
    setZoomDomain([newStart, newEnd]);
  }, [chartData.length, zoomDomain]);

  const handleZoomOut = useCallback(() => {
    if (!chartData.length) return;
    const dataLength = chartData.length;
    const currentStart = zoomDomain?.[0] ?? 0;
    const currentEnd = zoomDomain?.[1] ?? dataLength - 1;
    const range = currentEnd - currentStart;
    const newRange = Math.min(Math.ceil(range * 1.4), dataLength);
    const center = Math.floor((currentStart + currentEnd) / 2);
    const newStart = Math.max(0, center - Math.floor(newRange / 2));
    const newEnd = Math.min(dataLength - 1, newStart + newRange);
    if (newStart === 0 && newEnd === dataLength - 1) {
      setZoomDomain(null);
    } else {
      setZoomDomain([newStart, newEnd]);
    }
  }, [chartData.length, zoomDomain]);

  const handleResetZoom = useCallback(() => {
    setZoomDomain(null);
  }, []);

  const handleBrushChange = useCallback(
    (brushData: { startIndex?: number; endIndex?: number } | null) => {
      if (brushData?.startIndex !== undefined && brushData.endIndex !== undefined) {
        setZoomDomain([brushData.startIndex, brushData.endIndex]);
      }
    },
    []
  );

  const toggleSensorVisibility = useCallback((sensorId: string) => {
    setVisibleSensors((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(sensorId)) {
        newSet.delete(sensorId);
      } else {
        newSet.add(sensorId);
      }
      return newSet;
    });
  }, []);

  const handleManualRefresh = useCallback(() => {
    if (onRefresh) {
      onRefresh();
      setLastRefresh(new Date());
    }
  }, [onRefresh]);

  // Custom tooltip component
  const CustomTooltip: React.FC<TooltipProps<number, string>> = ({ active, payload, label }) => {
    if (!active || !payload?.length) return null;

    const timestamp = new Date(label as number);

    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg max-w-xs">
        <div className="text-sm font-medium text-gray-900 mb-2">{timestamp.toLocaleString()}</div>
        <div className="space-y-1">
          {payload.map((entry, index) => {
            if (entry.value === null || entry.value === undefined) return null;

            const sensorName = sensors.find((s) => s.id === entry.dataKey)?.name ?? entry.dataKey;
            const isViolation =
              showViolations &&
              violationPoints.some(
                (v) => v.timestamp === (label as number) && v.sensorId === entry.dataKey
              );

            return (
              <div key={index} className="flex items-center justify-between space-x-3">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: entry.color }} />
                  <span className="text-sm text-gray-700 truncate">{sensorName}</span>
                  {isViolation && <AlertTriangle className="h-3 w-3 text-red-500" />}
                </div>
                <span className="text-sm font-mono text-gray-900">
                  {formatTemp(Number(entry.value), currentUnit)}
                </span>
              </div>
            );
          })}
        </div>
        {payload.length > 1 && (
          <div className="mt-2 pt-2 border-t text-xs text-gray-500">{payload.length} sensors</div>
        )}
      </div>
    );
  };

  // Loading state - only show spinner when there is no data yet
  if (isLoading && (!data || data.length === 0)) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="flex flex-col items-center space-y-2">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
              <div className="text-gray-500">Loading temperature data...</div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-center h-64 text-center">
            <AlertTriangle className="h-12 w-12 mb-4 text-red-500" />
            <div className="text-lg font-medium mb-2 text-red-600">Error Loading Data</div>
            <div className="text-sm text-gray-600 mb-4">{error}</div>
            {onRefresh && (
              <Button onClick={handleManualRefresh} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Empty state
  if (!data.length) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-center h-64 text-gray-500">
            <Thermometer className="h-12 w-12 mb-4 text-gray-400" />
            <div className="text-lg font-medium mb-2">No Temperature Data</div>
            <div className="text-sm text-center mb-4">
              No temperature readings found for the selected time range.
              <br />
              Try expanding the time range or refreshing the data.
            </div>
            {onRefresh && (
              <Button onClick={handleManualRefresh} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Data
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex flex-col space-y-4">
          {/* Title and Status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <CardTitle>Temperature Trends</CardTitle>
              <Badge variant="secondary">{chartData.length} data points</Badge>
              {violationPoints.length > 0 && showViolations && (
                <Badge variant="destructive">{violationPoints.length} violations</Badge>
              )}
            </div>

            {/* Chart Controls */}
            <div className="flex items-center space-x-2">
              {enableZoom && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleZoomIn}
                    disabled={!chartData.length}
                  >
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleZoomOut}
                    disabled={!chartData.length}
                  >
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetZoom}
                    disabled={!zoomDomain}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </>
              )}

              <Select
                value={chartType}
                onValueChange={(value: 'line' | 'step') => setChartType(value)}
              >
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="line">Line</SelectItem>
                  <SelectItem value="step">Step</SelectItem>
                </SelectContent>
              </Select>

              {onRefresh && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleManualRefresh}
                  disabled={isLoading}
                >
                  <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                </Button>
              )}
            </div>
          </div>

          {/* Data Statistics */}
          {dataStats && (
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
              <span>
                Range: {formatTemp(dataStats.min, currentUnit)} -{' '}
                {formatTemp(dataStats.max, currentUnit)}
              </span>
              <span>Average: {formatTemp(dataStats.avg, currentUnit)}</span>
              <span>Total readings: {dataStats.count}</span>
              {dataStats.timeSpan && (
                <span>
                  Span: {dataStats.timeSpan.start.toLocaleString()} -{' '}
                  {dataStats.timeSpan.end.toLocaleString()}
                </span>
              )}
            </div>
          )}

          {/* Controls Row */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="show-points"
                  checked={showDataPoints}
                  onCheckedChange={setShowDataPoints}
                />
                <Label htmlFor="show-points" className="text-sm">
                  Show data points
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="auto-refresh"
                  checked={autoRefreshEnabled}
                  onCheckedChange={setAutoRefreshEnabled}
                />
                <Label htmlFor="auto-refresh" className="text-sm">
                  Auto-refresh
                </Label>
                {autoRefreshEnabled && (
                  <span className="text-xs text-gray-500">({autoRefreshInterval}s)</span>
                )}
              </div>

              {showThresholds && (
                <Badge variant="outline" className="text-xs">
                  Thresholds enabled
                </Badge>
              )}

              {zoomDomain && (
                <Badge variant="secondary" className="text-xs">
                  Zoomed view
                </Badge>
              )}
            </div>

            <div className="text-xs text-gray-500">
              Last refresh: {lastRefresh.toLocaleTimeString()}
            </div>
          </div>

          {/* Sensor Toggle Buttons */}
          <div className="flex flex-wrap gap-2">
            {sensorIds.map((sensorId, index) => {
              const sensor = sensors.find((s) => s.id === sensorId);
              const sensorName = sensor?.name ?? sensorId;
              const color = SENSOR_COLORS[index % SENSOR_COLORS.length];
              const isVisible = visibleSensors.has(sensorId);
              const isOnline = sensor?.isOnline ?? true;

              return (
                <Button
                  key={sensorId}
                  variant={isVisible ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => toggleSensorVisibility(sensorId)}
                  className="flex items-center space-x-2"
                  disabled={!isOnline}
                >
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: isVisible ? 'white' : color }}
                  />
                  {isVisible ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
                  <span className="text-xs truncate max-w-20">{sensorName}</span>
                  {!isOnline && <span className="text-xs opacity-60">(offline)</span>}
                </Button>
              );
            })}
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-6">
        {/* Main Chart */}
        <div style={{ height: `${height}px` }}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={chartData}
              margin={{ top: 5, right: 30, left: 20, bottom: showBrush ? 100 : 20 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />

              <XAxis
                dataKey="timestamp"
                type="number"
                scale="time"
                domain={zoomDomain ? ['dataMin', 'dataMax'] : ['dataMin', 'dataMax']}
                tickFormatter={(value) =>
                  new Date(value).toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    ...(timeRange === '7d' || timeRange === '30d'
                      ? { month: 'short', day: 'numeric' }
                      : {}),
                  })
                }
                stroke="#666"
                fontSize={12}
              />

              <YAxis
                domain={['dataMin - 5', 'dataMax + 5']}
                tickFormatter={(value) => formatTemp(value, currentUnit)}
                stroke="#666"
                fontSize={12}
              />

              <Tooltip content={<CustomTooltip />} />
              <Legend />

              {/* Temperature threshold lines */}
              {showThresholds && (
                <>
                  <ReferenceLine
                    y={displayTemp(0, currentUnit)} // Freezing point
                    stroke={TEMP_THRESHOLDS.freezer.color}
                    strokeDasharray="5 5"
                    label="Freezing"
                  />
                  <ReferenceLine
                    y={displayTemp(4, currentUnit)} // Max safe refrigerator temp
                    stroke={TEMP_THRESHOLDS.refrigerator.color}
                    strokeDasharray="5 5"
                    label="Max Safe"
                  />
                  <ReferenceLine
                    y={displayTemp(60, currentUnit)} // Danger zone start
                    stroke={TEMP_THRESHOLDS.danger.color}
                    strokeDasharray="5 5"
                    label="Danger Zone"
                  />
                </>
              )}

              {/* Temperature lines for each visible sensor */}
              {sensorIds.map((sensorId, index) => {
                if (!visibleSensors.has(sensorId)) return null;

                const color = SENSOR_COLORS[index % SENSOR_COLORS.length];
                const sensor = sensors.find((s) => s.id === sensorId);
                const sensorName = sensor?.name ?? sensorId;

                return (
                  <Line
                    key={sensorId}
                    type={chartType === 'step' ? 'stepAfter' : 'monotone'}
                    dataKey={sensorId}
                    stroke={color}
                    strokeWidth={2}
                    dot={showDataPoints ? { fill: color, r: 3 } : false}
                    activeDot={{ r: 6, stroke: color, strokeWidth: 2, fill: 'white' }}
                    name={sensorName}
                    connectNulls={true}
                  />
                );
              })}

              {/* Brush for zooming */}
              {showBrush && chartData.length > 10 && (
                <Brush
                  dataKey="timestamp"
                  height={60}
                  stroke="#8884d8"
                  tickFormatter={(value) =>
                    new Date(value).toLocaleTimeString('en-US', {
                      hour: '2-digit',
                      minute: '2-digit',
                    })
                  }
                  onChange={handleBrushChange}
                />
              )}
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};

export default EnhancedTimeSeriesChart;
