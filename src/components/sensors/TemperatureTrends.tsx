/**
 * Temperature Trends Chart Component
 *
 * Displays interactive temperature and humidity trends over time
 * with alert indicators and sensor selection capabilities.
 */

import React, { useState, useMemo } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Dot,
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Droplets, AlertTriangle, TrendingUp } from 'lucide-react';
import { useTemperatureTrends, useSensors } from '../../hooks/useTempStick';
import type { TimeRange } from '../../types/tempstick';

interface TemperatureTrendsProps {
  sensorIds?: string[];
  className?: string;
}

interface ChartDataPoint {
  timestamp: number;
  time: string;
  [key: string]: string | number;
}

interface AlertDotProps {
  cx?: number;
  cy?: number;
  payload?: ChartDataPoint;
  dataKey?: string;
}

interface TooltipPayload {
  dataKey: string;
  value: number;
  color: string;
  payload: ChartDataPoint;
}

interface TooltipProps {
  active?: boolean;
  payload?: TooltipPayload[];
  label?: string;
}

const timeRangeOptions = [
  { value: '1h' as TimeRange, label: 'Last Hour' },
  { value: '6h' as TimeRange, label: 'Last 6 Hours' },
  { value: '24h' as TimeRange, label: 'Last 24 Hours' },
  { value: '7d' as TimeRange, label: 'Last 7 Days' },
  { value: '30d' as TimeRange, label: 'Last 30 Days' },
] as const;

const sensorColors = [
  '#3b82f6', // blue
  '#ef4444', // red
  '#10b981', // green
  '#f59e0b', // yellow
  '#8b5cf6', // purple
  '#ec4899', // pink
  '#06b6d4', // cyan
  '#84cc16', // lime
] as const;

export function TemperatureTrends({ sensorIds, className }: TemperatureTrendsProps) {
  const [timeRange, setTimeRange] = useState<TimeRange>('24h');
  const [showHumidity, setShowHumidity] = useState(true);
  const [showAlerts, setShowAlerts] = useState(true);

  const { sensors } = useSensors();
  const trendData = useTemperatureTrends(sensorIds, timeRange);

  // Process data for chart with time bucketing
  const chartData = useMemo(() => {
    const dataMap = new Map<string, ChartDataPoint>();

    // Time bucket size based on range (in milliseconds)
    const bucketSize = timeRange === '1h' ? 5 * 60 * 1000 // 5 minutes
      : timeRange === '6h' ? 15 * 60 * 1000 // 15 minutes
      : timeRange === '24h' ? 30 * 60 * 1000 // 30 minutes
      : timeRange === '7d' ? 2 * 60 * 60 * 1000 // 2 hours
      : 4 * 60 * 60 * 1000; // 4 hours for 30d

    trendData.forEach((point) => {
      const timestamp = new Date(point.timestamp).getTime();
      // Round timestamp to bucket
      const bucketedTimestamp = Math.floor(timestamp / bucketSize) * bucketSize;
      const key = bucketedTimestamp.toString();

      if (!dataMap.has(key)) {
        dataMap.set(key, {
          timestamp: bucketedTimestamp,
          time: new Date(bucketedTimestamp).toLocaleTimeString('en-US', {
            hour: timeRange.includes('d') ? 'numeric' : '2-digit',
            minute: timeRange.includes('d') ? undefined : '2-digit',
            month: timeRange.includes('d') ? 'short' : undefined,
            day: timeRange.includes('d') ? 'numeric' : undefined,
          }),
        });
      }

      const entry = dataMap.get(key)!;
      // Average values if multiple readings in same bucket
      const tempKey = `temp_${point.sensorId}`;
      const existingTemp = entry[tempKey] as number | undefined;
      entry[tempKey] = existingTemp ? (existingTemp + point.temperature) / 2 : point.temperature;

      if (point.humidity !== undefined) {
        const humidityKey = `humidity_${point.sensorId}`;
        const existingHumidity = entry[humidityKey] as number | undefined;
        entry[humidityKey] = existingHumidity ? (existingHumidity + point.humidity) / 2 : point.humidity;
      }

      entry[`alert_${point.sensorId}`] = point.alertLevel !== 'normal';
      entry[`alertLevel_${point.sensorId}`] = point.alertLevel;
    });

    return Array.from(dataMap.values()).sort((a, b) => a.timestamp - b.timestamp);
  }, [trendData, timeRange]);

  const activeSensors = useMemo(() => {
    const sensorMap = new Map(sensors.map((s) => [s.id, s]));
    const activeSensorIds = sensorIds ?? sensors.map((s) => s.id);
    return activeSensorIds.map((id) => sensorMap.get(id)).filter(Boolean);
  }, [sensors, sensorIds]);

  const temperatureRange = useMemo(() => {
    const temps = trendData.map((d) => d.temperature);
    if (temps.length === 0) return null;

    const min = Math.min(...temps);
    const max = Math.max(...temps);
    const buffer = (max - min) * 0.1;

    return {
      min: min - buffer,
      max: max + buffer,
    };
  }, [trendData]);

  const humidityRange = useMemo(() => {
    const humidities = trendData.map((d) => d.humidity).filter((h) => h !== undefined) as number[];
    if (humidities.length === 0) return null;

    return {
      min: Math.max(0, Math.min(...humidities) - 5),
      max: Math.min(100, Math.max(...humidities) + 5),
    };
  }, [trendData]);

  // Custom dot component for alert indicators
  const AlertDot = (props: AlertDotProps) => {
    const { cx, cy, payload, dataKey } = props;
    if (!dataKey || !payload) return null;

    const sensorId = dataKey.split('_')[1];
    const alertLevel = payload[`alertLevel_${sensorId}`];

    if (!showAlerts || alertLevel === 'normal') return null;

    const color = alertLevel === 'critical' ? '#dc2626' : '#f59e0b';

    return <Dot cx={cx} cy={cy} r={4} fill={color} stroke="#fff" strokeWidth={2} />;
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: TooltipProps) => {
    if (!active || !payload || payload.length === 0) return null;

    const timeValue = payload[0]?.payload?.timestamp;
    const time = timeValue ? new Date(timeValue).toLocaleString() : label;

    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 mb-2">{time}</p>
        <div className="space-y-1">
          {payload.map((entry: TooltipPayload, index: number) => {
            const sensorId = entry.dataKey.split('_')[1];
            const sensor = sensors.find((s) => s.id === sensorId);
            const isTemp = entry.dataKey.startsWith('temp_');
            const isHumidity = entry.dataKey.startsWith('humidity_');

            if (!isTemp && !isHumidity) return null;

            return (
              <div key={index} className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full" style={{ backgroundColor: entry.color }} />
                <span className="text-sm text-gray-600">
                  {sensor?.name ?? `Sensor ${sensorId}`}:
                </span>
                <span className="text-sm font-medium">
                  {isTemp && `${entry.value.toFixed(1)}°F`}
                  {isHumidity && `${entry.value.toFixed(1)}%`}
                </span>
                {isTemp && entry.payload[`alert_${sensorId}`] && (
                  <AlertTriangle className="h-3 w-3 text-red-500" />
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  if (trendData.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <TrendingUp className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Temperature Data</h3>
          <p className="text-gray-600">
            Temperature trends will appear here once sensor data is available.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Temperature Trends</span>
          </CardTitle>

          <div className="flex flex-wrap items-center gap-2">
            <Select value={timeRange} onValueChange={(value) => setTimeRange(value as TimeRange)}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {timeRangeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button
              variant={showHumidity ? 'default' : 'outline'}
              size="sm"
              onClick={() => setShowHumidity(!showHumidity)}
              className="flex items-center space-x-1"
            >
              <Droplets className="h-4 w-4" />
              <span>Humidity</span>
            </Button>

            <Button
              variant={showAlerts ? 'default' : 'outline'}
              size="sm"
              onClick={() => setShowAlerts(!showAlerts)}
              className="flex items-center space-x-1"
            >
              <AlertTriangle className="h-4 w-4" />
              <span>Alerts</span>
            </Button>
          </div>
        </div>

        {/* Sensor Legend */}
        {activeSensors.length > 1 && (
          <div className="flex flex-wrap gap-2 mt-4">
            {activeSensors.map((sensor, index) => (
              <Badge key={sensor.id} variant="secondary" className="flex items-center space-x-1">
                <div
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: sensorColors[index % sensorColors.length] }}
                />
                <span>{sensor.name}</span>
              </Badge>
            ))}
          </div>
        )}
      </CardHeader>

      <CardContent>
        <div className="h-96">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
              <XAxis dataKey="time" stroke="#64748b" tick={{ fontSize: 12 }} />

              {/* Temperature Y-Axis */}
              <YAxis
                yAxisId="temp"
                stroke="#64748b"
                tick={{ fontSize: 12 }}
                domain={
                  temperatureRange ? [temperatureRange.min, temperatureRange.max] : ['auto', 'auto']
                }
                label={{ value: 'Temperature (°F)', angle: -90, position: 'insideLeft' }}
              />

              {/* Humidity Y-Axis */}
              {showHumidity && (
                <YAxis
                  yAxisId="humidity"
                  orientation="right"
                  stroke="#64748b"
                  tick={{ fontSize: 12 }}
                  domain={humidityRange ? [humidityRange.min, humidityRange.max] : [0, 100]}
                  label={{ value: 'Humidity (%)', angle: 90, position: 'insideRight' }}
                />
              )}

              <Tooltip content={<CustomTooltip />} />

              {/* Temperature Lines */}
              {activeSensors.map((sensor, index) => {
                const color = sensorColors[index % sensorColors.length];
                return (
                  <Line
                    key={`temp_${sensor.id}`}
                    yAxisId="temp"
                    type="monotone"
                    dataKey={`temp_${sensor.id}`}
                    stroke={color}
                    strokeWidth={3}
                    dot={{ r: 5, fill: color, strokeWidth: 2, stroke: '#fff' }}
                    activeDot={{ r: 7, fill: color, strokeWidth: 2, stroke: '#fff' }}
                    connectNulls={true}
                    name={`${sensor.name} Temperature`}
                    isAnimationActive={false}
                  />
                );
              })}

              {/* Humidity Lines */}
              {showHumidity &&
                activeSensors.map((sensor, index) => (
                  <Line
                    key={`humidity_${sensor.id}`}
                    yAxisId="humidity"
                    type="monotone"
                    dataKey={`humidity_${sensor.id}`}
                    stroke={sensorColors[index % sensorColors.length]}
                    strokeWidth={1}
                    strokeDasharray="5 5"
                    dot={false}
                    connectNulls={true}
                    name={`${sensor.name} Humidity`}
                  />
                ))}

              {/* Alert Indicators */}
              {showAlerts &&
                activeSensors.map((sensor) => (
                  <Line
                    key={`alert_${sensor.id}`}
                    yAxisId="temp"
                    type="monotone"
                    dataKey={`temp_${sensor.id}`}
                    stroke="transparent"
                    dot={<AlertDot />}
                    connectNulls={true}
                  />
                ))}
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
