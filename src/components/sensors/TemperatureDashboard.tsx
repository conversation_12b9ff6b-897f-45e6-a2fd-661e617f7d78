/**
 * Real-time Temperature Dashboard Component
 *
 * Provides comprehensive temperature monitoring with:
 * - Real-time sensor status display
 * - Interactive temperature trend charts
 * - Sensor health indicators
 * - Responsive design for mobile and tablet
 * - Auto-refresh functionality
 * - Dashboard filters and controls
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { logWarn, logError, logDebug } from '@/lib/debug-utils';
import { useSensorStatuses } from '@/hooks/useSensorStatuses';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  CartesianGrid,
  Legend,
} from 'recharts';
import { Wifi, AlertTriangle, RefreshCw, Thermometer } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { manualSyncService } from '@/lib/manual-sync-service';
import { useAuth } from '@/hooks/useAuth';
// Import new temperature components
import { UnitToggle, CombinedStatsCards, ChartRow, PdfButton } from '@/components/temperature';
import { useCurrentUnit } from '@/contexts/UnitContext';
import { displayTemp, formatTemp, round1 } from '@/lib/units';
import { calculateStats, type RawDataPoint, type StatsSummary } from '@/lib/stats';
import type { SensorMetadata } from '@/lib/export/pdf';
import MultiFileCSVImportDialog from '@/components/csv/MultiFileCSVImportDialog';
import AuthDebugHelper from '@/components/AuthDebugHelper';

// Import enhanced chart components
import { EnhancedTimeSeriesChart } from './charts/EnhancedTimeSeriesChart';
import TempStickSyncStatus from './TempStickSyncStatus';
import { useTimeSeriesData } from '@/hooks/useTimeSeriesData';
import type { SensorMetadata as TimeSeriesSensorMetadata } from '@/hooks/useTimeSeriesData';

// Import sensor types from the hook
interface SensorAlert {
  id: string;
  message: string;
  severity: 'warning' | 'critical';
  timestamp: string;
}

interface SensorStatus {
  sensor: {
    id: string;
    internalId: string; // Add internal UUID for unique React keys
    name: string;
    location: string;
    type: string;
  };
  latestReading?: {
    temperature: number;
    humidity?: number;
    timestamp: string;
  };
  activeAlerts: SensorAlert[];
  isOnline: boolean;
  lastUpdated: string;
}
import { useSensorHistory } from '@/hooks/useSensorHistory';
import { useMultiSensorHistory } from '@/hooks/useMultiSensorHistory';

// Dashboard props interface
interface DashboardProps {
  className?: string;
  showFilters?: boolean;
  _autoRefreshInterval?: number;
  compactMode?: boolean;
}

export const TemperatureDashboard: React.FC<DashboardProps> = ({
  className = '',
  showFilters = true,
  _autoRefreshInterval = 30000,
  compactMode = false,
}) => {
  // State management
  const [selectedSensorId, setSelectedSensorId] = useState<string>('all');
  const [autoRefresh] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [timeRange, setTimeRange] = useState<'1h' | '6h' | '24h' | '7d' | '30d'>('24h'); // Default to 24h
  const [showLiveData, setShowLiveData] = useState<boolean>(true);
  const [csvImportDialogOpen, setCsvImportDialogOpen] = useState<boolean>(false);

  // Get current unit from context
  const currentUnit = useCurrentUnit();
  const { user, isLoading: authLoading } = useAuth();

  // Debug authentication context (only on errors)
  useEffect(() => {
    if (!user) {
      logWarn('No user authenticated', 'TemperatureDashboard');
    }
  }, [user]);

  // Get sensor data from hook
  const {
    sensorStatuses = [],
    loading: isLoading,
    error: fetchError,
    lastUpdated,
    refresh,
  } = useSensorStatuses();

  // Update loading and error states
  useEffect(() => {
    setLoading(isLoading);
    if (fetchError) {
      setError(fetchError);
    }
    if (lastUpdated) {
      setLastUpdate(new Date(lastUpdated));
    }
  }, [isLoading, fetchError, lastUpdated]);

  // Compute date range from selected timeRange - NOW PROPERLY SHOWS LAST X TIME FROM CURRENT TIME
  const { fromISO, toISO } = useMemo(() => {
    const to = new Date(); // Current time
    const from = new Date(); // Start from current time and subtract

    switch (timeRange) {
      case '1h':
        from.setHours(to.getHours() - 1);
        break;
      case '6h':
        from.setHours(to.getHours() - 6);
        break;
      case '7d':
        from.setDate(to.getDate() - 7);
        break;
      case '30d':
        from.setDate(to.getDate() - 30);
        break;
      case '24h':
      default:
        from.setHours(to.getHours() - 24); // Last 24 hours from now
        break;
    }

    // Debug time range changes only for non-default values
    if (timeRange !== '24h') {
      logDebug(
        `Time range: ${timeRange} (${from.toISOString()} to ${to.toISOString()})`,
        'TemperatureDashboard'
      );
    }

    return { fromISO: from.toISOString(), toISO: to.toISOString() };
  }, [timeRange]);

  // Enhanced time-series data configuration for the new chart
  const timeSeriesConfig = useMemo(
    () => ({
      timeRange: timeRange as '1h' | '6h' | '24h' | '7d' | '30d',
      sensorIds: selectedSensorId === 'all' ? [] : [selectedSensorId],
      refreshInterval: autoRefresh ? 30 : 0,
      maxDataPoints: 1000,
      enableRealTime: showLiveData,
    }),
    [timeRange, selectedSensorId, autoRefresh, showLiveData]
  );

  const timeSeriesData = useTimeSeriesData(timeSeriesConfig);

  // Load history for a specific sensor when selected
  const history = useSensorHistory(
    selectedSensorId !== 'all' ? selectedSensorId : null,
    fromISO,
    toISO
  );

  // Multi-sensor historical data for "All Sensors" view
  const externalSensorIds = useMemo(() => {
    if (selectedSensorId !== 'all') return [];
    if (sensorStatuses.length > 0) return sensorStatuses.map((s) => s.sensor.id);
    if (timeSeriesData.sensors?.length > 0) return timeSeriesData.sensors.map((s) => s.id);
    return [];
  }, [selectedSensorId, sensorStatuses, timeSeriesData.sensors]);

  // Log when there are sensor mapping issues (only for "All Sensors" view)
  if (
    selectedSensorId === 'all' &&
    externalSensorIds.length === 0 &&
    (sensorStatuses.length > 0 || timeSeriesData.sensors?.length > 0)
  ) {
    logWarn('No external sensor IDs found despite having sensor data', 'TemperatureDashboard');
  }

  const multiHistory = useMultiSensorHistory(externalSensorIds, fromISO, toISO);

  // Fallback: build time-series data from multiHistory when primary source is too sparse
  const augmentedTimeSeries = useMemo(() => {
    // If primary has enough points, use it as-is
    if (timeSeriesData.data && timeSeriesData.data.length >= 30) {
      return { data: timeSeriesData.data, sensors: timeSeriesData.sensors };
    }

    // If multiHistory has richer data, convert it to the same shape
    if (multiHistory.points && multiHistory.points.length >= 30) {
      const converted = multiHistory.points.map((p) => ({
        sensorId: p.sensor_external_id,
        sensorName: p.sensor_name ?? p.sensor_external_id,
        temperature: Number(p.temp_celsius),
        humidity: p.humidity != null ? Number(p.humidity) : undefined,
        timestamp: new Date(p.recorded_at),
        batteryLevel: p.battery_level != null ? Number(p.battery_level) : undefined,
        signalStrength: p.signal_strength != null ? Number(p.signal_strength) : undefined,
      }));

      // Build basic sensor metadata if the primary hook hasn't yet
      const sensorMeta: TimeSeriesSensorMetadata[] = sensorStatuses.map((s) => ({
        id: s.sensor.id,
        name: s.sensor.name,
        location: s.sensor.location,
        isOnline: s.isOnline,
        batteryLevel: undefined,
        lastReading: s.latestReading ? new Date(s.latestReading.timestamp) : undefined,
      }));

      return { data: converted, sensors: sensorMeta };
    }

    // Default to primary
    return { data: timeSeriesData.data, sensors: timeSeriesData.sensors };
  }, [timeSeriesData.data, timeSeriesData.sensors, multiHistory.points, sensorStatuses]);

  // Handle refresh error
  const handleRefreshError = useCallback((err: unknown) => {
    const error = err instanceof Error ? err : new Error('Failed to refresh data');
    setError(error);
    logError('Error refreshing data', 'TemperatureDashboard', error);
  }, []);

  // Handle manual refresh
  const handleRefresh = useCallback(async () => {
    setLoading(true);
    try {
      // Trigger a refetch from the hook
      await (refresh?.() ?? new Promise((resolve) => setTimeout(resolve, 500)));
      setLastUpdate(new Date());
      setError(null);
    } catch (err) {
      handleRefreshError(err);
    } finally {
      setLoading(false);
    }
  }, [handleRefreshError, refresh]);

  // Manual sync via serverless API → DB, then refresh
  const handleManualSync = useCallback(async () => {
    setLoading(true);
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) throw new Error('Please sign in to sync sensors');

      // Try Edge Function first
      try {
        const { data, error } = await supabase.functions.invoke('tempstick_sync', {
          method: 'POST',
          body: { reason: 'manual' },
        });
        if (error) {
          logError('Edge function error', 'TemperatureDashboard', error);
          throw error;
        }
        logDebug('Edge function sync result', 'TemperatureDashboard', data);
      } catch (edgeErr) {
        // Fallback to client-side manual sync (uses proxy + anon auth)
        logWarn(
          'Edge function sync failed, falling back to client sync',
          'TemperatureDashboard',
          edgeErr
        );
        const result = await manualSyncService.performManualSync();
        logDebug('Client sync result', 'TemperatureDashboard', result);
      }

      // After sync, refresh dashboard data
      await (refresh?.() ?? Promise.resolve());
      setLastUpdate(new Date());
      setError(null);
    } catch (err) {
      handleRefreshError(err);
    } finally {
      setLoading(false);
    }
  }, [refresh, handleRefreshError]);

  // Handle CSV import completion (supports multiple files)
  const handleCSVImportComplete = useCallback(
    (results: unknown[]) => {
      logDebug('Multi-file CSV import completed', 'TemperatureDashboard', results);
      setCsvImportDialogOpen(false);

      const typed = results as Array<{ success: boolean } & Record<string, unknown>>;
      const successful = typed.filter((r) => r.success).length;
      const total = typed.length;
      logDebug(`${successful}/${total} files imported successfully`, 'TemperatureDashboard');

      // Run refresh without changing the callback type
      (async () => {
        try {
          await (refresh?.() ?? Promise.resolve());
          setLastUpdate(new Date());
          setError(null);
        } catch (err) {
          handleRefreshError(err);
        }
      })();
    },
    [refresh, handleRefreshError]
  );

  // Auto-trigger a one-time manual sync if no sensors are present and user is signed in
  const [autoSyncTriggered, setAutoSyncTriggered] = useState(false);
  useEffect(() => {
    if (!autoSyncTriggered && !loading && user && sensorStatuses.length === 0) {
      setAutoSyncTriggered(true);
      handleManualSync().catch(() => {
        // ignore, error state handled by handleManualSync
      });
    }
  }, [autoSyncTriggered, loading, user, sensorStatuses.length, handleManualSync]);

  // Filter sensors based on selection
  const filteredSensors = useMemo(() => {
    if (selectedSensorId === 'all') return sensorStatuses;
    return sensorStatuses.filter((sensor) => sensor.sensor.id === selectedSensorId);
  }, [sensorStatuses, selectedSensorId]);

  // Helper function to calculate sensor status
  const getSensorStatus = useCallback((sensor: SensorStatus) => {
    if (!sensor.isOnline) return 'offline';
    if (sensor.activeAlerts?.some((alert: SensorAlert) => alert.severity === 'critical'))
      return 'critical';
    if (sensor.activeAlerts?.length > 0) return 'warning';
    return 'online';
  }, []);

  // Convert sensor data to raw data points for stats calculation
  const rawDataPoints = useMemo((): RawDataPoint[] => {
    // Single-sensor: use historical readings if available
    if (selectedSensorId !== 'all' && history.data.length > 0) {
      return history.data.map((r) => ({
        sensorId: selectedSensorId,
        at: r.recorded_at,
        tempC: Number(r.temp_celsius),
        humidityPct: r.humidity != null ? Number(r.humidity) : 0,
      }));
    }

    // All-sensors: prefer multi-sensor historical points (supports realtime overlay)
    if (selectedSensorId === 'all' && multiHistory.points.length > 0) {
      return multiHistory.points.map((p) => ({
        sensorId: p.sensor_external_id,
        at: p.recorded_at,
        tempC: Number(p.temp_celsius),
        humidityPct: p.humidity != null ? Number(p.humidity) : 0,
      }));
    }

    // Fallback: derive minimal points from latest readings across sensors
    const points: RawDataPoint[] = [];
    sensorStatuses.forEach((sensorStatus) => {
      if (sensorStatus.latestReading) {
        points.push({
          sensorId: sensorStatus.sensor.id,
          at: sensorStatus.latestReading.timestamp ?? new Date().toISOString(),
          tempC: sensorStatus.latestReading.temperature ?? 20,
          humidityPct: sensorStatus.latestReading.humidity ?? 50,
        });
      }
    });
    return points;
  }, [selectedSensorId, history.data, multiHistory.points, sensorStatuses]);

  // Calculate comprehensive stats
  const dashboardStats = useMemo((): StatsSummary => {
    return calculateStats(rawDataPoints, currentUnit);
  }, [rawDataPoints, currentUnit]);

  // Generate sensor metadata for PDF export
  const sensorMetadata = useMemo((): SensorMetadata[] => {
    return sensorStatuses.map((sensorStatus) => ({
      id: sensorStatus.sensor.id,
      name: sensorStatus.sensor.name,
      status: sensorStatus.isOnline ? 'online' : 'offline',
      batteryPercent: 85, // Mock data - replace with real battery level
      signalStrength: 72, // Mock data - replace with real signal strength
      lastReadingAt: sensorStatus.latestReading?.timestamp,
      location: `Sensor ${sensorStatus.sensor.name}`, // Mock location
    }));
  }, [sensorStatuses]);

  // Calculate metrics (updated for unit conversion)
  const metrics = useMemo(() => {
    const totalSensors = sensorStatuses.length;
    const onlineSensors = sensorStatuses.filter((s) => s.isOnline).length;
    const activeAlertsCount = sensorStatuses.reduce(
      (count, sensor) => count + (sensor.activeAlerts?.length ?? 0),
      0
    );

    const temperatures = sensorStatuses
      .map((s) => s.latestReading?.temperature)
      .filter((t): t is number => t !== undefined);

    const averageTemperature =
      temperatures.length > 0
        ? temperatures.reduce((sum, t) => sum + t, 0) / temperatures.length
        : 0;

    return {
      totalSensors,
      onlineSensors,
      activeAlertsCount,
      averageTemperature, // Keep in Celsius for formatTemp to handle conversion
    };
  }, [sensorStatuses]);

  // Memoize single sensor chart data to prevent re-computation
  const singleSensorChartData = useMemo(() => {
    if (selectedSensorId === 'all' || history.data.length === 0) return null;

    return history.data.map((r) => {
      const d = new Date(r.recorded_at);
      const timeStr = d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      return {
        time: timeStr,
        [selectedSensorId]: round1(displayTemp(r.temp_celsius, currentUnit)),
      } as Record<string, number | string>;
    });
  }, [selectedSensorId, history.data, currentUnit]);

  // Memoize multi-sensor chart data to prevent expensive computation
  const multiSensorChartData = useMemo(() => {
    if (selectedSensorId !== 'all' || multiHistory.points.length === 0 || multiHistory.loading) {
      return null;
    }

    const sensorIds = new Set<string>();
    const bucketedData = new Map<number, Map<string, number>>();

    let minBucketKey = Number.POSITIVE_INFINITY;
    let maxBucketKey = Number.NEGATIVE_INFINITY;

    const bucketSizeMs = (() => {
      switch (timeRange) {
        case '1h':
          return 5 * 60 * 1000; // 5 minutes
        case '6h':
          return 15 * 60 * 1000; // 15 minutes
        case '24h':
          return 30 * 60 * 1000; // 30 minutes
        case '7d':
          return 3 * 60 * 60 * 1000; // 3 hours
        case '30d':
          return 6 * 60 * 60 * 1000; // 6 hours
        default:
          return 15 * 60 * 1000;
      }
    })();

    for (const point of multiHistory.points) {
      const convertedTemp = round1(displayTemp(point.temp_celsius, currentUnit));
      if (!Number.isFinite(convertedTemp)) continue;

      const recordedAt = new Date(point.recorded_at);
      const timeMs = recordedAt.getTime();
      if (!Number.isFinite(timeMs)) continue;

      const bucketKey = Math.floor(timeMs / bucketSizeMs) * bucketSizeMs;
      sensorIds.add(point.sensor_external_id);

      let bucket = bucketedData.get(bucketKey);
      if (!bucket) {
        bucket = new Map<string, number>();
        bucketedData.set(bucketKey, bucket);
      }
      bucket.set(point.sensor_external_id, convertedTemp);

      if (bucketKey < minBucketKey) minBucketKey = bucketKey;
      if (bucketKey > maxBucketKey) maxBucketKey = bucketKey;
    }

    if (!bucketedData.size || !Number.isFinite(minBucketKey) || !Number.isFinite(maxBucketKey)) {
      return null;
    }

    const fromTime = new Date(fromISO).getTime();
    const toTime = new Date(toISO).getTime();

    const normalizedFrom = Number.isFinite(fromTime)
      ? Math.floor(fromTime / bucketSizeMs) * bucketSizeMs
      : minBucketKey;
    const normalizedTo = Number.isFinite(toTime)
      ? Math.ceil(toTime / bucketSizeMs) * bucketSizeMs
      : maxBucketKey;

    const allSensorIds = Array.from(
      new Set([
        ...sensorIds,
        ...sensorStatuses.map((s) => s.sensor.id),
      ])
    );
    if (allSensorIds.length === 0) {
      return null;
    }

    const lastKnownValues = new Map<string, number>();
    const chartData: Array<Record<string, number | string | null>> = [];

    for (let bucket = normalizedFrom; bucket <= normalizedTo; bucket += bucketSizeMs) {
      const date = new Date(bucket);
      const timeStr =
        timeRange === '7d' || timeRange === '30d'
          ? date.toLocaleString([], {
              month: 'short',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit',
            })
          : date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

      const row: Record<string, number | string | null> = {
        time: timeStr,
        timestamp: bucket,
      };

      const bucketValues = bucketedData.get(bucket);
      if (bucketValues) {
        bucketValues.forEach((tempValue, sensorId) => {
          lastKnownValues.set(sensorId, tempValue);
        });
      }

      for (const sensorId of allSensorIds) {
        row[sensorId] = lastKnownValues.get(sensorId) ?? null;
      }

      chartData.push(row);
    }

    // Debug: Log the data keys vs expected Line dataKeys
    if (process.env.NODE_ENV === 'development' && chartData.length > 0) {
      const dataKeys = Object.keys(chartData[0]).filter((k) => k !== 'time' && k !== 'timestamp');
      const expectedKeys = sensorStatuses.map((s) => s.sensor.id);

      // Log comprehensive chart data structure for debugging
      logDebug('📊 Multi-sensor chart data structure analysis', 'TemperatureDashboard', {
        totalDataPoints: chartData.length,
        dataKeysInChart: dataKeys,
        expectedLineDataKeys: expectedKeys,
        keyMismatches: expectedKeys.filter((expected) => !dataKeys.includes(expected)),
        unexpectedKeys: dataKeys.filter((actual) => !expectedKeys.includes(actual)),
        sampleDataPoint: chartData[0],
        lastDataPoint: chartData[chartData.length - 1],
        // Check for null/undefined values that might prevent line drawing
        dataPointsWithNullValues: chartData.slice(0, 5).map((point, idx) => ({
          pointIndex: idx,
          time: point.time,
          values: Object.keys(point)
            .filter((k) => k !== 'time' && k !== 'timestamp')
            .reduce(
              (acc, key) => {
                acc[key] = {
                  value: point[key],
                  isNull: point[key] == null,
                  isUndefined: point[key] === undefined,
                  type: typeof point[key],
                };
                return acc;
              },
              {} as Record<string, unknown>
            ),
        })),
        multiHistorySamplePoints: multiHistory.points.slice(0, 3).map((p) => ({
          sensor_external_id: p.sensor_external_id,
          sensor_name: p.sensor_name,
        })),
        sensorStatusSample: sensorStatuses.slice(0, 3).map((s) => ({
          sensor_id: s.sensor.id,
          sensor_name: s.sensor.name,
        })),
      });
    }

    // Ensure we have data for the chart by checking if the keys actually match
    if (chartData.length > 0 && sensorStatuses.length > 0) {
      const dataKeys = Object.keys(chartData[0]).filter((k) => k !== 'time' && k !== 'timestamp');
      const hasAnyValidData = sensorStatuses.some((s) => dataKeys.includes(s.sensor.id));

      if (!hasAnyValidData) {
        logWarn('No valid sensor data keys found in chart data', 'TemperatureDashboard', {
          chartDataKeys: dataKeys,
          sensorIds: sensorStatuses.map((s) => s.sensor.id),
          possibleMismatch: 'External vs internal sensor ID mismatch',
        });
      }
    }

    return chartData;
  }, [
    selectedSensorId,
    multiHistory.points,
    multiHistory.loading,
    sensorStatuses,
    currentUnit,
    timeRange,
    fromISO,
    toISO,
  ]);

  // Enhanced fallback chart data with proper external sensor IDs
  const enhancedFallbackChartData = useMemo(() => {
    // Only use fallback if we have sensors but no multi-sensor data
    if (selectedSensorId !== 'all' || !sensorStatuses.length || multiHistory.points.length > 0) {
      return null;
    }

    const now = new Date();
    const timeStr = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    const point: Record<string, number | string> = { time: timeStr };
    let sensorsIncluded = 0;

    sensorStatuses.forEach((s) => {
      // Use sensor.id (external ID) as the key to match Line dataKey
      if (s.latestReading?.temperature != null) {
        const t = s.latestReading.temperature;
        point[s.sensor.id] = round1(displayTemp(t, currentUnit));
        sensorsIncluded++;
        logDebug(`Enhanced fallback included sensor: ${s.sensor.name}`, 'TemperatureDashboard', {
          externalId: s.sensor.id,
          temperature: `${t}°C`,
          convertedTemp: `${round1(displayTemp(t, currentUnit))}°${currentUnit === 'celsius' ? 'C' : 'F'}`,
        });
      }
    });

    if (sensorsIncluded > 0) {
      logDebug('Enhanced fallback chart data created', 'TemperatureDashboard', {
        sensorsIncluded,
        totalSensors: sensorStatuses.length,
        dataKeys: Object.keys(point).filter((k) => k !== 'time'),
      });
      return [point];
    }

    return null;
  }, [selectedSensorId, sensorStatuses, multiHistory.points.length, currentUnit]);

  // Memoize fallback chart data
  const fallbackChartData = useMemo(() => {
    const now = new Date();
    const timeStr = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    const point: Record<string, number | string> = { time: timeStr };
    let sensorsIncluded = 0;

    sensorStatuses.forEach((s) => {
      // Only include sensors that actually have temperature readings
      if (s.latestReading?.temperature != null) {
        const t = s.latestReading.temperature;
        point[s.sensor.id] = round1(displayTemp(t, currentUnit));
        sensorsIncluded++;
      }
    });

    return sensorsIncluded > 0 ? [point] : [];
  }, [sensorStatuses, currentUnit]);

  // Main chart data selection with enhanced fallback
  const chartData = useMemo(() => {
    // Return single sensor data if available
    if (singleSensorChartData && singleSensorChartData.length > 0) {
      return singleSensorChartData;
    }

    // Return multi-sensor data if available
    if (multiSensorChartData && multiSensorChartData.length > 0) {
      logDebug('Using multi-sensor chart data', 'TemperatureDashboard', {
        dataPointsCount: multiSensorChartData.length,
        sampleDataPoint: multiSensorChartData[0],
        sensorIds: sensorStatuses.map((s) => s.sensor.id),
      });
      return multiSensorChartData;
    }

    // Use enhanced fallback if available
    if (enhancedFallbackChartData && enhancedFallbackChartData.length > 0) {
      logDebug('Using enhanced fallback chart data', 'TemperatureDashboard');
      return enhancedFallbackChartData;
    }

    // Use original fallback
    if (fallbackChartData.length > 0) {
      logDebug('Using original fallback chart data', 'TemperatureDashboard');
      return fallbackChartData;
    }

    // Log issues if we have sensors but no data
    if (sensorStatuses.length > 0) {
      logWarn('No chart data available despite having sensors', 'TemperatureDashboard', {
        sensorCount: sensorStatuses.length,
        multiHistoryPoints: multiHistory.points.length,
        multiHistoryLoading: multiHistory.loading,
        selectedSensorId,
      });
    }

    return [];
  }, [
    singleSensorChartData,
    multiSensorChartData,
    enhancedFallbackChartData,
    fallbackChartData,
    sensorStatuses,
    multiHistory.points.length,
    multiHistory.loading,
    selectedSensorId,
  ]);

  // Live Data list (last 10 points from the same sources feeding the chart)
  const livePoints = useMemo(() => {
    if (selectedSensorId === 'all') {
      return [...multiHistory.points]
        .sort((a, b) => new Date(b.recorded_at).getTime() - new Date(a.recorded_at).getTime())
        .slice(0, 10)
        .map((p) => ({
          sensorLabel: p.sensor_name ?? p.sensor_external_id,
          at: new Date(p.recorded_at),
          tempC: Number(p.temp_celsius),
          humidity: p.humidity != null ? Number(p.humidity) : undefined,
        }));
    }
    return [...history.data]
      .sort((a, b) => new Date(b.recorded_at).getTime() - new Date(a.recorded_at).getTime())
      .slice(0, 10)
      .map((r) => ({
        sensorLabel:
          sensorStatuses.find((s) => s.sensor.id === selectedSensorId)?.sensor.name ??
          selectedSensorId,
        at: new Date(r.recorded_at),
        tempC: Number(r.temp_celsius),
        humidity: r.humidity != null ? Number(r.humidity) : undefined,
      }));
  }, [selectedSensorId, multiHistory.points, history.data, sensorStatuses]);

  // Generate colors for each sensor
  const sensorColors = useMemo(() => {
    const colors = [
      '#3b82f6', // blue-500
      '#10b981', // emerald-500
      '#f59e0b', // amber-500
      '#8b5cf6', // violet-500
      '#ec4899', // pink-500
      '#14b8a6', // teal-500
      '#f97316', // orange-500
      '#6366f1', // indigo-500
    ];

    const colorMap: Record<string, string> = {};
    sensorStatuses.forEach((sensor, index) => {
      colorMap[sensor.sensor.tempstick_sensor_id] = colors[index % colors.length];
    });

    return colorMap;
  }, [sensorStatuses]);

  // Format last updated time
  const lastUpdatedDisplay = lastUpdate ? lastUpdate.toLocaleTimeString() : 'just now';

  // Render the component
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Controls - moved above dashboard */}
      {showFilters && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">Monitor:</span>
            <Select value={selectedSensorId} onValueChange={setSelectedSensorId}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Select sensor" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sensors</SelectItem>
                {sensorStatuses.map((sensorStatus) => (
                  <SelectItem key={sensorStatus.sensor.internalId} value={sensorStatus.sensor.id}>
                    {sensorStatus.sensor.name} ({getSensorStatus(sensorStatus)})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <span className="text-sm font-medium ml-3">Range:</span>
            <Select
              value={timeRange}
              onValueChange={(v) => setTimeRange(v as '1h' | '6h' | '24h' | '7d' | '30d')}
            >
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Select range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1h">Last 1 hour</SelectItem>
                <SelectItem value="6h">Last 6 hours</SelectItem>
                <SelectItem value="24h">Last 24 hours</SelectItem>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      <div className="flex flex-col gap-4 lg:flex-row lg:items-start lg:justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-bold">Temperature Dashboard</h2>
          <UnitToggle size="md" showLabel={true} />
        </div>
        <div className="flex flex-col gap-3 sm:flex-row sm:items-start sm:justify-end sm:gap-4 lg:items-center">
          <div className="w-full sm:w-auto">
            <TempStickSyncStatus />
          </div>
          <div className="flex items-center justify-end gap-2">
            {/* PDF Export Button */}
            <PdfButton
              stats={dashboardStats}
              sensorMetadata={sensorMetadata}
              title="Temperature Dashboard Report"
              dateRange={
                dashboardStats.timeRange.start && dashboardStats.timeRange.end
                  ? {
                      start: dashboardStats.timeRange.start,
                      end: dashboardStats.timeRange.end,
                    }
                  : undefined
              }
              variant="outline"
              size="sm"
            />

            <span className="text-sm text-gray-500">Last updated: {lastUpdatedDisplay}</span>
            <button
              type="button"
              onClick={handleRefresh}
              disabled={loading}
              className="p-1 rounded-full hover:bg-gray-100 disabled:opacity-50"
              aria-label="Refresh data"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>
      </div>

      {/* Combined Stats Cards - Temperature & Humidity Summary */}
      {dashboardStats.dataPoints > 0 && (
        <CombinedStatsCards stats={dashboardStats} className="mb-6" compact={compactMode} />
      )}

      {/* Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sensors</CardTitle>
            <Thermometer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalSensors}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Online</CardTitle>
            <Wifi className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.onlineSensors}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Alerts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activeAlertsCount}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Temperature</CardTitle>
            <Thermometer className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatTemp(metrics.averageTemperature, currentUnit)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Time-Series Chart or Fallback Chart */}
      {augmentedTimeSeries.data.length > 0 ? (
        // Show enhanced chart whenever we have time-series data (primary or augmented)
        <EnhancedTimeSeriesChart
          data={augmentedTimeSeries.data}
          sensors={augmentedTimeSeries.sensors}
          height={400}
          timeRange={timeRange}
          showBrush={true}
          showThresholds={true}
          showViolations={true}
          enableZoom={true}
          autoRefreshInterval={autoRefresh ? 30 : 0}
          onRefresh={handleRefresh}
          isLoading={timeSeriesData.isLoading}
          error={timeSeriesData.error}
          className="mb-6"
        />
      ) : sensorStatuses.length > 0 ? (
        // Fallback to existing chart for limited data when we have sensor statuses but no time-series
        <ChartRow
          title="Temperature Trends"
          dataPoints={rawDataPoints}
          sensorNames={sensorStatuses.reduce(
            (acc, status) => {
              acc[status.sensor.id] = status.sensor.name;
              return acc;
            },
            {} as Record<string, string>
          )}
          dateRange={{ start: fromISO, end: toISO }}
          showDownload={true}
          showRange={true}
        >
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="time" tick={{ fontSize: 12 }} />
              <YAxis
                label={{
                  value: currentUnit === 'celsius' ? '°C' : '°F',
                  angle: -90,
                  position: 'insideLeft',
                }}
                tick={{ fontSize: 12 }}
              />
              <Tooltip
                formatter={(value: number) => [formatTemp(value, currentUnit), 'Temperature']}
                labelFormatter={(label) => `Time: ${label}`}
              />
              <Legend />
              {selectedSensorId === 'all' ? (
                sensorStatuses
                  .filter((sensor) => {
                    // Only render Line components for sensors that actually have data in the chart
                    const hasDataInChart = chartData.some((point) => {
                      const value = point[sensor.sensor.tempstick_sensor_id];
                      return value != null && typeof value === 'number' && !isNaN(value);
                    });

                    if (process.env.NODE_ENV === 'development' && !hasDataInChart) {
                      logWarn(
                        `Skipping Line component for sensor ${sensor.sensor.name} - no valid data in chart`,
                        'TemperatureDashboard'
                      );
                    }

                    return hasDataInChart;
                  })
                  .map((sensor) => {
                    // Debug: Log Line component configuration for each sensor
                    if (process.env.NODE_ENV === 'development') {
                      logDebug('Rendering Line component for sensor', 'TemperatureDashboard', {
                        sensorName: sensor.sensor.name,
                        dataKey: sensor.sensor.tempstick_sensor_id,
                        hasColor: Boolean(sensorColors[sensor.sensor.tempstick_sensor_id]),
                        color: sensorColors[sensor.sensor.tempstick_sensor_id],
                        // Check if this dataKey exists in the chart data
                        dataKeyExistsInChart: chartData.some((point) => sensor.sensor.tempstick_sensor_id in point),
                        chartDataKeys:
                          chartData.length > 0
                            ? Object.keys(chartData[0]).filter((k) => k !== 'time')
                            : [],
                        validDataPointsCount: chartData.filter((point) => {
                          const value = point[sensor.sensor.tempstick_sensor_id];
                          return value != null && typeof value === 'number' && !isNaN(value);
                        }).length,
                      });
                    }
                    return (
                      <Line
                        key={sensor.sensor.internalId}
                        type="monotone"
                        dataKey={sensor.sensor.tempstick_sensor_id}
                        name={sensor.sensor.name}
                        stroke={sensorColors[sensor.sensor.tempstick_sensor_id]}
                        strokeWidth={2}
                        connectNulls={true}
                        dot={false}
                        activeDot={{ r: 6 }}
                      />
                    );
                  })
              ) : (
                <Line
                  key={selectedSensorId}
                  type="monotone"
                  dataKey={selectedSensorId}
                  name={
                    sensorStatuses.find((s) => s.sensor.id === selectedSensorId)?.sensor.name ??
                    selectedSensorId
                  }
                  stroke={sensorColors[selectedSensorId] ?? '#3b82f6'}
                  strokeWidth={2}
                  connectNulls
                  dot={Boolean(
                    multiHistory.bySensor?.[selectedSensorId]?.length &&
                      multiHistory.bySensor[selectedSensorId].length <= 1
                  )}
                  activeDot={{ r: 6 }}
                />
              )}
            </LineChart>
          </ResponsiveContainer>
        </ChartRow>
      ) : (
        !loading && (
          <Card className="border-dashed">
            <CardHeader>
              <CardTitle>No sensors found</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                No temperature sensors match your current filters.
              </p>
              {!authLoading && !user && (
                <p className="text-sm text-red-600 mb-2">
                  Please sign in to view or sync your sensors.
                </p>
              )}
              <div className="flex items-center gap-2">
                <Button onClick={handleManualSync} disabled={loading ?? !user}>
                  {loading ? 'Syncing…' : 'Sync now'}
                </Button>
                <Button variant="outline" onClick={handleRefresh} disabled={loading}>
                  Refresh
                </Button>
              </div>
            </CardContent>
          </Card>
        )
      )}

      {/* Live Data Panel */}
      {sensorStatuses.length > 0 && (
        <Card className="mt-4">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Live Data (last 10)</CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => setShowLiveData((v) => !v)}>
                {showLiveData ? 'Hide' : 'Show'}
              </Button>
            </div>
          </CardHeader>
          {showLiveData && (
            <CardContent>
              {livePoints.length === 0 ? (
                <div className="text-sm text-muted-foreground">
                  No recent readings in the selected range.
                </div>
              ) : (
                <ul className="text-sm grid grid-cols-1 md:grid-cols-2 gap-2">
                  {livePoints.map((p, idx) => (
                    <li
                      key={idx}
                      className="flex items-center justify-between border-b last:border-b-0 py-1"
                    >
                      <span className="truncate mr-2">{p.sensorLabel}</span>
                      <span className="text-muted-foreground mr-2">
                        {p.at.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </span>
                      <span className="font-medium">{formatTemp(p.tempC, currentUnit)}</span>
                      {typeof p.humidity === 'number' && (
                        <span className="text-muted-foreground ml-2">
                          {Math.round(p.humidity)}% RH
                        </span>
                      )}
                    </li>
                  ))}
                </ul>
              )}
            </CardContent>
          )}
        </Card>
      )}

      {/* Sensor Status Grid */}
      {sensorStatuses.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredSensors.map((sensorStatus) => {
            const isOnline = getSensorStatus(sensorStatus) === 'online';
            return (
              <Card
                key={sensorStatus.sensor.internalId}
                className="hover:shadow-md transition-shadow"
              >
                <CardHeader>
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <Thermometer className="h-4 w-4" />
                    {sensorStatus.sensor.name}
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <div
                      className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${
                        isOnline ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      <Wifi className="h-3 w-3" />
                      {getSensorStatus(sensorStatus)}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {sensorStatus.latestReading ? (
                    <div>
                      <div className="text-2xl font-bold">
                        {formatTemp(sensorStatus.latestReading.temperature, currentUnit)}
                      </div>
                      {sensorStatus.latestReading.humidity && (
                        <div className="text-sm text-muted-foreground">
                          {sensorStatus.latestReading.humidity.toFixed(0)}% RH
                        </div>
                      )}
                      {sensorStatus.activeAlerts.length > 0 && (
                        <div className="flex items-center gap-1 text-xs text-orange-600 mt-2">
                          <AlertTriangle className="h-3 w-3" />
                          {sensorStatus.activeAlerts.length} alert
                          {sensorStatus.activeAlerts.length > 1 ? 's' : ''}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      <Thermometer className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No recent readings</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Authentication Debug Helper (temporary) */}
      {(!user || Boolean(error) || sensorStatuses.length === 0) && (
        <div className="mb-6">
          <AuthDebugHelper />
        </div>
      )}

      {/* Error Alert */}
      {error && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error.message}</AlertDescription>
        </Alert>
      )}

      {/* Alert Notifications */}
      {metrics.activeAlertsCount > 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            ⚠️ {metrics.activeAlertsCount} active alert{metrics.activeAlertsCount > 1 ? 's' : ''}{' '}
            detected
            {selectedSensorId !== 'all' && ' for selected sensor'}. Please check sensor status for
            details.
          </AlertDescription>
        </Alert>
      )}

      {/* Multi-File CSV Import Dialog */}
      <MultiFileCSVImportDialog
        isOpen={csvImportDialogOpen}
        onClose={() => setCsvImportDialogOpen(false)}
        onImportComplete={handleCSVImportComplete}
      />
    </div>
  );
};
