import React, { useEffect, useMemo, useState } from 'react';
import {
  <PERSON>ertTriangle,
  ClipboardCopy,
  ClipboardList,
  ExternalLink,
  Info,
  Mic,
  MicOff,
  RefreshCw,
  ShieldAlert,
  Wifi,
  WifiOff,
  X,
} from 'lucide-react';
import { VoiceError, VoiceErrorType } from '../../services/VoiceErrorHandler';

interface VoiceErrorDisplayProps {
  error?: VoiceError;
  onRetry?: () => void;
  onDismiss?: () => void;
  onUseManualInput?: () => void;
  isRetrying?: boolean;
  supportUrl?: string;
}

const VoiceErrorDisplay: React.FC<VoiceErrorDisplayProps> = ({
  error,
  onRetry,
  onDismiss,
  onUseManualInput,
  isRetrying = false,
  supportUrl,
}) => {
  const [showTechnicalDetails, setShowTechnicalDetails] = useState(false);
  const [copied, setCopied] = useState(false);
  const [countdown, setCountdown] = useState<number | null>(null);

  useEffect(() => {
    if (typeof error?.retryAfter === 'number') {
      setCountdown(error.retryAfter);
    } else {
      setCountdown(null);
    }
  }, [error?.retryAfter]);

  useEffect(() => {
    if (countdown === null || countdown <= 0) {
      return;
    }

    const handle = window.setTimeout(() => {
      setCountdown((value) => (typeof value === 'number' && value > 0 ? value - 1 : value));
    }, 1000);

    return () => window.clearTimeout(handle);
  }, [countdown]);

  useEffect(() => {
    if (!copied) {
      return;
    }

    const handle = window.setTimeout(() => setCopied(false), 2000);
    return () => window.clearTimeout(handle);
  }, [copied]);

  const developerDetails = useMemo(() => {
    if (!error?.developerDetails) {
      return undefined;
    }

    try {
      const parsed = JSON.parse(error.developerDetails);
      return JSON.stringify(parsed, null, 2);
    } catch {
      return error.developerDetails;
    }
  }, [error?.developerDetails]);

  if (!error) {
    return null;
  }

  const getErrorIcon = () => {
    switch (error.type) {
      case VoiceErrorType.NETWORK_ERROR:
        return <WifiOff className="w-6 h-6 text-red-500" aria-hidden="true" />;
      case VoiceErrorType.AUDIO_PROCESSING_ERROR:
      case VoiceErrorType.PERMISSION_ERROR:
        return <MicOff className="w-6 h-6 text-red-500" aria-hidden="true" />;
      case VoiceErrorType.TRANSCRIPTION_ERROR:
        return <Mic className="w-6 h-6 text-yellow-500" aria-hidden="true" />;
      default:
        return <AlertTriangle className="w-6 h-6 text-red-500" aria-hidden="true" />;
    }
  };

  const getErrorColor = () => {
    if (error.recoverable) {
      return error.type === VoiceErrorType.TRANSCRIPTION_ERROR
        ? 'border-yellow-200 bg-yellow-50'
        : 'border-orange-200 bg-orange-50';
    }
    return 'border-red-200 bg-red-50';
  };

  const getHeaderColor = () => {
    if (error.recoverable) {
      return error.type === VoiceErrorType.TRANSCRIPTION_ERROR
        ? 'text-yellow-800'
        : 'text-orange-800';
    }
    return 'text-red-800';
  };

  const getTextColor = () => {
    if (error.recoverable) {
      return error.type === VoiceErrorType.TRANSCRIPTION_ERROR
        ? 'text-yellow-700'
        : 'text-orange-700';
    }
    return 'text-red-700';
  };

  const retryLabel = (() => {
    switch (error.retryStrategy) {
      case 'delayed':
        return countdown && countdown > 0 ? `Retry in ${countdown}s` : 'Retry Now';
      case 'manual':
        return 'Retry Manually';
      default:
        return 'Try Again';
    }
  })();

  const assistanceUrl = supportUrl ?? 'https://status.openai.com/';
  const showSupportLink = typeof error.statusCode === 'number' ? error.statusCode >= 500 : Boolean(supportUrl);
  const retryDisabled =
    isRetrying || (error.retryStrategy === 'delayed' && countdown !== null && countdown > 0);

  const handleCopy = async () => {
    if (!developerDetails) {
      return;
    }

    try {
      await navigator.clipboard?.writeText(developerDetails);
      setCopied(true);
    } catch (copyError) {
      console.warn('Failed to copy developer details', copyError);
    }
  };

  return (
    <section
      className={`border rounded-lg p-4 ${getErrorColor()} focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500`}
      aria-live="assertive"
      role="alert"
    >
      <div className="flex items-start justify-between gap-3">
        <div className="flex items-start space-x-3 flex-1">
          <div className="flex-shrink-0 mt-1" aria-hidden="true">
            {getErrorIcon()}
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex flex-wrap items-center gap-2">
              <h3 className={`font-medium text-base ${getHeaderColor()} break-words`}>{error.userMessage}</h3>
              {typeof error.statusCode === 'number' && (
                <span className="inline-flex items-center rounded-full bg-white/70 px-2 py-0.5 text-xs font-semibold text-gray-700 flex-shrink-0">
                  Status {error.statusCode}
                </span>
              )}
              {error.correlationId && (
                <span className="inline-flex items-center rounded-full bg-white/70 px-2 py-0.5 text-xs font-medium text-gray-700 flex-shrink-0">
                  ID {error.correlationId}
                </span>
              )}
            </div>

            <div className={`mt-2 text-sm ${getTextColor()}`}>
              <ul className="space-y-1">
                {error.actionSteps.map((step, index) => (
                  <li key={index} className="flex items-start">
                    <span className="mr-2 flex-shrink-0">•</span>
                    <span className="break-words">{step}</span>
                  </li>
                ))}
              </ul>
            </div>

            {typeof countdown === 'number' && countdown > 0 && (
              <div className={`mt-2 text-xs ${getTextColor()}`} role="status">
                Automatic retry in {countdown} seconds
              </div>
            )}
          </div>
        </div>

        {onDismiss && (
          <button
            type="button"
            onClick={onDismiss}
            className={`flex-shrink-0 ml-2 p-1 rounded-md hover:bg-gray-100 ${getTextColor()}`}
            aria-label="Dismiss error"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>

      <div className="mt-4 flex flex-wrap gap-2" role="group" aria-label="Error recovery actions">
        {error.recoverable && onRetry && (
          <button
            type="button"
            onClick={onRetry}
            disabled={retryDisabled}
            className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-live="polite"
          >
            {isRetrying ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Retrying...
              </>
            ) : (
              <>
                <RefreshCw className="w-4 h-4 mr-2" />
                {retryLabel}
              </>
            )}
          </button>
        )}

        {onUseManualInput && (
          <button
            type="button"
            onClick={onUseManualInput}
            className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md bg-blue-600 text-white hover:bg-blue-700"
            aria-label="Switch to manual input"
          >
            Use Manual Input
          </button>
        )}

        {error.type === VoiceErrorType.PERMISSION_ERROR && (
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md bg-blue-600 text-white hover:bg-blue-700"
            aria-label="Refresh page for microphone permissions"
          >
            Refresh Page
          </button>
        )}

        {showSupportLink && (
          <a
            href={assistanceUrl}
            target="_blank"
            rel="noreferrer"
            className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            Check Service Status
          </a>
        )}
      </div>

      {error.type === VoiceErrorType.NETWORK_ERROR && (
        <div className="mt-3 flex items-center text-sm">
          {navigator.onLine ? (
            <>
              <Wifi className="w-4 h-4 text-green-500 mr-2" />
              <span className="text-green-700">
                Connection restored - voice input will be processed automatically
              </span>
            </>
          ) : (
            <>
              <WifiOff className="w-4 h-4 text-red-500 mr-2" />
              <span className={`${getTextColor()}`}>
                Currently offline - voice input will be processed when connection is restored
              </span>
            </>
          )}
        </div>
      )}

      {developerDetails && (
        <div className="mt-4 border-t border-white/40 pt-3">
          <div className="flex items-center justify-between">
            <button
              type="button"
              className="inline-flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900"
              onClick={() => setShowTechnicalDetails((value) => !value)}
              aria-expanded={showTechnicalDetails ? 'true' : 'false'}
              aria-controls="voice-error-technical-details"
            >
              {showTechnicalDetails ? (
                <ClipboardList className="w-4 h-4" />
              ) : (
                <Info className="w-4 h-4" />
              )}
              {showTechnicalDetails ? 'Hide technical details' : 'Show technical details'}
            </button>

            <div className="flex items-center gap-2">
              <button
                type="button"
                onClick={handleCopy}
                disabled={!developerDetails}
                className="inline-flex items-center gap-2 text-xs font-medium text-gray-600 hover:text-gray-900 disabled:opacity-50"
                aria-label="Copy technical details"
              >
                <ClipboardCopy className="w-3 h-3" />
                {copied ? 'Copied!' : 'Copy'}
              </button>
              <button
                type="button"
                onClick={() => window.open(assistanceUrl, '_blank', 'noopener')}
                className="inline-flex items-center gap-2 text-xs font-medium text-gray-600 hover:text-gray-900"
              >
                <ShieldAlert className="w-3 h-3" />
                Report Issue
              </button>
            </div>
          </div>

          {showTechnicalDetails && (
            <pre
              id="voice-error-technical-details"
              className="mt-3 max-h-56 overflow-auto rounded-md bg-white/80 p-3 text-xs text-gray-900"
            >
              {developerDetails}
            </pre>
          )}
        </div>
      )}
    </section>
  );
};

export default VoiceErrorDisplay;
