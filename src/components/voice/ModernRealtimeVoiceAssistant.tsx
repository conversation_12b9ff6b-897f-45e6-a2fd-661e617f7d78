import React, { useC<PERSON>back, useEffect, useState, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Wif<PERSON>, WifiOff, Database, Brain, Zap, Loader2, AlertTriangle, CheckCircle, ExternalLink } from 'lucide-react';

import { appEnv } from '@/lib/config/env';

import type { VoiceEventData } from '../../types/schema';
import { useModernRealtimeVoice } from '../../hooks/useModernRealtimeVoice';
import { useVoiceAssistantContext } from '../../contexts/VoiceAssistantContext';
import { useNavigationContext } from '../../contexts/NavigationContext';
import { useAuth } from '../../hooks/useAuth';
import { setAuthenticatedClient } from '../../lib/realtime-tools';
import { supabase } from '../../lib/supabase';
import {
  AudioLevelMeter,
  InterimTranscript,
  ToolExecutionPreview,
  EnhancedErrorDisplay,
  VoiceStateIndicator,
} from '../voice-assistant';

interface VoiceEventResultSummary {
  id: string;
  product_name?: string | null;
  event_type?: string | null;
  quantity?: number | null;
  unit?: string | null;
  occurred_at?: string | null;
  created_at?: string | null;
}

interface ModernRealtimeVoiceAssistantProps {
  userId?: string;
  onEventCreated?: (event: VoiceEventData) => void;
  onError?: (error: string) => void;
  forceTransport?: 'webrtc' | 'websocket';
  disableFallback?: boolean;
  enableVerboseLogging?: boolean;
}

const ModernRealtimeVoiceAssistant: React.FC<ModernRealtimeVoiceAssistantProps> = ({
  userId: _userId,
  onEventCreated,
  onError,
  forceTransport,
  disableFallback = false,
  enableVerboseLogging = false,
}) => {
  // Get authenticated user
  const { user, isLoading: authLoading } = useAuth();
  
  // Use authenticated user ID, fallback to prop, then fallback to default
  const actualUserId = user?.id || _userId || 'seafood-user';

  // Enhanced authentication setup for voice operations
  const [authenticationStatus, setAuthenticationStatus] = useState<{
    isAuthenticated: boolean;
    error?: string;
    isValidating?: boolean;
  }>({ isAuthenticated: false });

  // Set up authenticated Supabase client for voice operations when user is available
  useEffect(() => {
    const setupAuthentication = async () => {
      if (user && !authLoading) {
        setAuthenticationStatus({ isAuthenticated: false, isValidating: true });

        try {
          // Validate the user session and database access
          const { data: { session }, error: sessionError } = await supabase.auth.getSession();

          if (sessionError || !session) {
            console.error('🚫 Voice assistant session validation failed:', sessionError);
            setAuthenticationStatus({
              isAuthenticated: false,
              error: 'Invalid session for voice operations'
            });
            return;
          }

          // Create an authenticated client and validate it can access the database
          const { success, error } = await setAuthenticatedClient(supabase);

          if (success) {
            console.log('✅ Voice assistant authenticated as:', user.email);
            setAuthenticationStatus({ isAuthenticated: true });
          } else {
            console.error('🚫 Voice assistant authentication failed:', error);
            setAuthenticationStatus({
              isAuthenticated: false,
              error: error || 'Failed to set up authenticated client'
            });
          }
        } catch (error) {
          console.error('🚫 Voice assistant authentication error:', error);
          setAuthenticationStatus({
            isAuthenticated: false,
            error: error instanceof Error ? error.message : 'Authentication setup failed'
          });
        }
      } else if (!user && !authLoading) {
        setAuthenticationStatus({
          isAuthenticated: false,
          error: 'User authentication required for voice inventory operations'
        });
      }
    };

    setupAuthentication();
  }, [user, authLoading]);

  // Generate unique instance ID
  const instanceId = useRef(Math.random().toString(36).substr(2, 9)).current;
  const { registerInstance, unregisterInstance, isInstanceActive, getActiveInstanceId } = useVoiceAssistantContext();
  const { setActiveView, clearViewFilters } = useNavigationContext();
  
  const [recentActivities, setRecentActivities] = useState<string[]>([]);
  const [ephemeralToken, setEphemeralToken] = useState<string | null>(null);
  const [assistantSession, setAssistantSession] = useState<{
    id: string;
    model: string;
    voice?: string;
  } | null>(null);
  const [isInitializingAssistant, setIsInitializingAssistant] = useState(false);
  const [tokenCreationStage, setTokenCreationStage] = useState<'requesting' | 'validating' | 'configuring' | null>(null);
  const [connectionDiagnosticStage, setConnectionDiagnosticStage] = useState<'auth' | 'database' | 'tools' | 'connecting' | null>(null);
  const [recentEventResults, setRecentEventResults] = useState<VoiceEventResultSummary[]>([]);
  const [shouldUseAltInit, setShouldUseAltInit] = useState(true); // Enable alternative initialization by default
  const [sessionTypeErrorSeen, setSessionTypeErrorSeen] = useState(false);
  const altInitRetryRef = useRef(false);

  // Enhanced transport selection logic with proper WebRTC support
   const getTransportMode = () => {
     // Prefer WebRTC because the relay path surfaces additional SDK bugs.
     const forceWebRtc = forceTransport === 'webrtc' || appEnv.featureFlags.forceWebRtcTransport;
     if (forceWebRtc) {
       if (enableVerboseLogging || appEnv.isDevelopment) {
         console.log('🔧 FORCING WebRTC mode via configuration toggle');
       }
       return false; // false = WebRTC
     }

     const forceWebSocket = forceTransport === 'websocket' || !appEnv.featureFlags.useWebRtc;
     if (forceWebSocket) {
       if (enableVerboseLogging || appEnv.isDevelopment) {
         console.log('ℹ️ Using WebSocket relay based on configuration');
       }
       return true; // true = WebSocket relay
     }

     return false; // Default to WebRTC for lowest latency voice path
   };

  const useRelay = getTransportMode();
  const currentTransport = useRelay ? 'websocket' : 'webrtc';

  // Check if this instance should be active
  const shouldBeActive = isInstanceActive(instanceId);
  const activeInstanceId = getActiveInstanceId();

  // Enhanced debug logging with transport details and WebRTC status
  useEffect(() => {
    const logLevel = enableVerboseLogging || appEnv.featureFlags.enableWebRtcDebug ? 'verbose' : 'normal';

    if (logLevel === 'verbose' || appEnv.isDevelopment) {
      console.log(`🔧 Voice Assistant [${instanceId}] Transport Configuration:`, {
        currentTransport,
        useRelay,
        transportReason: forceTransport ? `Forced: ${forceTransport}` :
          appEnv.featureFlags.forceWebRtcTransport ? 'Force WebRTC flag enabled' :
          !appEnv.featureFlags.directRealtime ? 'DirectRealtime disabled' :
          !appEnv.featureFlags.useWebRtc ? 'WebRTC disabled' : 'Default selection',
        featureFlags: {
          directRealtime: appEnv.featureFlags.directRealtime,
          useWebRtc: appEnv.featureFlags.useWebRtc,
          forceWebRtcTransport: appEnv.featureFlags.forceWebRtcTransport,
          disableWebRtcFallback: appEnv.featureFlags.disableWebRtcFallback,
          enableWebRtcDebug: appEnv.featureFlags.enableWebRtcDebug
        },
        instanceId,
        shouldBeActive,
        activeInstanceId,
        authStatus: authenticationStatus.isAuthenticated ? 'authenticated' : 'pending'
      });
    }
  }, [instanceId, shouldBeActive, activeInstanceId, authenticationStatus.isAuthenticated, currentTransport, useRelay, forceTransport, enableVerboseLogging]);

  const addActivity = useCallback((activity: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const activityWithTime = `${timestamp}: ${activity}`;
    setRecentActivities((prev) => [activityWithTime, ...prev.slice(0, 4)]);
  }, []);

  const formatEventType = useCallback((eventType?: string | null) => {
    if (!eventType) return 'Event';
    return eventType
      .split('_')
      .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
      .join(' ');
  }, []);

  const formatEventTimestamp = useCallback((timestamp?: string | null) => {
    if (!timestamp) return 'Unknown time';
    const date = new Date(timestamp);
    if (Number.isNaN(date.getTime())) {
      return 'Unknown time';
    }
    return date.toLocaleString(undefined, {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });
  }, []);

  const handleNavigateToEvent = useCallback(
    (eventId: string) => {
      if (!eventId) return;
      setActiveView('Events');
      clearViewFilters();
      window.dispatchEvent(
        new CustomEvent('highlight-inventory-event', {
          detail: { eventId },
        })
      );
      // Allow events view to mount/update before attempting to scroll
      window.setTimeout(() => {
        const row = document.getElementById(`inventory-event-${eventId}`);
        row?.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }, 250);
    },
    [setActiveView, clearViewFilters]
  );

  // Use the modern realtime voice hook - always initialize, control via UI
  const {
    isConnected,
    isListening,
    error,
    transcript,
    response,
    toolCalls,
    connect,
    disconnect,
    sendMessage,
    clearConversation,
    updateClientConfig,
    getConnectionStatus,
    // New UI component support
    interimTranscript,
    voiceState,
    currentlyExecutingTool,
  } = useModernRealtimeVoice({
    model: appEnv.realtime.model,
    voice: appEnv.realtime.voice as 'alloy' | 'echo' | 'shimmer',
    enableDebugLogs: enableVerboseLogging || appEnv.featureFlags.enableWebRtcDebug || appEnv.isDevelopment,
    autoConnect: false, // We'll connect manually after getting the token or hitting the relay
    useAlternativeInitialization: shouldUseAltInit,
    ...(useRelay
      ? {
          relayUrl: '/api/realtime-relay',
          transport: 'websocket' as const,
          useInsecureApiKey: true,
        }
      : {
          ephemeralToken: ephemeralToken || undefined,
          transport: 'webrtc' as const,
          forceTransport: 'webrtc' as const,
          // CRITICAL: Do NOT set relayUrl for WebRTC mode - it forces WebSocket
        }),
    disableFallback: useRelay
      ? disableFallback || appEnv.featureFlags.disableWebRtcFallback
      : true,
    forceTransport: forceTransport || (useRelay ? 'websocket' : 'webrtc'),
  });

  // Register this instance with the singleton context (only on mount)
  useEffect(() => {
    const voiceInstance = {
      id: instanceId,
      isActive: true,
      isConnected: false,
      isListening: false,
      connect: () => Promise.resolve(true),
      disconnect: () => Promise.resolve(),
      sendMessage: () => Promise.resolve(),
    };

    registerInstance(voiceInstance);

    // Cleanup on unmount
    return () => {
      unregisterInstance(instanceId);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // instanceId, registerInstance, unregisterInstance are stable - intentionally excluded

  // Enhanced ephemeral token creation with correlation IDs and detailed diagnostics
  const createAssistantSession = useCallback(async (): Promise<string | null> => {
    const correlationId = `voice_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    try {
      setIsInitializingAssistant(true);
      setTokenCreationStage('requesting');

      console.log(`🎯 [${correlationId}] Creating voice assistant session:`, {
        instanceId,
        transport: currentTransport,
        model: appEnv.realtime.model,
        voice: 'alloy',
        timestamp: new Date().toISOString()
      });

      addActivity(`Creating session [${correlationId}]...`);

      // For WebRTC mode, we need an ephemeral token
      const model = appEnv.realtime.model;
      const requestStart = Date.now();

      console.log(`📤 [${correlationId}] Requesting ephemeral token:`, {
        url: `/api/voice/ephemeral-token`,
        method: 'POST',
        model,
        voice: 'alloy'
      });

      const response = await fetch(`/api/voice/ephemeral-token?model=${encodeURIComponent(model)}&voice=alloy`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Correlation-ID': correlationId,
          'X-Voice-Instance': instanceId,
        },
      });

      const responseTime = Date.now() - requestStart;
      const totalTime = Date.now() - startTime;

      console.log(`📥 [${correlationId}] Token response received:`, {
        status: response.status,
        statusText: response.statusText,
        responseTime,
        totalTime,
        contentType: response.headers.get('content-type'),
        hasCorrelationHeader: Boolean(response.headers.get('x-correlation-id'))
      });

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unable to read error response');
        console.error(`❌ [${correlationId}] Token request failed:`, {
          status: response.status,
          statusText: response.statusText,
          errorText: errorText.substring(0, 200),
          responseTime,
          totalTime
        });

        let enhancedError = `Ephemeral token request failed: ${response.status}`;
        let remediation = 'Check server logs and API configuration';

        if (response.status === 401) {
          enhancedError = 'Authentication failed for voice token request';
          remediation = 'Verify OpenAI API key is configured and valid';
        } else if (response.status === 429) {
          enhancedError = 'Rate limit exceeded for voice token request';
          remediation = 'Wait a moment before trying again';
        } else if (response.status === 500) {
          enhancedError = 'Server error during voice token creation';
          remediation = 'Check OpenAI API status and server configuration';
        } else if (response.status === 404) {
          enhancedError = 'Voice token endpoint not found';
          remediation = 'Verify Express server is running and endpoints are configured';
        }

        addActivity(`❌ Token request failed: ${response.status}`);
        throw new Error(`${enhancedError}${errorText ? ` - ${errorText}` : ''} | ${remediation}`);
      }

      setTokenCreationStage('validating');
      
      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        console.error(`❌ [${correlationId}] Token response parse error:`, parseError);
        addActivity(`❌ Invalid token response format`);
        throw new Error('Invalid JSON response from token endpoint');
      }

      console.log(`📊 [${correlationId}] Token response data:`, {
        hasError: Boolean(data.error),
        hasClientSecret: Boolean(data.client_secret),
        hasId: Boolean(data.id),
        hasModel: Boolean(data.model),
        serverCorrelationId: data.correlationId,
        responseKeys: Object.keys(data),
        performance: data.performance
      });

      if (data.error) {
        console.error(`❌ [${correlationId}] Token creation error:`, data.error);
        addActivity(`❌ Token error: ${data.error}`);
        throw new Error(`Ephemeral token error: ${data.error}`);
      }

      const token = data.client_secret?.value || data.client_secret || data.value;

      if (!token) {
        console.error(`❌ [${correlationId}] Missing client secret in response:`, data);
        addActivity(`❌ Missing token in response`);
        throw new Error('Ephemeral token response did not include a client secret');
      }

      // Validate token format
      const isValidFormat = typeof token === 'string' && token.length > 10;
      const isEphemeralToken = token.startsWith('ek_');

      console.log(`✅ [${correlationId}] Token validation:`, {
        tokenLength: token.length,
        tokenPrefix: token.substring(0, 8),
        isValidFormat,
        isEphemeralToken,
        tokenType: isEphemeralToken ? 'ephemeral' : 'other',
        expiresAt: data.expires_at,
        sessionId: data.id,
        model: data.model,
        totalTime
      });

      // Validate token format - ephemeral tokens should start with 'ek_'
      if (!isValidFormat) {
        console.warn(`⚠️ [${correlationId}] Unexpected token format - expected 'ek_' prefix`);
        addActivity(`⚠️ Unusual token format received`);
      } else {
        console.log(`✅ [${correlationId}] Token format validated (ek_* prefix)`);
      }

      setTokenCreationStage('configuring');
      
      // Set token in state
      setEphemeralToken(token);
      console.log(`💾 [${correlationId}] Token saved to state:`, {
        tokenPrefix: token.substring(0, 8),
        tokenLength: token.length
      });

      // Set session metadata
      setAssistantSession({
        id: data.id || `session_${correlationId}`,
        model: data.model || appEnv.realtime.model,
        voice: data.voice || 'alloy',
      });

      addActivity(`✅ Session created [${data.id}]`);

      console.log(`✅ [${correlationId}] Voice assistant session created successfully:`, {
        sessionId: data.id,
        model: data.model,
        voice: data.voice,
        totalTime,
        instanceId,
        tokenReady: true
      });

      // Return both the token and a confirmation
      return token;
    } catch (error) {
      const totalTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown assistant session error';

      console.error(`❌ [${correlationId}] Session creation failed:`, {
        error: errorMessage,
        type: error instanceof Error ? error.name : 'unknown',
        totalTime,
        instanceId
      });

      addActivity(`❌ Session creation failed`);

      // Provide enhanced error context
      let contextualError = errorMessage;
      if (errorMessage.includes('Authentication failed') || errorMessage.includes('401')) {
        contextualError = 'Authentication failed - check OpenAI API key configuration';
      } else if (errorMessage.includes('Rate limit') || errorMessage.includes('429')) {
        contextualError = 'Rate limit exceeded - try again in a moment';
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        contextualError = 'Network error - check internet connection and server status';
      } else if (errorMessage.includes('500')) {
        contextualError = 'Server error - check OpenAI API status and server logs';
      }

      onError?.(`Failed to create assistant session: ${contextualError}`);
      return null;
    } finally {
      setIsInitializingAssistant(false);
      setTokenCreationStage(null);
    }
  }, [addActivity, onError, instanceId, currentTransport]);

  // Enhanced connection handler with comprehensive diagnostics and correlation IDs
  const handleConnect = useCallback(async () => {
    const correlationId = `voice_connect_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    try {
      console.log(`🔗 [${correlationId}] Starting voice assistant connection:`, {
        instanceId,
        transport: currentTransport,
        useRelay,
        hasEphemeralToken: Boolean(ephemeralToken),
        authStatus: authenticationStatus.isAuthenticated ? 'authenticated' : 'pending',
        initializationStrategy: shouldUseAltInit ? 'alternative' : 'standard',
        timestamp: new Date().toISOString()
      });

      // Pre-connection validation and diagnostics
      setConnectionDiagnosticStage('auth');
      
      if (!authenticationStatus.isAuthenticated) {
        console.error(`❌ [${correlationId}] Authentication required for voice operations`);
        addActivity(`❌ Authentication required`);
        onError?.('Cannot connect: authentication required for voice inventory operations');
        setConnectionDiagnosticStage(null);
        return;
      }

      addActivity(`Running diagnostics [${correlationId}]...`);

      // Step 1: Validate database connectivity
      setConnectionDiagnosticStage('database');
      console.log(`🗄️ [${correlationId}] Testing database connectivity...`);
      try {
        const dbTestStart = Date.now();
        const { data, error } = await supabase
          .from('inventory_events')
          .select('id')
          .limit(1);

        const dbTestTime = Date.now() - dbTestStart;

        if (error && error.code !== 'PGRST116') {
          console.error(`❌ [${correlationId}] Database test failed:`, {
            error: error.message,
            code: error.code,
            hint: error.hint,
            details: error.details,
            testTime: dbTestTime
          });
          throw new Error(`Database connectivity test failed: ${error.message}`);
        }

        console.log(`✅ [${correlationId}] Database connectivity verified:`, {
          testTime: dbTestTime,
          recordsFound: data?.length || 0,
          errorCode: error?.code || 'none'
        });
        addActivity('✅ Database connectivity verified');
      } catch (dbError) {
        const dbTestTime = Date.now() - startTime;
        const message = dbError instanceof Error ? dbError.message : 'Database connection failed';
        console.error(`❌ [${correlationId}] Database connectivity failed:`, {
          error: message,
          testTime: dbTestTime
        });
        addActivity(`❌ Database test failed`);
        onError?.(`Database connectivity check failed: ${message}`);
        setConnectionDiagnosticStage(null);
        return;
      }

      // Step 2: Tool availability validation
      setConnectionDiagnosticStage('tools');
      console.log(`🔧 [${correlationId}] Validating inventory tools...`);
      addActivity('🔧 Validating tools...');

      // Step 3: Transport-specific setup
      if (!useRelay) {
        console.log(`🎯 [${correlationId}] WebRTC mode - checking ephemeral token...`);
        let token = ephemeralToken;

        if (!token) {
          console.log(`🎫 [${correlationId}] No token available - creating new session...`);
          addActivity('🎫 Creating token...');
          token = await createAssistantSession();
          if (!token) {
            console.error(`❌ [${correlationId}] Token creation failed - aborting connection`);
            addActivity('❌ Token creation failed');
            onError?.('Failed to create session token');
            return;
          }
          console.log(`✅ [${correlationId}] Token created successfully:`, {
            tokenPrefix: token.substring(0, 8),
            tokenLength: token.length
          });
        } else {
          console.log(`✅ [${correlationId}] Using existing ephemeral token:`, {
            tokenPrefix: token.substring(0, 8),
            tokenLength: token.length
          });
        }

        if (token) {
          console.log(`🔄 [${correlationId}] Updating client config with token...`);
          addActivity('🔄 Configuring credentials...');

          // CRITICAL FIX: Pass BOTH apiKey and ephemeralToken
          // - apiKey (sk-*) is required for /v1/realtime/calls SDP endpoint
          // - ephemeralToken (ek_*) is used for WebRTC peer connection
          const apiKey = import.meta.env.VITE_OPENAI_API_KEY;
          console.log(`🔧 [${correlationId}] Configuring WebRTC client with:`, {
            hasApiKey: Boolean(apiKey),
            hasEphemeralToken: Boolean(token),
            apiKeyPrefix: apiKey?.substring(0, 10),
            ephemeralTokenPrefix: token?.substring(0, 10)
          });
          await updateClientConfig({
            apiKey,
            ephemeralToken: token
          });

          // Wait for state propagation - increased from 0 to 50ms
          console.log(`⏳ [${correlationId}] Waiting for credential configuration to propagate...`);
          await new Promise((resolve) => setTimeout(resolve, 50));

          // Verify credentials were set in the client
          const status = getConnectionStatus();
          if (!status || typeof status === 'boolean' || !status.hasCredentials) {
            console.error(`❌ [${correlationId}] Credential configuration failed:`, {
              status,
              hasCredentials: typeof status === 'object' ? status.hasCredentials : false,
              credentialType: typeof status === 'object' ? status.credentialType : undefined,
              patchStatus: typeof status === 'object' ? status.patchApplied : undefined
            });
            addActivity('❌ Credential configuration failed');
            onError?.('Failed to configure credentials in voice client. This may prevent proper connection to OpenAI Realtime API.');
            return;
          }

          console.log(`✅ [${correlationId}] Credentials configured successfully:`, {
            hasCredentials: status.hasCredentials,
            credentialType: status.credentialType,
            patchApplied: status.patchApplied
          });
          addActivity('✅ Credentials ready');
        }
      } else {
        console.log(`🌐 [${correlationId}] WebSocket relay mode - no token required`);
        addActivity('🌐 Using WebSocket relay');
      }

      // Step 4: Attempt connection
      setConnectionDiagnosticStage('connecting');
      console.log(`🚀 [${correlationId}] Attempting voice assistant connection...`);
      addActivity('🚀 Connecting...');

      if (shouldUseAltInit) {
        console.warn(`⚠️ [${correlationId}] Alternative initialization enabled - syncing client config`);
        addActivity('⚠️ Switching to alternative initialization');
        await updateClientConfig({ useAlternativeInitialization: true });
      } else {
        await updateClientConfig({ useAlternativeInitialization: false });
      }

      const connectionStart = Date.now();
      const success = await connect();
      const connectionTime = Date.now() - connectionStart;
      const totalTime = Date.now() - startTime;

      if (success) {
        const finalStatus = getConnectionStatus();
        console.log(`✅ [${correlationId}] Voice assistant connected successfully:`, {
          connectionTime,
          totalTime,
          transport: currentTransport,
          instanceId,
          patchStatus: typeof finalStatus === 'object' ? finalStatus.patchApplied : 'unknown'
        });
        addActivity(`✅ Connected [${currentTransport}]`);
      } else {
        const finalStatus = getConnectionStatus();
        console.error(`❌ [${correlationId}] Voice assistant connection failed:`, {
          connectionTime,
          totalTime,
          transport: currentTransport,
          lastError: error,
          patchStatus: typeof finalStatus === 'object' ? finalStatus.patchApplied : 'unknown'
        });
        addActivity(`❌ Connection failed`);

        // Provide specific guidance based on error content
        let errorGuidance = 'Connection failed - voice assistant did not connect successfully';
        if (error?.includes('session.type') || error?.includes('Unknown parameter')) {
          errorGuidance = 'OpenAI API rejected connection due to session.type parameter. This is a known SDK bug. Check console for DataChannel patch logs and try reconnecting.';
        } else if (error?.includes('database') || error?.includes('RLS')) {
          errorGuidance = 'Database access issue detected. Note: Voice authentication is working, but database permissions may need verification.';
        }

        onError?.(errorGuidance);
      }
      
      setConnectionDiagnosticStage(null);
    } catch (error) {
      const totalTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      console.error(`❌ [${correlationId}] Connection process failed:`, {
        error: errorMessage,
        type: error instanceof Error ? error.name : 'unknown',
        totalTime,
        transport: currentTransport,
        instanceId
      });

      addActivity(`❌ Connection failed`);

      // Provide specific guidance based on error type
      let enhancedError = errorMessage;
      let remediation = 'Check logs for detailed error information';

      if (errorMessage.includes('authentication') || errorMessage.includes('token') || errorMessage.includes('401')) {
        enhancedError = 'Authentication failed';
        remediation = 'Refresh your login and check API key configuration';
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch') || errorMessage.includes('NetworkError')) {
        enhancedError = 'Network connection failed';
        remediation = 'Check your internet connection and server status';
      } else if (errorMessage.includes('database') || errorMessage.includes('RLS') || errorMessage.includes('PGRST')) {
        enhancedError = 'Database access failed';
        remediation = 'Check database permissions and RLS policies';
      } else if (errorMessage.includes('timeout') || errorMessage.includes('TimeoutError')) {
        enhancedError = 'Connection timeout';
        remediation = 'Check server performance and network latency';
      } else if (errorMessage.includes('500') || errorMessage.includes('502') || errorMessage.includes('503')) {
        enhancedError = 'Server error';
        remediation = 'Check server status and API endpoint availability';
      }

      onError?.(`Connection failed: ${enhancedError} | ${remediation}`);
      setConnectionDiagnosticStage(null);
    }
  }, [useRelay, ephemeralToken, createAssistantSession, connect, onError, addActivity, updateClientConfig, getConnectionStatus, authenticationStatus.isAuthenticated, instanceId, currentTransport, error, shouldUseAltInit]);

  // Disconnect from voice assistant
  const handleDisconnect = useCallback(async () => {
    await disconnect();
    if (!useRelay) {
      setAssistantSession(null);
      setEphemeralToken(null);
    }
    addActivity('Disconnected from voice assistant');
  }, [addActivity, disconnect, useRelay]);

  // Handle tool calls for inventory events
  useEffect(() => {
    if (toolCalls.length > 0) {
      const latestToolCall = toolCalls[toolCalls.length - 1];
      
      // Process the tool call result
      try {
        const result = typeof latestToolCall.result === 'string' 
          ? JSON.parse(latestToolCall.result)
          : latestToolCall.result;

        // Create voice event data if applicable
        if (latestToolCall.toolName === 'create_inventory_event' && result.success) {
          const args = (latestToolCall.args || {}) as Record<string, unknown>;

          const rawEventType = typeof args.event_type === 'string' ? args.event_type : 'receiving';
          const allowedEventTypes: VoiceEventData['event_type'][] = ['receiving', 'disposal', 'physical_count', 'sale'];
          const eventType = allowedEventTypes.includes(rawEventType as VoiceEventData['event_type'])
            ? rawEventType as VoiceEventData['event_type']
            : 'receiving';

          const rawUnit = typeof args.unit === 'string' ? args.unit.toLowerCase() : 'units';
          const allowedUnits: VoiceEventData['unit'][] = ['lbs', 'kg', 'cases', 'units'];
          const unit = allowedUnits.includes(rawUnit as VoiceEventData['unit'])
            ? rawUnit as VoiceEventData['unit']
            : 'units';

          const productName = typeof args.product_name === 'string' ? args.product_name : '';
          const quantity = typeof args.quantity === 'number' ? args.quantity : Number(args.quantity);

          const rawTemperatureUnit = typeof args.temperature_unit === 'string'
            ? args.temperature_unit.toLowerCase()
            : undefined;
          const temperatureUnit = rawTemperatureUnit === 'celsius' || rawTemperatureUnit === 'fahrenheit'
            ? rawTemperatureUnit
            : undefined;

          if (!productName || Number.isNaN(quantity)) {
            console.warn('Skipping event callback due to invalid product data', { productName, quantity });
          } else {
            const resultData = (result.data || {}) as Record<string, unknown>;
            const occurredAt = typeof resultData.occurred_at === 'string'
              ? resultData.occurred_at
              : new Date().toISOString();

            const metadata: Record<string, unknown> = {
              tool_response: resultData,
              captured_at: new Date().toISOString(),
              created_by_user_id: actualUserId,
            };

            if (typeof args.category === 'string') metadata.category = args.category;
            if (typeof args.unit_price === 'number') metadata.unit_price = args.unit_price;
            if (typeof args.total_amount === 'number') metadata.total_amount = args.total_amount;
            if (typeof args.lot_number === 'string') metadata.lot_number = args.lot_number;
            if (typeof args.expiration_date === 'string') metadata.expiration_date = args.expiration_date;
            if (typeof args.unit === 'string' && args.unit.toLowerCase() !== unit) metadata.original_unit = args.unit;
            if (typeof args.temperature_unit === 'string') metadata.temperature_unit = args.temperature_unit;

            const eventData: VoiceEventData = {
              event_type: eventType,
              product_name: productName,
              quantity,
              unit,
              vendor_name: typeof args.vendor === 'string' ? args.vendor : undefined,
              customer_name: typeof args.customer === 'string' ? args.customer : undefined,
              condition: typeof args.condition === 'string' ? args.condition as 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Damaged' : undefined,
              temperature: typeof args.temperature === 'number' ? args.temperature : undefined,
              notes: typeof args.notes === 'string' ? args.notes : undefined,
              occurred_at: occurredAt,
              temperature_unit: temperatureUnit,
              metadata,
            };

            onEventCreated?.(eventData);

            // CRITICAL FIX: Emit toast notification event (client-side only)
            window.dispatchEvent(new CustomEvent('voice-inventory-created', {
              detail: {
                productName,
                quantity,
                unit,
                eventType,
                lotNumber: metadata.lot_number as string | undefined,
                timestamp: Date.now()
              }
            }));
          }
        }

        if (latestToolCall.toolName === 'query_recent_events') {
          if (result?.success && result?.data) {
            const dataPayload = result.data as Record<string, unknown>;
            const recentEventsRaw = Array.isArray(dataPayload.recent_events)
              ? dataPayload.recent_events
              : [];

            const normalizedEvents = recentEventsRaw
              .map((event) => {
                if (typeof event !== 'object' || event === null) return null;
                const record = event as Record<string, unknown>;
                const rawId = record.id;
                const id = typeof rawId === 'string' ? rawId : rawId != null ? String(rawId) : null;
                if (!id) return null;

                const metadata = (record.metadata as Record<string, unknown> | null) ?? null;
                const unit =
                  typeof record.unit === 'string'
                    ? record.unit
                    : typeof metadata?.unit === 'string'
                      ? (metadata.unit as string)
                      : null;

                const quantityRaw = record.quantity;
                const quantity =
                  typeof quantityRaw === 'number'
                    ? quantityRaw
                    : typeof quantityRaw === 'string' && quantityRaw.trim() !== ''
                      ? Number(quantityRaw)
                      : null;

                const productName =
                  typeof record.product_name === 'string'
                    ? record.product_name
                    : typeof record.name === 'string'
                      ? record.name
                      : null;

                return {
                  id,
                  product_name: productName,
                  event_type: typeof record.event_type === 'string' ? record.event_type : null,
                  quantity,
                  unit,
                  occurred_at: typeof record.occurred_at === 'string' ? record.occurred_at : null,
                  created_at: typeof record.created_at === 'string' ? record.created_at : null,
                } satisfies VoiceEventResultSummary;
              })
              .filter(Boolean) as VoiceEventResultSummary[];

            setRecentEventResults(normalizedEvents.slice(0, 5));
          } else {
            setRecentEventResults([]);
          }
        }

        addActivity(`Tool executed: ${latestToolCall.toolName}`);
      } catch (error) {
        console.error('Error processing tool call result:', error);
      }
    }
  }, [toolCalls, onEventCreated, _userId, addActivity, actualUserId]);

  // Enhanced error handling for database access and authentication issues
  useEffect(() => {
    if (error) {
      let enhancedError = error;
      let category = 'general';
      const isSessionTypeError = error.includes('session.type') || error.includes('Unknown parameter');

      // Categorize and enhance error messages
      if (isSessionTypeError) {
        category = 'openai_api';
        setSessionTypeErrorSeen(true);
        if (!shouldUseAltInit && !altInitRetryRef.current) {
          altInitRetryRef.current = true;
          console.warn('⚠️ Session.type error detected, retrying with alternative initialization strategy');
          addActivity('⚠️ Session.type error detected - enabling alternative initialization');
          setShouldUseAltInit(true);
          enhancedError = `OpenAI API Error: ${error}. Retrying automatically with alternative initialization strategy...`;
          setTimeout(() => {
            void handleConnect();
          }, 0);
        } else if (shouldUseAltInit) {
          enhancedError = `OpenAI API Error: ${error}. Alternative initialization was attempted automatically and also failed. Please review console logs for constructor patch status.`;
        } else {
          enhancedError = `OpenAI API Error: ${error}. This is a known SDK bug where the @openai/agents library injects an invalid 'type: "realtime"' field. The DataChannel patch should prevent this. Check browser console for patch application logs.`;
        }
      } else if (error.includes('authentication') || error.includes('JWT') || error.includes('401')) {
        category = 'authentication';
        enhancedError = 'Authentication failed - please log in and reconnect voice assistant';
      } else if (error.includes('RLS') || error.includes('permission') || error.includes('403')) {
        category = 'database_permissions';
        enhancedError = 'Database access denied - check RLS policies for voice operations. Note: Voice authentication may be working, but database-level permissions need verification.';
      } else if (error.includes('connection') || error.includes('network')) {
        category = 'network';
        enhancedError = 'Connection failed - check network connectivity and try again';
      } else if (error.includes('tool') || error.includes('function')) {
        category = 'tool_execution';
        enhancedError = 'Voice tool execution failed - check tool availability and authentication';
      } else {
        setSessionTypeErrorSeen(false);
        altInitRetryRef.current = false;
      }

      console.error(`[Voice Error - ${category}]:`, enhancedError);
      onError?.(enhancedError);
      addActivity(`Error [${category}]: ${enhancedError}`);
    } else {
      setSessionTypeErrorSeen(false);
      altInitRetryRef.current = false;
    }
  }, [error, onError, addActivity, shouldUseAltInit, handleConnect]);

  // Handle authentication status errors
  useEffect(() => {
    if (authenticationStatus.error) {
      onError?.(authenticationStatus.error);
      addActivity(`Auth Error: ${authenticationStatus.error}`);
    }
  }, [authenticationStatus.error, onError, addActivity]);

  useEffect(() => {
    if (isConnected) {
      setSessionTypeErrorSeen(false);
      altInitRetryRef.current = false;
    }
  }, [isConnected]);

  const connectionStatusSnapshot = getConnectionStatus();
  const initializationStrategyLabel = shouldUseAltInit ? 'Alternative' : 'Standard';
  const clientInitializationStrategy =
    typeof connectionStatusSnapshot === 'object' && connectionStatusSnapshot !== null && 'initializationStrategy' in connectionStatusSnapshot
      ? (connectionStatusSnapshot as { initializationStrategy?: string }).initializationStrategy
      : undefined;
  const patchApplied =
    typeof connectionStatusSnapshot === 'object' && connectionStatusSnapshot !== null && 'patchApplied' in connectionStatusSnapshot
      ? (connectionStatusSnapshot as { patchApplied?: boolean }).patchApplied
      : undefined;

  // Test message function
  const sendTestMessage = useCallback(async () => {
    try {
      await sendMessage("Hi, how much COD do we have in stock?");
      addActivity('Sent test message');
    } catch (error) {
      console.error('Failed to send test message:', error);
    }
  }, [sendMessage, addActivity]);

  // Only render if this is the active instance
  if (!shouldBeActive && activeInstanceId && activeInstanceId !== instanceId) {
    return (
      <div className="bg-gray-100 rounded-lg shadow-lg p-6 max-w-2xl mx-auto">
        <div className="text-center text-gray-600">
          <Brain className="w-8 h-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm">Voice Assistant is active in another location</p>
          <p className="text-xs text-gray-500">Instance: {instanceId} (Inactive)</p>
          <p className="text-xs text-gray-500">Active: {activeInstanceId}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-2xl mx-auto w-full overflow-hidden">
      <div className="flex items-center justify-between mb-6 min-w-0">
        <div className="flex items-center space-x-2 min-w-0 flex-1">
          <Brain className="w-6 h-6 text-blue-600 flex-shrink-0" />
          <h2 className="text-xl font-semibold text-gray-900 break-words min-w-0 flex-1">
            Modern Voice Assistant
            <span className="text-xs text-gray-500 ml-2 break-words">({instanceId})</span>
          </h2>
          <Zap className="w-5 h-5 text-yellow-500 flex-shrink-0" />
        </div>

        <div className="flex items-center space-x-3 flex-shrink-0">
          <div className="flex items-center space-x-1">
            <span className={`text-xs px-2 py-1 rounded-full font-medium break-words truncate ${
              currentTransport === 'webrtc'
                ? 'bg-blue-100 text-blue-700'
                : 'bg-purple-100 text-purple-700'
            }`}>
              {currentTransport === 'webrtc' ? 'WebRTC' : 'WebSocket'}
            </span>
          </div>

          <div className="flex items-center space-x-2">
            {isConnected ? (
              <Wifi className="w-5 h-5 text-green-500 flex-shrink-0" />
            ) : (
              <WifiOff className="w-5 h-5 text-red-500 flex-shrink-0" />
            )}
            <span className={`text-sm font-medium break-words ${
              isConnected ? 'text-green-600' : 'text-red-600'
            }`}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>
      </div>

      {/* Session Info (only shown when connected) */}
      {assistantSession && isConnected && (
        <div className="mb-4 p-3 bg-indigo-50 rounded-lg border border-indigo-200">
          <div className="flex items-center gap-2 mb-1">
            <Brain className="w-4 h-4 text-indigo-600" />
            <span className="text-sm font-semibold text-indigo-900">Active Session</span>
          </div>
          <div className="text-xs text-indigo-700 space-y-0.5">
            <div>ID: {assistantSession.id}</div>
            <div>
              Model: {assistantSession.model}
              {assistantSession.voice ? ` · Voice: ${assistantSession.voice}` : ''}
            </div>
          </div>
        </div>
      )}

      {/* Multi-Stage Token Creation Progress */}
      {isInitializingAssistant && tokenCreationStage && (
        <div className="mb-4 p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400">
          <div className="flex items-center gap-3 mb-3">
            <Loader2 className="w-5 h-5 animate-spin text-blue-600" />
            <div className="flex-1">
              <div className="text-sm font-semibold text-blue-900">
                {tokenCreationStage === 'requesting' && 'Requesting secure session token...'}
                {tokenCreationStage === 'validating' && 'Validating credentials...'}
                {tokenCreationStage === 'configuring' && 'Configuring voice client...'}
              </div>
              <div className="text-xs text-blue-600 mt-0.5">
                This usually takes 2-4 seconds
              </div>
            </div>
          </div>
          <div className="w-full bg-blue-200 rounded-full h-2 overflow-hidden">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-500"
              style={{ 
                width: tokenCreationStage === 'requesting' ? '33%' : 
                       tokenCreationStage === 'validating' ? '66%' : '95%' 
              }}
            />
          </div>
        </div>
      )}

      {/* Connection Diagnostic Progress */}
      {connectionDiagnosticStage && (
        <div className="mb-4 p-4 bg-indigo-50 rounded-lg border-l-4 border-indigo-400">
          <div className="flex items-center gap-3 mb-3">
            <Loader2 className="w-5 h-5 animate-spin text-indigo-600" />
            <div className="flex-1">
              <div className="text-sm font-semibold text-indigo-900">
                {connectionDiagnosticStage === 'auth' && 'Verifying authentication...'}
                {connectionDiagnosticStage === 'database' && 'Testing database connectivity...'}
                {connectionDiagnosticStage === 'tools' && 'Validating inventory tools...'}
                {connectionDiagnosticStage === 'connecting' && 'Establishing voice connection...'}
              </div>
              <div className="text-xs text-indigo-600 mt-0.5">
                Running pre-connection diagnostics
              </div>
            </div>
          </div>
          <div className="w-full bg-indigo-200 rounded-full h-2 overflow-hidden">
            <div 
              className="bg-indigo-600 h-2 rounded-full transition-all duration-500"
              style={{ 
                width: connectionDiagnosticStage === 'auth' ? '25%' : 
                       connectionDiagnosticStage === 'database' ? '50%' :
                       connectionDiagnosticStage === 'tools' ? '75%' : '90%'
              }}
            />
          </div>
        </div>
      )}

      {/* Prominent Connection Status Banner */}
      <div className={`mb-4 p-4 rounded-lg border-2 transition-all ${
        isConnected 
          ? 'bg-green-50 border-green-300' 
          : 'bg-red-50 border-red-300'
      }`}>
        <div className="flex items-center gap-3">
          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
            isConnected ? 'bg-green-500' : 'bg-red-500'
          }`}>
            {isConnected ? (
              <Wifi className="w-6 h-6 text-white" />
            ) : (
              <WifiOff className="w-6 h-6 text-white" />
            )}
          </div>
          <div className="flex-1">
            <div className={`font-semibold text-base break-words ${
              isConnected ? 'text-green-900' : 'text-red-900'
            }`}>
              {isConnected ? 'Voice Assistant Connected' : 'Disconnected'}
            </div>
            <div className={`text-sm break-words ${
              isConnected ? 'text-green-700' : 'text-red-700'
            }`}>
              {isConnected
                ? `Ready to process voice commands via ${currentTransport.toUpperCase()}`
                : 'Voice commands are unavailable'
              }
            </div>
          </div>
      </div>
    </div>

      {/* Connection Diagnostics (only show when there's an error) */}
      {error && error.includes('session.type') && (
        <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h4 className="text-sm font-semibold text-yellow-800 mb-2">🔧 Diagnostic Information</h4>
          <div className="text-xs text-yellow-700 space-y-1">
            <p><strong>Issue:</strong> OpenAI API rejected session.type parameter</p>
            <p><strong>Cause:</strong> Known SDK bug in @openai/agents library</p>
            <p><strong>Expected Fix:</strong> DataChannel patch should strip this field</p>
            <p><strong>Action:</strong> Check browser console for "DATACHANNEL SEND INTERCEPTED" logs</p>
            <p><strong>If patch not applied:</strong> Try disconnecting and reconnecting</p>
            <p>
              <strong>Initialization Strategy:</strong> {clientInitializationStrategy ?? initializationStrategyLabel}{' '}
              {shouldUseAltInit
                ? '(Alternative: all session config provided upfront to avoid SDK mutation)'
                : '(Standard: post-connect session.update flow)'}
            </p>
            <p>
              <strong>Patch Status:</strong> {patchApplied === true ? 'Constructor/DataChannel patches active' : patchApplied === false ? 'Patches pending' : 'Unknown'}
            </p>
            <p className="mt-2 pt-2 border-t border-yellow-300">
              <strong>Note:</strong> Database authentication is working correctly (user: {user?.email ?? 'unknown'}).
              The issue is with OpenAI API connection, not database access.
            </p>
          </div>
        </div>
      )}

      {/* Enhanced Authentication Warning with Action Button */}
      {!authenticationStatus.isAuthenticated && !authenticationStatus.isValidating && (
        <div className="mb-4 p-4 bg-yellow-50 border-l-4 border-yellow-400 rounded-lg">
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
            <div className="flex-1">
              <div className="text-sm font-semibold text-yellow-900 mb-1">
                Authentication Required
              </div>
              <div className="text-sm text-yellow-800 mb-3">
                Voice inventory operations require authentication to protect your data.
              </div>
              {authenticationStatus.error && (
                <div className="text-xs text-red-600 mb-2 p-2 bg-red-50 rounded">
                  Error: {authenticationStatus.error}
                </div>
              )}
              <button
                onClick={() => window.location.href = '/login'}
                className="px-3 py-1.5 bg-yellow-600 hover:bg-yellow-700 text-white text-sm font-medium rounded transition-colors"
              >
                Sign In Now
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Database Connectivity Indicator */}
      {authenticationStatus.isAuthenticated && !connectionDiagnosticStage && (
        <div className="mb-4 flex items-center gap-2 p-3 bg-green-50 rounded-lg border border-green-200">
          <Database className="w-4 h-4 text-green-600" />
          <span className="text-sm text-green-700 font-medium">Database connected</span>
          <CheckCircle className="w-4 h-4 text-green-600 ml-auto" />
        </div>
      )}

      {/* Single-Button Connection Controls */}
      <div className="flex space-x-3 mb-6">
        {!isConnected ? (
          <button
            onClick={handleConnect}
            disabled={!authenticationStatus.isAuthenticated || isInitializingAssistant}
            className={`w-full flex items-center justify-center space-x-2 px-6 py-3 rounded-lg transition-all font-medium ${
              !authenticationStatus.isAuthenticated || isInitializingAssistant
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl'
            }`}
            title={
              !authenticationStatus.isAuthenticated
                ? 'Sign in to enable voice assistant'
                : isInitializingAssistant
                  ? 'Creating session...'
                  : 'One-click setup: Creates session token and connects to OpenAI voice assistant'
            }
          >
            {isInitializingAssistant ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin flex-shrink-0" />
                <span className="truncate">Setting up...</span>
              </>
            ) : (
              <>
                <Mic className="w-5 h-5 flex-shrink-0" />
                <span className="truncate">Start Voice Assistant</span>
                <span className="text-xs bg-white/20 px-2 py-0.5 rounded break-words truncate flex-shrink-0">
                    {currentTransport.toUpperCase()}
                  </span>
              </>
            )}
          </button>
        ) : (
          <>
            <button
              onClick={handleDisconnect}
              className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              <MicOff className="w-4 h-4" />
              <span>Disconnect</span>
            </button>
            <button
              onClick={sendTestMessage}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Test Message
            </button>
            <button
              onClick={clearConversation}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Clear
            </button>
          </>
        )}
      </div>

      {/* Status Indicators */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
          <div className={`w-3 h-3 rounded-full ${
            isListening ? 'bg-red-500 animate-pulse' : 'bg-gray-300'
          }`} />
          <span className="text-sm font-medium">
            {isListening ? 'Listening...' : 'Ready'}
          </span>
        </div>

        <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
          <div className={`w-3 h-3 rounded-full ${
            authenticationStatus.isAuthenticated ? 'bg-green-500' :
            authenticationStatus.isValidating ? 'bg-yellow-500 animate-pulse' : 'bg-red-500'
          }`} />
          <span className="text-sm font-medium">
            {authenticationStatus.isValidating ? 'Validating...' :
             authenticationStatus.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
          </span>
        </div>

        <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
          <Database className="w-4 h-4 text-blue-600" />
          <span className="text-sm font-medium break-words">
            Tool Calls: {toolCalls.length}
          </span>
          <span className="text-xs text-gray-500 break-words truncate">
            (12 tools available)
          </span>
        </div>
      </div>

      {/* Conversation Display */}
      <div className="space-y-4 mb-6">
        {transcript && (
          <div className="p-3 bg-blue-50 rounded-lg">
            <div className="text-sm font-medium text-blue-800 mb-1">You said:</div>
            <div className="text-blue-700">{transcript}</div>
          </div>
        )}

        {response && (
          <div className="p-3 bg-green-50 rounded-lg">
            <div className="text-sm font-medium text-green-800 mb-1">Assistant:</div>
            <div className="text-green-700">{response}</div>
          </div>
        )}

        {recentEventResults.length > 0 && (
          <div className="space-y-3">
            <div className="text-sm font-medium text-purple-800">Recent Event Matches</div>
            {recentEventResults.map((event) => (
              <div
                key={event.id}
                className="rounded-lg border border-purple-200 bg-purple-50 p-3"
              >
                <div className="flex items-start justify-between gap-3">
                  <div>
                    <div className="text-sm font-semibold text-purple-900">
                      {event.product_name ?? 'Inventory Event'}
                    </div>
                    <div className="text-xs text-purple-700 mt-1">
                      {formatEventType(event.event_type)} • {formatEventTimestamp(event.occurred_at ?? event.created_at)}
                    </div>
                    {typeof event.quantity === 'number' && (
                      <div className="text-xs text-purple-700 mt-1">
                        {event.quantity} {event.unit ?? 'units'}
                      </div>
                    )}
                  </div>
                  <button
                    type="button"
                    onClick={() => handleNavigateToEvent(event.id)}
                    className="inline-flex items-center gap-1 rounded-md border border-purple-300 bg-white px-3 py-1 text-xs font-medium text-purple-700 shadow-sm transition hover:bg-purple-100"
                  >
                    View event
                    <ExternalLink className="h-3 w-3" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Enhanced Error Display with Actionable Buttons */}
        {error && (
          <EnhancedErrorDisplay
            error={error}
            onRetry={handleConnect}
            onDismiss={() => {}}
          />
        )}
        {sessionTypeErrorSeen && (
          <button
            type="button"
            onClick={() => {
              setShouldUseAltInit(true);
              altInitRetryRef.current = true;
              addActivity('🔄 Manual alternative initialization retry requested');
              void handleConnect();
            }}
            className="mt-2 inline-flex items-center justify-center rounded-md border border-yellow-400 bg-yellow-100 px-3 py-1.5 text-xs font-semibold text-yellow-900 shadow-sm transition hover:bg-yellow-200"
            title="Uses a constructor-level configuration strategy that avoids the SDK from injecting session.type"
          >
            Try Alternative Connection Method
          </button>
        )}

        {/* Voice State Indicator */}
        <VoiceStateIndicator
          state={voiceState || 'idle'}
          isConnected={isConnected}
          size="md"
        />

        {/* Audio Level Meter */}
        {isListening && (
          <AudioLevelMeter isListening={isListening} />
        )}

        {/* Interim Transcript Display */}
        {(interimTranscript || transcript) && (
          <InterimTranscript
            interimText={interimTranscript || ''}
            finalizedText={transcript}
            isListening={isListening}
          />
        )}
      </div>

      {/* Tool Execution Preview */}
      {toolCalls.length > 0 && (
        <ToolExecutionPreview
          toolCalls={toolCalls}
          currentlyExecuting={currentlyExecutingTool || null}
        />
      )}

      {/* Recent Activities */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-2">Recent Activities:</h3>
        <div className="space-y-1 max-h-32 overflow-y-auto">
          {recentActivities.map((activity, index) => (
            <div key={index} className="text-xs text-gray-600 p-2 bg-gray-50 rounded">
              {activity}
            </div>
          ))}
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="text-sm font-medium text-blue-800 mb-2">Using the 2025 OpenAI Agents SDK:</h4>
        <ul className="text-xs text-blue-700 space-y-1">
          <li>• Click "Connect Voice Assistant" to start</li>
          <li>• Speak naturally about seafood inventory</li>
          <li>• The assistant will automatically handle microphone and audio</li>
          <li>• Try: "Add 5 pounds of salmon to inventory"</li>
          <li>• Or: "How much COD do we have in stock?"</li>
        </ul>
      </div>
    </div>
  );
};

export default ModernRealtimeVoiceAssistant;
