-- Date: 2025-09-30
-- Description: Automatically create Products catalog entries and link them when inventory_events are created

-- Ensure product_name column exists before the trigger/function logic runs
ALTER TABLE IF EXISTS public.inventory_events
  ADD COLUMN IF NOT EXISTS product_name TEXT;

-- Backfill product_name from existing data (name/meta) if column was just added
UPDATE public.inventory_events
SET product_name = COALESCE(product_name, name, metadata->>'product_name', metadata->>'name')
WHERE product_name IS NULL;

-- Function to automatically create product in catalog and link it
CREATE OR REPLACE FUNCTION auto_sync_product_from_inventory_event()
RETURNS TRIGGER AS $$
DECLARE
  v_product_id UUID;
  v_category TEXT;
BEGIN
  -- Only process if product_name is provided
  IF NEW.product_name IS NOT NULL AND NEW.product_name != '' THEN

    -- Try to find existing product by name (case-insensitive)
    SELECT id INTO v_product_id
    FROM "Products"
    WHERE LOWER(name) = LOWER(NEW.product_name)
    LIMIT 1;

    -- If product doesn't exist, create it
    IF v_product_id IS NULL THEN
      -- Infer category from product name
      v_category := CASE
        WHEN LOWER(NEW.product_name) LIKE '%cod%' THEN 'Cod'
        WHEN LOWER(NEW.product_name) LIKE '%salmon%' THEN 'Salmon'
        WHEN LOWER(NEW.product_name) LIKE '%tuna%' THEN 'Tuna'
        WHEN LOWER(NEW.product_name) LIKE '%crab%' THEN 'Shellfish'
        WHEN LOWER(NEW.product_name) LIKE '%shrimp%' THEN 'Shellfish'
        WHEN LOWER(NEW.product_name) LIKE '%lobster%' THEN 'Shellfish'
        WHEN LOWER(NEW.product_name) LIKE '%halibut%' THEN 'Flatfish'
        WHEN LOWER(NEW.product_name) LIKE '%sole%' THEN 'Flatfish'
        ELSE 'Other'
      END;

      -- Create the product
      INSERT INTO "Products" (name, category, unit)
      VALUES (NEW.product_name, v_category, COALESCE(NEW.unit, 'lbs'))
      RETURNING id INTO v_product_id;

      RAISE NOTICE 'Auto-created product: % (category: %, unit: %)',
        NEW.product_name, v_category, COALESCE(NEW.unit, 'lbs');
    END IF;

    -- Link the inventory_event to the product
    NEW.product_id := v_product_id;

  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to run before insert on inventory_events
DROP TRIGGER IF EXISTS trg_auto_sync_product_from_inventory_event ON inventory_events;
CREATE TRIGGER trg_auto_sync_product_from_inventory_event
  BEFORE INSERT ON inventory_events
  FOR EACH ROW
  EXECUTE FUNCTION auto_sync_product_from_inventory_event();

-- Backfill: Link existing inventory_events to products
UPDATE inventory_events
SET product_id = p.id
FROM "Products" p
WHERE inventory_events.product_name = p.name
  AND inventory_events.product_id IS NULL;

-- Create any missing products from existing inventory_events
DO $$
DECLARE
  v_event RECORD;
  v_product_id UUID;
  v_category TEXT;
BEGIN
  FOR v_event IN
    SELECT DISTINCT product_name, unit
    FROM inventory_events
    WHERE product_name IS NOT NULL
      AND product_id IS NULL
  LOOP
    -- Check if product exists
    SELECT id INTO v_product_id
    FROM "Products"
    WHERE LOWER(name) = LOWER(v_event.product_name)
    LIMIT 1;

    -- Create if missing
    IF v_product_id IS NULL THEN
      v_category := CASE
        WHEN LOWER(v_event.product_name) LIKE '%cod%' THEN 'Cod'
        WHEN LOWER(v_event.product_name) LIKE '%salmon%' THEN 'Salmon'
        WHEN LOWER(v_event.product_name) LIKE '%tuna%' THEN 'Tuna'
        WHEN LOWER(v_event.product_name) LIKE '%crab%' THEN 'Shellfish'
        WHEN LOWER(v_event.product_name) LIKE '%shrimp%' THEN 'Shellfish'
        WHEN LOWER(v_event.product_name) LIKE '%lobster%' THEN 'Shellfish'
        WHEN LOWER(v_event.product_name) LIKE '%halibut%' THEN 'Flatfish'
        WHEN LOWER(v_event.product_name) LIKE '%sole%' THEN 'Flatfish'
        ELSE 'Other'
      END;

      INSERT INTO "Products" (name, category, unit)
      VALUES (v_event.product_name, v_category, COALESCE(v_event.unit, 'lbs'))
      RETURNING id INTO v_product_id;

      RAISE NOTICE 'Backfilled product: % (category: %)', v_event.product_name, v_category;
    END IF;

    -- Link all events for this product
    UPDATE inventory_events
    SET product_id = v_product_id
    WHERE product_name = v_event.product_name
      AND product_id IS NULL;
  END LOOP;
END $$;

COMMENT ON FUNCTION auto_sync_product_from_inventory_event() IS
  'Automatically creates Products catalog entries and links them when inventory_events are created via voice or other means';
