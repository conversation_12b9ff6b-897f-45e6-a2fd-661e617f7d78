-- Relax RLS policies to allow service role inserts with null user_id
-- This migration fixes voice inventory operations when user authentication is unavailable
-- but service role (Edge Function) can still insert data

-- Drop the overly restrictive voice_events_insert_auth policy
DROP POLICY IF EXISTS voice_events_insert_auth ON public.inventory_events;

-- Create a more flexible policy that allows:
-- 1. Service role to insert regardless of user_id (bypasses <PERSON><PERSON> entirely)
-- 2. Authenticated users to insert voice events with their user_id OR null user_id
CREATE POLICY voice_events_insert_flexible ON public.inventory_events
  FOR INSERT
  WITH CHECK (
    -- Service role bypasses all checks
    auth.role() = 'service_role' OR

    -- Authenticated users can insert voice events
    (
      auth.role() = 'authenticated' AND
      created_by_voice = true AND
      (
        -- Allow inserts where created_by_user_id matches current user
        created_by_user_id = auth.uid() OR

        -- Allow inserts where created_by_user_id is null (for legacy or fallback cases)
        created_by_user_id IS NULL OR

        -- Allow inserts with user_id in metadata
        (metadata ? 'user_id' AND metadata->>'user_id' = auth.uid()::text)
      )
    )
  );

-- Update the select policy to be similarly flexible
DROP POLICY IF EXISTS voice_events_select_own ON public.inventory_events;

CREATE POLICY voice_events_select_flexible ON public.inventory_events
  FOR SELECT
  USING (
    -- Service role can see everything
    auth.role() = 'service_role' OR

    -- Authenticated users can see voice events they created or that have null user_id
    (
      auth.role() = 'authenticated' AND
      created_by_voice = true AND
      (
        created_by_user_id = auth.uid() OR
        created_by_user_id IS NULL OR
        (metadata ? 'user_id' AND metadata->>'user_id' = auth.uid()::text)
      )
    )
  );

-- Update the update policy similarly
DROP POLICY IF EXISTS voice_events_update_own ON public.inventory_events;

CREATE POLICY voice_events_update_flexible ON public.inventory_events
  FOR UPDATE
  USING (
    auth.role() = 'service_role' OR
    (
      auth.role() = 'authenticated' AND
      created_by_voice = true AND
      (
        created_by_user_id = auth.uid() OR
        created_by_user_id IS NULL OR
        (metadata ? 'user_id' AND metadata->>'user_id' = auth.uid()::text)
      )
    )
  )
  WITH CHECK (
    auth.role() = 'service_role' OR
    (
      auth.role() = 'authenticated' AND
      created_by_voice = true AND
      (
        created_by_user_id = auth.uid() OR
        created_by_user_id IS NULL OR
        (metadata ? 'user_id' AND metadata->>'user_id' = auth.uid()::text)
      )
    )
  );

-- Add helpful comments
COMMENT ON POLICY voice_events_insert_flexible ON public.inventory_events IS
  'Allows service role and authenticated users to insert voice events. Permits null user_id for Edge Function fallback scenarios.';

COMMENT ON POLICY voice_events_select_flexible ON public.inventory_events IS
  'Allows service role and authenticated users to view voice events they created or with null user_id.';

COMMENT ON POLICY voice_events_update_flexible ON public.inventory_events IS
  'Allows service role and authenticated users to update voice events they created or with null user_id.';
