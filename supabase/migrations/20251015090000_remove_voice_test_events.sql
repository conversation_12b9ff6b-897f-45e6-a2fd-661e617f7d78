-- Migration: Remove voice test events and related artifacts
-- Purpose: Clean up diagnostic/test data created by voice tooling and validation flows
-- Date: 2025-10-15
-- Safely deletes records flagged as voice test fixtures from inventory and voice tables

BEGIN;

-- Clean up inventory events created by voice test flows
DO $$
DECLARE
  deleted_inventory INTEGER := 0;
  deleted_products INTEGER := 0;
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_schema = 'public' AND table_name = 'inventory_events'
  ) THEN
    DELETE FROM public.inventory_events
    WHERE
      name IN ('__voice_test__', '__voice_test_product__', '__test_product__')
      OR name LIKE '_\_voice_test%'
      OR (metadata ->> 'product_name') IN ('__voice_test__', '__voice_test_product__', '__test_product__')
      OR (metadata ->> 'test') = 'true'
      OR (metadata ->> 'test_session') = 'true'
      OR (metadata ->> 'voice_test') = 'true'
      OR (metadata ->> 'voice_inventory_tester') = 'true'
      OR (metadata::text ILIKE '%"__voice_test%')
      OR (metadata::text ILIKE '%"test_session":"true"%')
      OR notes ILIKE '%Voice inventory tester validation%';

    GET DIAGNOSTICS deleted_inventory = ROW_COUNT;
    RAISE NOTICE 'Removed % voice test inventory events', deleted_inventory;
  ELSE
    RAISE NOTICE 'inventory_events table not found; skipping voice event cleanup';
  END IF;

  IF EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_schema = 'public' AND table_name = 'products'
  ) THEN
    DELETE FROM public.products
    WHERE
      name IN ('__voice_test__', '__voice_test_product__', '__test_product__')
      OR name LIKE '_\_voice_test%';

    GET DIAGNOSTICS deleted_products = ROW_COUNT;
    RAISE NOTICE 'Removed % voice test products', deleted_products;
  ELSE
    RAISE NOTICE 'products table not found; skipping voice product cleanup';
  END IF;
END
$$;

-- Clean up voice conversation/session artifacts used in automated tests
DO $$
DECLARE
  deleted_turns INTEGER := 0;
  deleted_metrics INTEGER := 0;
  deleted_errors INTEGER := 0;
  deleted_sessions INTEGER := 0;
  deleted_configs INTEGER := 0;
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_schema = 'public' AND table_name = 'voice_conversation_turns'
  ) THEN
    DELETE FROM public.voice_conversation_turns
    WHERE metadata ->> 'test_session' = 'true'
       OR metadata::text ILIKE '%"test_session":"true"%';

    GET DIAGNOSTICS deleted_turns = ROW_COUNT;
    RAISE NOTICE 'Removed % voice_conversation_turns test rows', deleted_turns;
  ELSE
    RAISE NOTICE 'voice_conversation_turns table not found; skipping turn cleanup';
  END IF;

  IF EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_schema = 'public' AND table_name = 'voice_performance_metrics'
  ) THEN
    DELETE FROM public.voice_performance_metrics
    WHERE metadata ->> 'test_session' = 'true'
       OR metadata::text ILIKE '%"test_session":"true"%';

    GET DIAGNOSTICS deleted_metrics = ROW_COUNT;
    RAISE NOTICE 'Removed % voice_performance_metrics test rows', deleted_metrics;
  ELSE
    RAISE NOTICE 'voice_performance_metrics table not found; skipping metrics cleanup';
  END IF;

  IF EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_schema = 'public' AND table_name = 'voice_error_logs'
  ) THEN
    DELETE FROM public.voice_error_logs
    WHERE context ->> 'test_session' = 'true'
       OR context::text ILIKE '%"test_session":"true"%';

    GET DIAGNOSTICS deleted_errors = ROW_COUNT;
    RAISE NOTICE 'Removed % voice_error_logs test rows', deleted_errors;
  ELSE
    RAISE NOTICE 'voice_error_logs table not found; skipping error log cleanup';
  END IF;

  IF EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_schema = 'public' AND table_name = 'voice_conversation_sessions'
  ) THEN
    DELETE FROM public.voice_conversation_sessions
    WHERE (metadata ->> 'test_session') = 'true'
       OR metadata::text ILIKE '%"test_session":"true"%'
       OR user_id = '00000000-0000-0000-0000-00000000BEEF'
       OR (metadata ->> 'session_label') = '__voice_test__';

    GET DIAGNOSTICS deleted_sessions = ROW_COUNT;
    RAISE NOTICE 'Removed % voice_conversation_sessions test rows', deleted_sessions;
  ELSE
    RAISE NOTICE 'voice_conversation_sessions table not found; skipping session cleanup';
  END IF;

  IF EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_schema = 'public' AND table_name = 'voice_agent_configs'
  ) THEN
    DELETE FROM public.voice_agent_configs
    WHERE user_id = '00000000-0000-0000-0000-00000000BEEF'
       OR metadata ->> 'test_session' = 'true'
       OR metadata::text ILIKE '%"test_session":"true"%';

    GET DIAGNOSTICS deleted_configs = ROW_COUNT;
    RAISE NOTICE 'Removed % voice_agent_configs test rows', deleted_configs;
  ELSE
    RAISE NOTICE 'voice_agent_configs table not found; skipping config cleanup';
  END IF;
END
$$;

COMMIT;
