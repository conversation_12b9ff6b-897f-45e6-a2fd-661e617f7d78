-- Restore strict authentication requirement for voice inventory operations
-- This migration fixes the issue where removing auth requirement broke database access
--
-- Problem: The 20251003 migration tried to allow null user_ids but broke the auth flow
-- Solution: Restore strict authentication OR service role requirement
--
-- This ensures:
-- 1. Authenticated users can create voice inventory events with their user_id
-- 2. Service role (Edge Functions) can bypass RLS entirely
-- 3. Unauthenticated requests are properly rejected

-- Drop the overly permissive "flexible" policies from 20251003 migration
DROP POLICY IF EXISTS voice_events_insert_flexible ON public.inventory_events;
DROP POLICY IF EXISTS voice_events_select_flexible ON public.inventory_events;
DROP POLICY IF EXISTS voice_events_update_flexible ON public.inventory_events;

-- Restore strict voice inventory policies
-- These require <PERSON>ITHER authenticated user with matching user_id OR service role

-- INSERT policy: Authenticated users can insert voice events with their user_id
CREATE POLICY voice_events_insert_auth ON public.inventory_events
  FOR INSERT
  WITH CHECK (
    -- Service role bypasses all checks (for Edge Functions)
    auth.role() = 'service_role' OR

    -- Authenticated users MUST provide their user_id
    (
      auth.role() = 'authenticated' AND
      created_by_voice = true AND
      created_by_user_id = auth.uid()
    )
  );

-- SELECT policy: Users can read their own voice events
CREATE POLICY voice_events_select_own ON public.inventory_events
  FOR SELECT
  USING (
    -- Service role can see everything
    auth.role() = 'service_role' OR

    -- Authenticated users can see their own voice events
    (
      auth.role() = 'authenticated' AND
      created_by_voice = true AND
      created_by_user_id = auth.uid()
    )
  );

-- UPDATE policy: Users can update their own voice events
CREATE POLICY voice_events_update_own ON public.inventory_events
  FOR UPDATE
  USING (
    auth.role() = 'service_role' OR
    (
      auth.role() = 'authenticated' AND
      created_by_voice = true AND
      created_by_user_id = auth.uid()
    )
  )
  WITH CHECK (
    auth.role() = 'service_role' OR
    (
      auth.role() = 'authenticated' AND
      created_by_voice = true AND
      created_by_user_id = auth.uid()
    )
  );

-- Add helpful comments
COMMENT ON POLICY voice_events_insert_auth ON public.inventory_events IS
  'Strict authentication: requires service role OR authenticated user with matching user_id. Fixes database access issues from 20251003 migration.';

COMMENT ON POLICY voice_events_select_own ON public.inventory_events IS
  'Users can only read voice events they created. Service role can read all.';

COMMENT ON POLICY voice_events_update_own ON public.inventory_events IS
  'Users can only update voice events they created. Service role can update all.';

-- Add a helper function to validate voice operations have proper authentication
CREATE OR REPLACE FUNCTION public.validate_voice_auth()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Allow service role to bypass validation
  IF auth.role() = 'service_role' THEN
    RETURN NEW;
  END IF;

  -- For authenticated users, ensure created_by_user_id is set correctly
  IF auth.role() = 'authenticated' AND NEW.created_by_voice = true THEN
    -- If user_id is null, set it to current user
    IF NEW.created_by_user_id IS NULL THEN
      NEW.created_by_user_id := auth.uid();
    END IF;

    -- Verify it matches the authenticated user
    IF NEW.created_by_user_id != auth.uid() THEN
      RAISE EXCEPTION 'created_by_user_id must match authenticated user';
    END IF;

    RETURN NEW;
  END IF;

  -- Reject unauthenticated voice operations
  IF NEW.created_by_voice = true THEN
    RAISE EXCEPTION 'Voice inventory operations require authentication';
  END IF;

  RETURN NEW;
END;
$$;

-- Create trigger to validate auth on voice inventory operations
DROP TRIGGER IF EXISTS validate_voice_inventory_auth ON public.inventory_events;

CREATE TRIGGER validate_voice_inventory_auth
  BEFORE INSERT OR UPDATE ON public.inventory_events
  FOR EACH ROW
  WHEN (NEW.created_by_voice = true)
  EXECUTE FUNCTION public.validate_voice_auth();

COMMENT ON FUNCTION public.validate_voice_auth() IS
  'Validates that voice inventory operations have proper authentication. Auto-sets created_by_user_id if missing.';

COMMENT ON TRIGGER validate_voice_inventory_auth ON public.inventory_events IS
  'Ensures voice inventory events have proper authentication before being written to database.';
