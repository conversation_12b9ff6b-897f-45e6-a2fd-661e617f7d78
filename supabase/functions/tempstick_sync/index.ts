// Supabase Edge Function: tempstick_sync
// Ingest TempStick sensors and latest readings into Supabase
// - If called with an authenticated user (Authorization header), uses that user_id
// - Otherwise falls back to Deno.env SYNC_USER_ID (single-tenant) for scheduled runs

// deno-lint-ignore-file no-explicit-any
import { createClient } from 'jsr:@supabase/supabase-js@2';

type RateLimitBucket = {
  count: number;
  windowStart: number;
};

const RATE_LIMIT_WINDOW_MS = 60_000;
const RATE_LIMIT_MAX_REQUESTS = 6;
const rateLimitBuckets = new Map<string, RateLimitBucket>();

// Updated to match actual TempStick API response structure
type TempStickApiSensor = {
  id: string;
  sensor_id: string;
  sensor_name: string;
  last_temp: number; // Temperature in Celsius
  last_humidity: number;
  last_checkin: string; // Timestamp
  offline: string; // "0" = online, "1" = offline
  battery_pct: number;
  rssi: string; // Signal strength
};

type ApiResponse = {
  type: string; // "success"
  message: string;
  data: {
    groups: any[];
    items: TempStickApiSensor[];
  };
};

function isValidSensor(sensor: any): sensor is TempStickApiSensor {
  if (!sensor) return false;
  if (!sensor.sensor_id) return false;
  if (!sensor.sensor_name || typeof sensor.sensor_name !== 'string') return false;
  if (sensor.last_temp !== undefined) {
    const t = Number(sensor.last_temp);
    if (!Number.isFinite(t) || t < -100 || t > 200) return false;
  }
  return true;
}

export async function handler(req: Request): Promise<Response> {
  // Add CORS headers for all responses
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  };

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: corsHeaders });
  }

  try {
    const rateLimitKey = req.headers.get('cf-connecting-ip')
      || req.headers.get('x-forwarded-for')
      || 'global';
    if (isRateLimited(rateLimitKey)) {
      console.warn(`⏱️ TempStick sync rate limited for ${rateLimitKey}`);
      return new Response(
        JSON.stringify({ error: 'Rate limit exceeded', rateLimitKey }),
        { status: 429, headers: { ...corsHeaders, 'content-type': 'application/json' } }
      );
    }

    const requestStartedAt = performance.now();
    const diagnostics: Record<string, unknown> = {
      requestStartedAt: new Date().toISOString(),
      rateLimitKey,
    };

    const SUPABASE_URL = Deno.env.get('SUPABASE_URL') || Deno.env.get('VITE_SUPABASE_URL');
    const SUPABASE_ANON_KEY = Deno.env.get('SUPABASE_ANON_KEY') || Deno.env.get('VITE_SUPABASE_ANON_KEY');
    const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || Deno.env.get('VITE_SUPABASE_SERVICE_ROLE_KEY');
    // Use direct TempStick API (Edge Functions run on Supabase's servers, no CORS issues)
    const TEMPSTICK_BASE_URL = 'https://tempstickapi.com/api/v1';
    const TEMPSTICK_API_KEY = Deno.env.get('TEMPSTICK_API_KEY') || Deno.env.get('VITE_TEMPSTICK_API_KEY');
    const SYNC_USER_ID = Deno.env.get('TEMPSTICK_SYNC_USER_ID');

    if (!SUPABASE_URL || !SUPABASE_ANON_KEY || !SUPABASE_SERVICE_ROLE_KEY) {
      return new Response(JSON.stringify({ error: 'Missing Supabase configuration' }), { 
        status: 500, 
        headers: { ...corsHeaders, 'content-type': 'application/json' }
      });
    }
    if (!TEMPSTICK_API_KEY) {
      return new Response(JSON.stringify({ error: 'Missing TempStick API key' }), { 
        status: 500, 
        headers: { ...corsHeaders, 'content-type': 'application/json' }
      });
    }

    // Auth-aware client (to read caller user)
    const authorizationHeader = req.headers.get('Authorization') || '';
    diagnostics.authorizationHeaderPresent = Boolean(authorizationHeader);

    const authClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
      global: { headers: { Authorization: authorizationHeader } },
    });
    // Service client for privileged writes (bypasses RLS)
    const serviceClient = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

    // Resolve target user id: prefer authenticated caller; else configured fallback
    let userId: string | null = null;
    const { data: userData } = await authClient.auth.getUser();
    if (userData?.user) {
      userId = userData.user.id;
    } else if (SYNC_USER_ID) {
      userId = SYNC_USER_ID;
    }
    diagnostics.userContext = userId ? 'resolved' : 'missing';
    if (!userId) {
      console.error('❌ TempStick sync aborted - no user context available');
      return new Response(JSON.stringify({ error: 'No user context and no TEMPSTICK_SYNC_USER_ID set' }), { 
        status: 400, 
        headers: { ...corsHeaders, 'content-type': 'application/json' }
      });
    }

    // Fetch sensors from TempStick API
    const resp = await fetch(`${TEMPSTICK_BASE_URL}/sensors/all`, {
      headers: { 
        'X-API-KEY': TEMPSTICK_API_KEY, 
        'Accept': 'application/json',
        'User-Agent': '' // TempStick API blocks default User-Agent
      },
    });
    diagnostics.tempstickStatus = resp.status;
    if (!resp.ok) {
      const text = await resp.text();
      console.error(`❌ TempStick API error (${resp.status}): ${text}`);
      return new Response(JSON.stringify({ error: `TempStick API error: ${text}` }), { 
        status: resp.status,
        headers: { ...corsHeaders, 'content-type': 'application/json' }
      });
    }
    const payload = (await resp.json()) as ApiResponse;
    
    // Check if response is successful
    if (payload.type !== 'success') {
      return new Response(JSON.stringify({ error: 'TempStick API returned unsuccessful response' }), { 
        status: 400,
        headers: { ...corsHeaders, 'content-type': 'application/json' }
      });
    }
    
    const sensors = payload.data?.items || [];
    const valid = sensors.filter(isValidSensor);
    diagnostics.sensorsFetched = sensors.length;
    diagnostics.sensorsValid = valid.length;

    let sensorsSynced = 0;
    let readingsCreated = 0;
    let duplicatesPrevented = 0;
    let constraintViolations = 0;
    const errors: string[] = [];

    for (const s of valid) {
      try {
        const sensorRow = {
          user_id: userId,
          sensor_id: s.sensor_id,
          name: s.sensor_name || `TempStick ${s.sensor_id}`,
          device_name: s.sensor_name || `TempStick ${s.sensor_id}`,
          location_description: s.sensor_name || 'Unknown Location',
          is_online: s.offline === '0', // "0" = online, "1" = offline
          battery_level: s.battery_pct,
          is_active: true,
          last_seen_at: new Date(`${s.last_checkin} UTC`).toISOString(),
          updated_at: new Date().toISOString(),
        } as const;

        const { error: upErr } = await serviceClient
          .from('sensors')
          .upsert(sensorRow, { onConflict: 'user_id,sensor_id', ignoreDuplicates: false });
        if (upErr) throw upErr;
        sensorsSynced++;

        if (s.last_temp !== undefined && s.last_checkin) {
          const readingTimestamp = normaliseTimestamp(s.last_checkin);
          const { data: dbSensor, error: selErr } = await serviceClient
            .from('sensors')
            .select('id')
            .eq('user_id', userId)
            .eq('sensor_id', s.sensor_id)
            .single();
          if (selErr || !dbSensor) throw new Error('Sensor lookup failed');

          // Temperature is in Celsius per API documentation
          const tempC = Number(s.last_temp);
          const tempF = (tempC * 1.8) + 32; // Use correct formula from API docs
          const reading = {
            user_id: userId,
            sensor_id: dbSensor.id,
            recorded_at: readingTimestamp,
            temp_celsius: tempC,
            temp_fahrenheit: tempF,
            humidity: Number.isFinite(s.last_humidity) ? s.last_humidity : null,
            within_safe_range: true,
            temp_violation: false,
            data_source: 'tempstick_edge_sync',
          } as const;

          const { data: inserted, error: insErr } = await serviceClient
            .from('temperature_readings')
            .upsert(reading, { onConflict: 'sensor_id,recorded_at' })
            .select('id, recorded_at');

          if (insErr) {
            if (insErr.code === '23514' || insErr.details?.includes('data_source')) {
              constraintViolations++;
              console.error(`🚫 TempStick data_source constraint violation for ${s.sensor_id}: ${insErr.message}`);
            } else if (insErr.code === '42501') {
              console.error(`🚫 TempStick RLS block detected for ${s.sensor_id}: ${insErr.message}`);
            } else {
              console.error(`❌ TempStick reading insert failed for ${s.sensor_id}: ${insErr.message}`);
            }
            throw insErr;
          }

          if (!inserted || inserted.length === 0) {
            duplicatesPrevented++;
            console.log(`♻️ Duplicate TempStick reading ignored for ${s.sensor_id} at ${readingTimestamp}`);
          } else {
            readingsCreated += inserted.length;
          }
        }
      } catch (e: any) {
        errors.push(`Sensor ${s.sensor_id}: ${e?.message || 'sync failed'}`);
        console.error(`❌ TempStick sensor sync failure (${s.sensor_id}):`, e);
      }
    }

    const durationMs = Math.round(performance.now() - requestStartedAt);
    diagnostics.durationMs = durationMs;
    diagnostics.duplicatesPrevented = duplicatesPrevented;
    diagnostics.constraintViolations = constraintViolations;

    console.log(
      `✅ TempStick edge sync completed in ${durationMs}ms - sensors synced: ${sensorsSynced}, readings created: ${readingsCreated}, duplicates prevented: ${duplicatesPrevented}, errors: ${errors.length}`
    );

    return new Response(
      JSON.stringify({
        ok: true,
        sensorsFound: valid.length,
        sensorsSynced,
        readingsCreated,
        duplicatesPrevented,
        errors,
        lastSyncTime: new Date().toISOString(),
        diagnostics,
      }),
      { headers: { ...corsHeaders, 'content-type': 'application/json' } }
    );
  } catch (err: any) {
    console.error('❌ TempStick edge sync fatal error:', err);
    return new Response(JSON.stringify({ error: err?.message || 'Unknown error' }), { 
      status: 500,
      headers: { ...corsHeaders, 'content-type': 'application/json' }
    });
  }
}

// Standard export for Supabase Edge Functions
export default handler;

function isRateLimited(key: string): boolean {
  const bucket = rateLimitBuckets.get(key);
  const now = Date.now();

  if (!bucket || now - bucket.windowStart > RATE_LIMIT_WINDOW_MS) {
    rateLimitBuckets.set(key, { count: 1, windowStart: now });
    return false;
  }

  if (bucket.count >= RATE_LIMIT_MAX_REQUESTS) {
    bucket.count++;
    return true;
  }

  bucket.count++;
  return false;
}

function normaliseTimestamp(timestamp: string): string {
  const trimmed = timestamp.trim();
  const endsWithOffset = /[zZ]|[+-]\d{2}:?\d{2}$/.test(trimmed);
  const isoSource = endsWithOffset ? trimmed : `${trimmed}Z`;
  const date = new Date(isoSource);

  if (Number.isNaN(date.getTime())) {
    console.warn(`⚠️ Unable to parse TempStick timestamp ${timestamp}, defaulting to current time`);
    return new Date().toISOString();
  }

  return date.toISOString();
}
