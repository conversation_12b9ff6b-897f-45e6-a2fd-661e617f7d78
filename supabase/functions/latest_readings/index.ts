// Supabase Edge Function: latest_readings
// Returns the latest temperature_readings per external sensor_id, regardless of user_id (uses service role key).
// Body: { sensor_ids: string[] }

// deno-lint-ignore-file no-explicit-any
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.1?target=deno';

type Reading = {
  sensor_id: string; // internal UUID
  recorded_at: string;
  temp_celsius: number;
  temp_fahrenheit: number;
  humidity?: number | null;
};

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'content-type': 'application/json',
};

export async function handler(req: Request): Promise<Response> {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
  try {
    const SUPABASE_URL = Deno.env.get('SUPABASE_URL') || Deno.env.get('VITE_SUPABASE_URL');
    const SERVICE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || Deno.env.get('VITE_SUPABASE_SERVICE_ROLE_KEY');
    if (!SUPABASE_URL || !SERVICE_KEY) {
      return new Response(JSON.stringify({ error: 'Missing Supabase config' }), { status: 500, headers: corsHeaders });
    }

    const service = createClient(SUPABASE_URL, SERVICE_KEY);

    // Parse body
    const contentType = req.headers.get('content-type') || '';
    if (!contentType.includes('application/json')) {
      return new Response(JSON.stringify({ error: 'Expected application/json body' }), { status: 400, headers: corsHeaders });
    }
    const body = await req.json();
    const externalIds: string[] = Array.isArray(body?.sensor_ids) ? body.sensor_ids : [];
    if (externalIds.length === 0) {
      return new Response(JSON.stringify({ readings: {} }), { headers: corsHeaders });
    }

    // Resolve internal sensor UUIDs for any user
    const { data: sensors, error: sErr } = await service
      .from('sensors')
      .select('id, sensor_id, name')
      .in('sensor_id', externalIds);
    if (sErr) {
      return new Response(JSON.stringify({ error: sErr.message }), { status: 500, headers: corsHeaders });
    }

    if (!sensors || sensors.length === 0) {
      return new Response(JSON.stringify({ readings: {} }), { headers: corsHeaders });
    }

    const internalIds = sensors.map((s: any) => s.id);
    const idToExternal = new Map<string, string>(sensors.map((s: any) => [s.id, s.sensor_id]));

    // Fetch readings for these internal sensors
    const { data: rows, error: rErr } = await service
      .from('temperature_readings')
      .select('sensor_id, recorded_at, temp_celsius, temp_fahrenheit, humidity')
      .in('sensor_id', internalIds)
      .order('recorded_at', { ascending: false });
    if (rErr) {
      return new Response(JSON.stringify({ error: rErr.message }), { status: 500, headers: corsHeaders });
    }

    // Take first row per sensor_id (already ordered desc)
    const latestByInternal = new Map<string, Reading>();
    for (const row of rows as Reading[]) {
      if (!latestByInternal.has(row.sensor_id)) latestByInternal.set(row.sensor_id, row);
    }

    // Map to external ids
    const result: Record<string, Reading | null> = {};
    for (const internalId of internalIds) {
      const ext = idToExternal.get(internalId)!;
      const r = latestByInternal.get(internalId) || null;
      result[ext] = r;
    }

    return new Response(JSON.stringify({ readings: result }), { headers: corsHeaders });
  } catch (err: any) {
    return new Response(JSON.stringify({ error: err?.message || 'Unknown error' }), { status: 500, headers: corsHeaders });
  }
}

export default handler;
