# Supabase Local Development Setup

This document provides comprehensive instructions for setting up and managing the local Supabase Docker environment for the Seafood Manager application.

## Table of Contents

1. [Local Development Setup](#local-development-setup)
2. [Connection Configuration](#connection-configuration)
3. [Database Schema Management](#database-schema-management)
4. [Service Role vs Anon Key Usage](#service-role-vs-anon-key-usage)
5. [Troubleshooting](#troubleshooting)
6. [Schema Dump Commands](#schema-dump-commands)

## Local Development Setup

### Prerequisites

- Docker and Docker Compose installed
- Supabase CLI installed (`npm install -g supabase`)
- Node.js and npm/yarn

### Starting Local Supabase

1. **Initialize Supabase (first time only):**
   ```bash
   supabase init
   ```

2. **Start the local Supabase stack:**
   ```bash
   supabase start
   ```

   This command will:
   - Start PostgreSQL database on port 54322
   - Start Supabase Studio on port 54323
   - Start API server on port 54321
   - Start other required services (Auth, Storage, etc.)

3. **Verify services are running:**
   ```bash
   supabase status
   ```

   Expected output should show all services as "running".

### Stopping Local Supabase

```bash
supabase stop
```

### Reset Local Database

⚠️ **Warning: This will delete all local data**

```bash
supabase db reset
```

## Connection Configuration

### Environment Variables

The application uses these environment variables for local development (configured in `.env.development`):

#### Client-Side Configuration
```env
# Supabase Configuration - Local Docker Instance
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
```

#### Server-Side Configuration
```env
# Service Role Key for admin operations
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# Required for TempStick synchronization - should be a valid user UUID
TEMPSTICK_SYNC_USER_ID=<user-uuid-from-users-table>
```

### Application Connection Workflow

1. **Client Initialization:** The application creates a Supabase client using the anon key
2. **User Authentication:** Users sign in through Supabase Auth
3. **Row Level Security:** Database access is controlled through RLS policies
4. **Service Operations:** Background services use the service role key for admin operations

### Database Access Patterns

- **Port 54321:** API server (used by application)
- **Port 54322:** Direct PostgreSQL access (used for dumps and direct queries)
- **Port 54323:** Supabase Studio (web interface)

## Database Schema Management

### Migrations

Supabase uses SQL migration files to manage schema changes:

```bash
# Create a new migration
supabase migration new <migration_name>

# Apply pending migrations
supabase db reset  # Applies all migrations from scratch

# Check migration status
supabase migration list
```

### Migration Files Location

- **Local migrations:** `supabase/migrations/`
- **Seed data:** `supabase/seed.sql`

### Key Database Tables

#### Core Tables
- `users` - User accounts and profiles
- `sensors` - TempStick sensor configuration
- `temperature_readings` - Temperature data from sensors
- `temperature_alerts` - Alert configurations and active alerts

#### TempStick Integration
- `sensors.user_id` - Links sensors to specific users
- `temperature_readings.sensor_id` - Links readings to sensors
- Foreign key constraints ensure data integrity

## Service Role vs Anon Key Usage

### Anon Key (Client-Side)
- **Used for:** Browser-based operations, user authentication
- **Restrictions:** Subject to Row Level Security (RLS) policies
- **Security:** Safe to expose in client-side code
- **Example usage:** User dashboard, data viewing with proper access control

### Service Role Key (Server-Side Only)
- **Used for:** Admin operations, background tasks, bypassing RLS
- **Restrictions:** Full database access, bypasses security policies
- **Security:** ⚠️ **NEVER expose in client-side code**
- **Example usage:** TempStick data synchronization, system maintenance

### Configuration in Application

```typescript
// Client-side (browser safe)
import { supabase } from '@/lib/supabase';

// Server-side admin operations
import { supabaseService } from '@/lib/supabase-service';
```

## Troubleshooting

### Common Issues

#### 1. Connection Refused Errors

**Problem:** Application cannot connect to Supabase
```
Error: fetch failed - Connection refused
```

**Solutions:**
- Verify Supabase is running: `supabase status`
- Check if port 54321 is available: `lsof -i :54321`
- Restart Supabase: `supabase stop && supabase start`

#### 2. Authentication Errors

**Problem:** User authentication fails
```
Error: Invalid API key
```

**Solutions:**
- Verify anon key in `.env.development` matches local instance
- Check that VITE_SUPABASE_URL points to `http://127.0.0.1:54321`
- Ensure local Supabase is running

#### 3. RLS Policy Errors

**Problem:** Users cannot access their own data
```
Error: new row violates row-level security policy
```

**Solutions:**
- Check RLS policies for the affected table
- Verify user is properly authenticated
- Ensure policies match user_id patterns in your data

#### 4. TempStick Sync Issues

**Problem:** TempStick data not syncing
```
Error: Key (user_id)=(uuid) is not present in table "users"
```

**Solutions:**
- Create a user account in local development database
- Update `TEMPSTICK_SYNC_USER_ID` with valid user UUID
- Verify TempStick API proxy is running on port 3001

### Container Management

Advanced container troubleshooting for Supabase Docker environments, integrating with the comprehensive service management infrastructure from `scripts/start-all-services.sh`.

#### Container Lifecycle and Architecture

**Supabase Service Dependencies:**
```
Docker Daemon
├── supabase_db_<project> (PostgreSQL 15)
├── supabase_auth_<project> (GoTrue Auth)
├── supabase_rest_<project> (PostgREST API)
├── supabase_realtime_<project> (Phoenix Realtime)
├── supabase_storage_<project> (Storage API)
├── supabase_imgproxy_<project> (Image Processing)
├── supabase_kong_<project> (API Gateway)
├── supabase_analytics_<project> (Analytics)
└── supabase_edge_runtime_<project> (Edge Functions)
```

**Network Architecture:**
- **supabase_network_<project>**: Internal container communication
- **Port Mappings**: 54321 (API), 54322 (DB), 54323 (Studio), 54324 (Inbucket), 54325 (Logflare), 54326 (Vector)

#### Phase 1: Comprehensive Container Diagnostics

1. **Container State Analysis**
   ```bash
   bash scripts/diagnose-supabase-containers.sh
   ```
   
   **Diagnostic Coverage:**
   - **CLI Health**: Supabase CLI version, installation integrity, command availability
   - **Docker Daemon**: Connection status, socket accessibility, version compatibility  
   - **Container Inventory**: Running/stopped/exited containers with resource usage
   - **Network Topology**: Active networks, IP assignments, bridge connectivity
   - **Volume Persistence**: Data volumes, mount points, storage allocation
   - **Port Allocation**: Binding conflicts, listening services, firewall rules
   - **Resource Utilization**: Memory, CPU, disk usage by container
   - **Log Analysis**: Recent errors, warning patterns, startup failures

2. **CLI Cache Deep Inspection**
   
   **Cache Directory Structure** (`~/.supabase/docker/`):
   ```
   ~/.supabase/docker/
   ├── docker-compose.yml      # Main orchestration config
   ├── .env                    # Container environment variables  
   ├── volumes/               # Volume configuration
   │   ├── api/
   │   ├── db/
   │   └── storage/
   └── logs/                  # Container startup logs
   ```
   
   **Cache Corruption Indicators:**
   - **Zero-byte files**: `find ~/.supabase/docker/ -size 0 -type f`
   - **Timestamp anomalies**: Files newer than last `supabase start`
   - **Permission errors**: Access denied on cache reads
   - **Malformed YAML**: Invalid docker-compose.yml syntax
   - **Missing dependencies**: Incomplete volume or network definitions
   
   **Manual Cache Analysis:**
   ```bash
   # Check cache file integrity
   ls -la ~/.supabase/docker/
   docker-compose -f ~/.supabase/docker/docker-compose.yml config --quiet
   
   # Validate environment variables
   cat ~/.supabase/docker/.env | grep -E "(POSTGRES_|API_)"
   
   # Check volume mount permissions
   find ~/.supabase/docker/volumes/ -type d -exec ls -ld {} \;
   ```

#### Phase 2: Advanced Diagnostic Procedures

3. **Docker Container Health Assessment**
   
   **Container State Validation:**
   ```bash
   # List all Supabase containers with detailed status
   docker ps -a --filter "label=com.supabase.cli.project" --format "table {{.Names}}	{{.Status}}	{{.Ports}}	{{.Size}}"
   
   # Check container resource consumption
   docker stats --no-stream --format "table {{.Container}}	{{.CPUPerc}}	{{.MemUsage}}	{{.NetIO}}	{{.BlockIO}}"
   
   # Examine container logs for errors
   docker logs supabase_db_<project> --tail 50 --timestamps
   ```
   
   **Network Connectivity Analysis:**
   ```bash
   # Verify internal network communication
   docker network inspect supabase_network_<project>
   
   # Test inter-container connectivity
   docker exec supabase_rest_<project> curl -s http://db:5432 || echo "DB unreachable"
   
   # Check external port accessibility
   netstat -tulpn | grep -E "(54321|54322|54323)"
   ```

4. **Service Dependency Validation**
   
   **Database Container Health:**
   ```bash
   # PostgreSQL readiness check
   docker exec supabase_db_<project> pg_isready -U postgres
   
   # Database connection test
   docker exec supabase_db_<project> psql -U postgres -c "SELECT version();"
   ```
   
   **API Gateway Status:**
   ```bash
   # Kong gateway health
   curl -s http://localhost:54321/health | jq .
   
   # PostgREST API availability
   curl -s http://localhost:54321/rest/v1/ | head -1
   ```

#### Phase 3: Intelligent Rebuild Procedures

5. **Data-Safe Environment Reconstruction**
   ```bash
   bash scripts/rebuild-supabase-environment.sh
   ```
   
   **Rebuild Workflow Phases:**
   
   **Phase A: Pre-Rebuild Validation**
   - Detect existing data volumes and prompt for backup
   - Validate `supabase/config.toml` syntax and project settings
   - Check Docker daemon health and available resources
   - Create timestamped backup of current state in `tmp/`
   
   **Phase B: Controlled Teardown**
   ```bash
   # Graceful service shutdown
   supabase stop --no-backup
   
   # Targeted container cleanup
   docker container rm -f $(docker ps -aq --filter "label=com.supabase.cli.project")
   
   # Network and volume cleanup with safety checks
   docker network prune -f --filter "label=com.supabase.cli.project"
   docker volume prune -f --filter "label=com.supabase.cli.project"
   ```
   
   **Phase C: Cache Restoration**
   - Remove corrupted CLI cache files if detected
   - Validate remaining cache integrity
   - Reset file permissions to user ownership
   - Clear Docker BuildKit cache if needed
   
   **Phase D: Service Reconstruction**
   - Execute `supabase start` with enhanced monitoring
   - Apply environment configuration via `scripts/setup-env.sh`
   - Validate all migration applications
   - Perform health checks on each service component

#### Phase 4: Advanced Health Validation

6. **End-to-End Service Verification**
   ```bash
   bash scripts/validate-supabase-health.sh
   ```
   
   **Comprehensive Health Matrix:**
   
   **API Layer Validation:**
   - **REST API**: `GET /rest/v1/` endpoint responsiveness
   - **Auth API**: `GET /auth/v1/settings` configuration retrieval
   - **Storage API**: `GET /storage/v1/buckets` bucket enumeration
   - **Realtime**: WebSocket connection establishment
   - **Edge Functions**: Function deployment and invocation
   
   **Database Layer Validation:**
   - **Connection Pool**: PostgreSQL connection establishment
   - **Query Execution**: Basic SELECT operations
   - **Migration Status**: `supabase_migrations.schema_migrations` integrity
   - **RLS Enforcement**: Row-level security policy application
   - **Extension Availability**: Required PostgreSQL extensions loaded
   
   **Integration Layer Validation:**
   - **TempStick Sync User**: Verify user exists and has proper permissions
   - **CORS Proxy**: Test connectivity to `http://localhost:3001`
   - **Service Dependencies**: Inter-service communication paths
   - **Data Integrity**: Foreign key constraints and referential integrity

#### Phase 5: Integration with Service Management

7. **Service Management Integration**
   
   **Coordinated Startup with start-all-services.sh:**
   ```bash
   bash scripts/start-all-services.sh
   ```
   
   **Expected Service Status Patterns:**
   ```
   ✅ Prerequisites Check
   ├── Docker daemon: Running
   ├── Node.js environment: Ready  
   ├── Git repository: Clean
   └── Environment files: Valid
   
   ✅ Supabase Services
   ├── API Server: http://127.0.0.1:54321 [200 OK]
   ├── Database: postgresql://postgres:postgres@127.0.0.1:54322/postgres [Connected]
   ├── Studio: http://127.0.0.1:54323 [Accessible]
   └── Auth: Configured and operational
   
   ✅ Supporting Services  
   ├── CORS Proxy: http://localhost:3001 [Active]
   ├── TempStick Sync: User validated, API accessible
   └── Development Server: http://localhost:5173 [Ready]
   ```
   
   **Service Status Interpretation:**
   - **"Starting..."** status lasting >60 seconds indicates container startup issues
   - **Connection timeouts** suggest network configuration problems
   - **Authentication errors** point to RLS policy or user configuration issues
   - **Port binding failures** indicate conflicts with existing services

#### Advanced Troubleshooting Scenarios

8. **Complex Failure Mode Recovery**
   
   **Scenario A: Partial Service Degradation**
   ```bash
   # Identify degraded services
   docker ps --filter "label=com.supabase.cli.project" --format "{{.Names}}: {{.Status}}"
   
   # Restart specific service
   docker restart supabase_rest_<project>
   
   # Validate recovery
   curl -s http://localhost:54321/rest/v1/ | jq .
   ```
   
   **Scenario B: Database Corruption Recovery**
   ```bash
   # Check database integrity
   docker exec supabase_db_<project> pg_dump -U postgres --schema-only postgres > /tmp/schema_check.sql
   
   # Restore from migration state
   supabase db reset --debug
   ```
   
   **Scenario C: Network Isolation Issues**
   ```bash
   # Recreate Supabase network
   docker network rm supabase_network_<project>
   docker network create supabase_network_<project>
   
   # Restart all containers to rejoin network
   bash scripts/rebuild-supabase-environment.sh
   ```

9. **Container Health Monitoring**
   
   **Continuous Health Monitoring:**
   ```bash
   # Real-time container health
   watch -n 5 'docker ps --filter "label=com.supabase.cli.project" --format "table {{.Names}}	{{.Status}}	{{.Ports}}"'
   
   # Service endpoint monitoring  
   watch -n 10 'curl -s http://localhost:54321/health | jq .status'
   
   # Resource usage tracking
   docker stats --format "table {{.Container}}	{{.CPUPerc}}	{{.MemUsage}}" --no-stream
   ```
   
   **Automated Health Alerting:**
   - Monitor container exit codes and restart patterns
   - Track API response times and error rates
   - Alert on resource exhaustion (memory >80%, CPU >90%)
   - Log analysis for error pattern detection

#### Preventive Container Management

10. **Proactive Environment Maintenance**
    
    **Daily Health Validation:**
    ```bash
    # Automated health check routine
    bash scripts/validate-supabase-health.sh --automated
    bash scripts/start-all-services.sh --status-check
    ```
    
    **Weekly Maintenance Tasks:**
    ```bash
    # Update Supabase CLI
    npm install -g supabase@latest
    
    # Clean Docker build cache
    docker system prune --volumes -f --filter "until=168h"
    
    # Backup local development data
    bash scripts/backup-supabase-data.sh
    ```
    
    **Environment Hygiene:**
    - Keep CLI cache under 1GB (`du -sh ~/.supabase/docker/`)
    - Monitor container log sizes (`docker system df`)
    - Validate configuration files weekly (`supabase config validate`)
    - Archive diagnostic reports older than 30 days
    
    **Best Practices:**
    - Always use `supabase stop` before Docker daemon restarts
    - Avoid manual container manipulation outside Supabase CLI
    - Keep development data backups for critical testing scenarios
    - Monitor system resources during intensive development sessions

### Database Connection Testing

#### Test Anon Key Connection
```bash
curl -H "apikey: <anon-key>" \
     -H "Authorization: Bearer <anon-key>" \
     "http://127.0.0.1:54321/rest/v1/users?select=count"
```

#### Test Service Role Connection
```bash
curl -H "apikey: <service-role-key>" \
     -H "Authorization: Bearer <service-role-key>" \
     "http://127.0.0.1:54321/rest/v1/sensors?select=count"
```

### Health Check Commands

```bash
# Check Supabase services
supabase status

# Check database connectivity
psql "postgres://postgres:postgres@localhost:54322/postgres" -c "\l"

# Check API server
curl http://127.0.0.1:54321/health

# View logs
supabase logs
```

## Schema Dump Commands

Commands assume local Supabase Postgres at localhost:54322 with user/password postgres.

### Schema Dumps

- **Dump public schema (DDL only):**
  ```bash
  npx supabase db dump --schema public --schema-only --file supabase/schema/public_schema.sql --db-url "postgres://postgres:postgres@localhost:54322/postgres"
  ```

- **Dump all schemas (DDL only):**
  ```bash
  npx supabase db dump --schema-only --file supabase/schema/all_schemas.sql --db-url "postgres://postgres:postgres@localhost:54322/postgres"
  ```

- **Dump data only (optional; not committed):**
  ```bash
  npx supabase db dump --data-only --file supabase/schema/data_dump.sql --db-url "postgres://postgres:postgres@localhost:54322/postgres"
  ```

### Useful Development Queries

```sql
-- Check current users
SELECT id, email, created_at FROM auth.users;

-- Check sensors and their owners
SELECT s.name, s.sensor_id, u.email
FROM sensors s
JOIN auth.users u ON s.user_id = u.id;

-- Check latest temperature readings
SELECT s.name, tr.temp_celsius, tr.recorded_at
FROM temperature_readings tr
JOIN sensors s ON tr.sensor_id = s.id
ORDER BY tr.recorded_at DESC
LIMIT 10;

-- Check RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies
WHERE tablename IN ('sensors', 'temperature_readings')
ORDER BY tablename, policyname;
```

