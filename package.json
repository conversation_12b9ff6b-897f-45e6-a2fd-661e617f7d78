{"name": "pacific-cloud-seafoods", "private": true, "version": "1.0.0", "type": "module", "engines": {"node": ">=18.0.0"}, "scripts": {"db:dump:public": "supabase db dump --schema public --schema-only --file supabase/schema/public_schema.sql --db-url \"postgres://postgres:postgres@localhost:54322/postgres\"", "db:dump:all": "supabase db dump --schema-only --file supabase/schema/all_schemas.sql --db-url \"postgres://postgres:postgres@localhost:54322/postgres\"", "db:dump:data": "supabase db dump --data-only --file supabase/schema/data_dump.sql --db-url \"postgres://postgres:postgres@localhost:54322/postgres\"", "db:report": "ts-node scripts/dump-schema.ts", "dev": "vite", "dev:api": "node server/index.js", "dev:full": "node server/index.js & vite", "build": "vite build", "build:production": "vite build --config vite.config.production.ts", "build:analyze": "npm run build:production && node scripts/analyze-bundle-performance.js", "build:analyze:detailed": "npm run build:production && npx vite-bundle-analyzer dist", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:yaml": "node scripts/check-yaml.js", "format": "npx prettier --write 'src/**/*.{ts,tsx,js,jsx}'", "format:check": "npx prettier --check 'src/**/*.{ts,tsx,js,jsx}'", "type-check": "tsc --noEmit --project tsconfig.app.json", "quality:check": "npm run type-check && npm run lint && npm run format:check", "quality:fix": "npm run format && npm run lint:fix", "preview": "vite preview", "watch": "vite", "restart": "./scripts/restart-servers.sh", "start-all": "./scripts/start-all-services.sh", "stop-all": "./scripts/stop-all-services.sh", "services:start": "./scripts/start-all-services.sh", "services:stop": "./scripts/stop-all-services.sh", "services:status": "./scripts/start-all-services.sh status", "services:restart": "./scripts/stop-all-services.sh && ./scripts/start-all-services.sh", "setup-env": "./scripts/setup-env.sh", "env:audit": "node scripts/audit-environment.mjs", "env:validate": "node scripts/validate-api-keys.mjs", "env:setup": "node scripts/create-env-local.mjs", "env:check:enhanced": "node check-env.mjs --verbose", "env:production:validate": "node scripts/validate-production-config.mjs", "env:template": "node scripts/generate-env-template.mjs", "env:all": "npm run env:audit && npm run env:validate", "health:check": "node scripts/diagnose-api-connections.mjs", "health:monitor": "node scripts/monitor-api-status.mjs", "health:monitor:status": "node scripts/monitor-api-status.mjs status", "health:monitor:report": "node scripts/monitor-api-status.mjs report", "diagnostics:proxy": "node scripts/test-proxy-chains.mjs", "diagnostics:proxy:voice": "node scripts/test-proxy-chains.mjs --focus=voice", "diagnostics:proxy:tempstick": "node scripts/test-proxy-chains.mjs --focus=tempstick", "diagnostics:proxy:verbose": "node scripts/test-proxy-chains.mjs --verbose", "diagnostics:api": "node scripts/diagnose-api-connections.mjs", "diagnostics:env": "npm run env:check:enhanced", "diagnostics:report": "node scripts/monitor-api-status.mjs report", "seed:categories": "node scripts/seed-categories.mjs", "seed:products": "node scripts/seed-products.mjs", "verify:tempstick": "node scripts/verify-tempstick-ingestion.mjs", "verify:temperature": "node scripts/verify-temperature-system.mjs", "db:migrate": "npx supabase db push", "db:migrate:all": "npx supabase db push --include-all", "db:reset": "npx supabase db reset", "test": "vitest", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:unit": "vitest --run src/components src/lib src/hooks", "test:integration": "vitest --run src/test/integration", "test:performance": "vitest --run src/test/performance", "test:voice": "vitest --run --testPathPattern=\"voice.*test\"", "test:voice:unit": "vitest --run src/test/unit/voice-processing-components.test.ts", "test:voice:integration": "vitest --run src/test/integration/voice-database-comprehensive.test.ts", "test:voice:performance": "vitest --run src/test/performance/voice-processing-performance.test.ts", "test:voice:all": "npm run test:voice:unit && npm run test:voice:integration && npm run test:voice:performance", "test:supabase": "vitest --run --testPathPattern=\".*supabase.*test\"", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:voice": "playwright test src/test/e2e/voice-system-comprehensive.spec.ts", "test:e2e:voice:browsers": "playwright test src/test/e2e/voice-system-comprehensive.spec.ts --project=chromium --project=firefox --project=webkit", "test:all": "npm run test && npm run test:integration && npm run test:e2e", "test:voice:complete": "npm run test:voice:all && npm run test:e2e:voice", "test:ci": "npm run test:coverage && npm run test:integration && npm run test:performance && npm run test:voice:complete", "test:ci:voice": "npm run test:voice:unit && npm run test:voice:integration && npm run test:voice:performance", "test:vendor": "./scripts/test-vendor-system.sh", "test:vendor:unit": "vitest --run --testPathPattern=\"vendor.*component.*test\"", "test:vendor:api": "vitest --run --testPathPattern=\"vendor-api.test\"", "test:vendor:integration": "vitest --run --testPathPattern=\"vendor.*integration.*test\"", "test:vendor:performance": "vitest --run --testPathPattern=\"vendor.*performance.*test\"", "test:vendor:accessibility": "vitest --run --testPathPattern=\"vendor.*accessibility.*test\"", "test:vendor:e2e": "playwright test vendor-performance-workflow.spec.ts", "type-check:root": "tsc --noEmit", "performance:check": "node scripts/check-performance-budgets.js", "performance:lighthouse": "lhci autorun", "performance:analyze": "npm run build:production && npm run performance:check", "docker:build": "docker build -f docker/Dockerfile.production -t seafood-manager:latest .", "docker:run": "docker run -p 8080:8080 seafood-manager:latest", "monitoring:setup": "node scripts/setup-monitoring.js", "functions:serve": "supabase functions serve --no-verify-jwt --env-file .env", "test:edge-sync": "node scripts/test-edge-sync.mjs", "maintenance:light": "bash scripts/dev-maintenance.sh --level=light", "maintenance:medium": "bash scripts/dev-maintenance.sh --level=medium", "maintenance:deep": "bash scripts/dev-maintenance.sh --level=deep", "maintenance:analyze": "bash scripts/dev-maintenance.sh --analyze-only", "maintenance:docker": "bash scripts/docker-maintenance.sh", "maintenance:logs": "bash scripts/rotate-logs.sh", "monitor:resources": "bash scripts/monitor-dev-resources.sh", "monitor:watch": "bash scripts/monitor-dev-resources.sh --watch", "monitor:report": "bash scripts/monitor-dev-resources.sh --report", "dev:safe": "npm run maintenance:light && npm run dev", "dev:monitored": "npm run monitor:watch & npm run dev", "clean": "npm run maintenance:light", "clean:deep": "npm run maintenance:deep", "postbuild": "npm run performance:check", "voice-agent:dev": "./start-voice-agent.sh", "voice-agent:build": "tsc --project src/services/voice-agent/tsconfig.json", "voice-agent:start": "node dist/services/voice-agent/server.js", "voice-agent:test": "vitest --run src/services/voice-agent"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@modelcontextprotocol/sdk": "^1.18.1", "@openai/agents": "^0.1.3", "@openai/realtime-api-beta": "github:openai/openai-realtime-api-beta", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@square/web-sdk": "^2.0.1", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.56.1", "@types/html2canvas": "^0.5.35", "@types/jspdf": "^1.3.3", "@types/papaparse": "^5.3.15", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "cssesc": "^3.0.0", "csv-parse": "^6.1.0", "dotenv": "^16.4.7", "express": "^5.1.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.469.0", "neo4j-driver": "^5.28.2", "node-fetch": "^3.3.2", "openai": "^5.21.0", "papaparse": "^5.4.1", "react": "^19.1.1", "react-day-picker": "^9.9.0", "react-dom": "^19.1.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.51.0", "react-router-dom": "^7.8.1", "recharts": "^2.12.2", "regenerator-runtime": "^0.14.1", "rollup": "^4.50.1", "sonner": "^2.0.7", "tailwind-merge": "^2.6.0", "ws": "^8.18.3", "xlsx": "^0.18.5", "zod": "^3.25.76"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.24.0", "@babel/runtime": "^7.26.0", "@lhci/cli": "^0.12.0", "@playwright/test": "^1.55.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.9", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.6.1", "@types/express": "^5.0.3", "@types/jest": "^29.5.14", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.2", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.18", "babel-plugin-polyfill-regenerator": "^0.6.3", "bundlesize": "^0.18.1", "date-fns": "^4.1.0", "esbuild": "^0.24.0", "eslint": "^9.9.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "glob": "^10.3.10", "globals": "^15.9.0", "happy-dom": "^15.11.6", "js-yaml": "^4.1.0", "jsdom": "^25.0.1", "msw": "^2.6.4", "parcel": "^2.13.2", "playwright-mcp-server": "^1.0.0", "postcss": "^8.4.35", "shadcn": "^3.2.1", "tailwindcss": "^3.4.1", "terser": "^5.43.1", "typescript": "^5.7.3", "typescript-eslint": "^8.3.0", "undici": "^6.21.0", "vite": "^7.1.5", "vite-bundle-analyzer": "^1.2.3", "vite-plugin-commonjs": "^0.10.4", "vite-tsconfig-paths": "^5.1.3", "vitest": "^3.2.4"}, "bundlesize": [{"path": "dist/assets/*.js", "maxSize": "1MB"}, {"path": "dist/assets/*.css", "maxSize": "50kB"}], "overrides": {"@supabase/auth-ui-react": {"react": "19.1.1", "react-dom": "19.1.1"}}}