# Cipher MCP Server Configuration
# Long-term memory for coding agents across multiple IDEs

llm:
  provider: openai
  model: gpt-4o
  apiKey: ${OPENAI_API_KEY}
  maxIterations: 50

memory:
  # Primary vector store for semantic search and knowledge storage
  qdrant:
    url: http://localhost:6333
    collections:
      knowledge: knowledge_memory
      reflection: reflection_memory
    embedding_model: text-embedding-3-small

# MCP Server Configuration
mcp:
  servers:
    cipher:
      description: "Cross-IDE memory layer with Neo4j and Qdrant backends"
      # Timeout in milliseconds
      timeout_ms: 30000

# System prompt for coding memory
systemPrompt: |
  You are a cross-IDE coding memory system for the Seafood Manager project.

  PROJECT CONTEXT:
  - Voice-based inventory management system
  - React + TypeScript frontend with Vite
  - Supabase backend (database, auth, storage)
  - OpenAI integration for voice processing
  - TempStick sensor integration for temperature monitoring

  MEMORY PRIORITIES:
  1. Store architectural decisions and patterns
  2. Track component relationships and dependencies
  3. Remember API integrations and configurations
  4. Maintain context about database schema and migrations
  5. Keep voice processing optimizations and fixes
  6. Document testing strategies and known issues

  Focus on reusable knowledge that helps maintain consistency across IDEs.

# Enable debugging and logging
debug: true
logging:
  level: info
  file: ./data/cipher-memory.log
