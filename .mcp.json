{"mcpServers": {"shadcn": {"command": "npx", "args": ["shadcn@latest", "mcp"]}, "serena": {"command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena", "start-mcp-server"]}, "playwright": {"command": "npx", "args": ["playwright-mcp-server"]}, "cipher": {"command": "cipher", "args": ["--mode", "mcp", "--agent", "memAgent/cipher.yml"], "env": {"VECTOR_STORE_TYPE": "qdrant", "VECTOR_STORE_URL": "http://localhost:6333", "OPENAI_API_KEY": "********************************************************************************************************************************************************************", "PROJECT_NAME": "seafood-manager", "PROJECT_ROOT": "/Users/<USER>/Dev/Seafood-Manager"}, "disabled": false}}}