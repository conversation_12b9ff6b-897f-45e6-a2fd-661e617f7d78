# Pacific Cloud Seafoods Manager

A comprehensive seafood inventory management and compliance tracking system with voice-enabled data entry, real-time traceability, and HACCP compliance monitoring.

## Overview

The Pacific Cloud Seafoods Manager is a modern web application built to streamline seafood operations through intelligent inventory management, voice-enabled data entry, and comprehensive compliance tracking. The system provides real-time visibility into inventory levels, batch tracking, vendor performance, and regulatory compliance.

## Key Features

### 🎤 Voice-Enabled Operations
- Hands-free inventory updates using speech-to-text
- Real-time voice command processing
- Audio storage and playback for audit trails
- Quality review system for low-confidence transcriptions

### 📊 Inventory Management
- Real-time inventory tracking and updates
- Batch tracking and lot management
- Product catalog with detailed specifications
- Automated alerts for low stock and expiration dates

### 🛡️ Compliance & Traceability
- HACCP compliance monitoring and reporting
- GDST-compliant traceability tracking
- FDA regulatory compliance features
- Complete audit trail for all operations

### 👥 Vendor & Customer Management
- Vendor performance tracking and report cards
- Customer relationship management
- Order history and communication tracking
- Integration with external vendor APIs

### 🌡️ Temperature Monitoring & Sensor Integration
- **TempStick API Integration** for real-time temperature monitoring
- **Automated sensor synchronization** with database storage
- **Real-time temperature dashboards** with live data updates
- **HACCP-compliant temperature logging** and violation detection
- **Battery and connectivity monitoring** for all sensors
- **Automated alerts** for temperature threshold breaches
- **Comprehensive sensor health tracking** and reporting

### 📈 Analytics & Reporting
- Real-time dashboards and analytics
- Custom reporting capabilities
- Performance metrics and KPIs
- Export functionality for various formats

## Technology Stack

### Frontend
- **React 18** with TypeScript for type-safe development
- **Vite** for fast development and optimized builds
- **Tailwind CSS** with Radix UI for modern, accessible components
- **React Hook Form** with Zod validation for robust form handling

### Backend & Database
- **Supabase** for PostgreSQL database with real-time capabilities
- **Row Level Security (RLS)** for data access control
- **Supabase Storage** for audio files and document management
- **Real-time subscriptions** for live data updates

### Voice Processing
- **OpenAI Whisper API** for speech-to-text conversion
- **Custom confidence scoring** for quality assurance
- **Audio compression and optimization** for efficient storage
- **Real-time processing pipeline** for immediate feedback

### Infrastructure
- **Supabase** for database, auth, storage, and realtime
- **Supabase Edge Functions** for background ingestion and scheduled sync
- **Neo4j Memory Layer** with APOC plugin for AI agent knowledge management
- **TempStick API Integration** for temperature sensor data ingestion
- **Global CDN** via host of your choice
- **Automated CI/CD** with quality gates

### Sensor Integration
- **TempStick API** for temperature and humidity monitoring
- **Real-time data synchronization** with automatic fallback mechanisms
- **Rate-limited API requests** with exponential backoff retry logic
- **Mock data system** for development and offline scenarios

## Project Structure

```
├── src/                    # Main application source code
│   ├── components/         # React components organized by feature
│   ├── services/          # Business logic and data access services
│   ├── lib/               # Core libraries and utilities
│   ├── types/             # TypeScript type definitions
│   ├── hooks/             # Custom React hooks
│   ├── test/              # Testing infrastructure and utilities
│   └── utils/             # Helper functions and utilities
├── api/                   # (Deprecated) Vercel-style API endpoints (not used in production)
├── supabase/              # Database migrations and configuration
├── docs/                  # Project documentation
├── scripts/               # Utility scripts and automation
├── infrastructure/        # Infrastructure as code and deployment configs
└── e2e/                   # End-to-end tests
```

Each major directory contains its own README.md with detailed information about its purpose, structure, and usage.

## Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn package manager
- Supabase account for database
- OpenAI API key for voice processing

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/paccloud/PCS-Seafood-Manager.git
   cd PCS-Seafood-Manager
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env.development
   # Populate .env.development (or .env.development.local) with your local secrets
   ```

   Production builds should reference `.env.production` and inject real secrets via CI/CD or a managed secrets store.

4. **Database setup**
   ```bash
   npm run db:migrate
   npm run seed:categories
   npm run seed:products
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

6. **Initialize Neo4j Memory Layer** (optional)
   ```bash
   # Check memory layer health
   ./scripts/health-check-neo4j.sh
   
   # Initialize databases with APOC
   ./scripts/manage-neo4j-memory-layer.sh init
   ```
   > Neo4j secrets live in `.env.neo4j`; source it manually before running the helper scripts when required.

### Environment Variables

```bash
# Client-visible (Vite exposes these to the browser)
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=your-local-supabase-anon-key
VITE_API_BASE_URL=http://127.0.0.1:3001
VITE_TEMPSTICK_API_URL=http://127.0.0.1:3001/api/v1
VITE_TEMPSTICK_API_KEY=local-tempstick-api-key
VITE_ENVIRONMENT=development
VITE_TEMPSTICK_SYNC_INTERVAL=300000
VITE_FEATURE_DIRECT_REALTIME=false
VITE_FEATURE_USE_WEBRTC=true
VITE_FEATURE_OVERLAY_CLEANUP=false
VITE_FEATURE_OVERLAY_DIAGNOSTIC=false
VITE_FEATURE_BOTTOM_BAR_DEBUG=false
VITE_REALTIME_MODEL=gpt-4o-realtime-preview-2024-12-17
VITE_REALTIME_VOICE=alloy

# Server-only (never exposed to the browser)
SUPABASE_SERVICE_ROLE_KEY=
OPENAI_API_KEY=
TEMPSTICK_SYNC_USER_ID=
```

Feature flag guidance:
- `VITE_FEATURE_DIRECT_REALTIME` — enables direct connections to OpenAI Realtime; when false requests flow through the relay server.
- `VITE_FEATURE_USE_WEBRTC` — prefers WebRTC instead of websockets for the realtime client.
- `VITE_FEATURE_OVERLAY_CLEANUP` — automatically removes stuck overlays; leave disabled unless debugging overlay issues.
- `VITE_FEATURE_OVERLAY_DIAGNOSTIC` and `VITE_FEATURE_BOTTOM_BAR_DEBUG` — enable diagnostic overlays for UI debugging.
- `VITE_SKIP_AUTH` — bypasses Supabase auth screens; only use on throwaway local environments.

`TEMPSTICK_SYNC_USER_ID` should map to the Supabase auth user that owns TempStick sync jobs. Set it via server-side configuration (or leave blank to derive dynamically in backend workers).

## Development Workflow

### Code Quality
```bash
# Run all quality checks
npm run quality:check

# Fix formatting and linting issues
npm run quality:fix

# Type checking
npm run type-check
```

### Testing
```bash
# Run unit tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run end-to-end tests
npm run test:e2e

# Run all tests
npm run test:all
```

### Database Operations
```bash
# Apply migrations
npm run db:migrate

# Reset database
npm run db:reset

# Generate TypeScript types
supabase gen types typescript > src/types/supabase.ts
```

## Deployment

### Production Build
```bash
# Build for production
npm run build:production

# Analyze bundle size
npm run build:analyze

# Check performance budgets
npm run performance:check
```

### Temperature Monitoring & Sensor Integration

#### Manual Sync Service
The application includes a comprehensive manual synchronization service (`src/lib/manual-sync-service.ts`) that provides:

- **API Integration**: Direct fetch from TempStick API with CORS proxy support
- **Data Validation**: Robust validation of sensor data before processing
- **Database Sync**: Upsert sensors with proper conflict resolution
- **Temperature Readings**: Creation of temperature readings from latest sensor data
- **Cleanup**: Deactivation of stale sensors no longer in API response
- **Error Handling**: Comprehensive error collection and reporting
- **Type Safety**: Full TypeScript type definitions for all API responses

### 🧠 Neo4j Memory Layer
- **AI Agent Memory Management** with three domain-specific databases
- **APOC Plugin Integration** for advanced graph operations (190+ procedures)
- **Knowledge Storage and Retrieval** with temporal analysis capabilities
- **Cross-Database Relationships** for complex knowledge graphs
- **MCP Server Integration** for seamless AI agent interaction
- **Automated Schema Management** with constraints and performance indexes
- **Real-time Memory Operations** for development, seafood, and finance domains

#### Real-time Features
- **Live Dashboard Updates**: Real-time subscription to temperature readings
- **Automated Sync**: Background synchronization every 30 seconds when dashboard is active
- **Manual Sync Button**: On-demand synchronization with detailed result feedback
- **Sensor Health Monitoring**: Battery level and connectivity status tracking

#### API Configuration
- **Base URL**: `https://api.tempstick.com/v1` via CORS proxy
- **Rate Limiting**: 60 requests per minute with exponential backoff
- **Authentication**: API key-based authentication
- **Fallback Support**: Mock data system for development and offline scenarios

#### Development Setup
```bash
# Start CORS proxy for TempStick API (development only)
npm run cors-proxy

# Run manual sensor sync  
npm run sync:sensors

# Check sensor health status
npm run check:sensor-health
```

#### Production Configuration
The temperature monitoring system uses client-side synchronization with proper error handling and fallback mechanisms.

Configure the following environment variables:
```bash
# TempStick API Configuration  
VITE_TEMPSTICK_API_KEY=your-api-key
VITE_USE_MOCK_TEMPSTICK_DATA=false
# For development with CORS proxy
VITE_TEMPSTICK_BASE_URL=http://localhost:3001/api/v1
```

#### Sensor Management
The manual sync service provides comprehensive sensor management:
- **Auto-discovery**: Automatically finds and registers new sensors
- **Data validation**: Validates temperature ranges and sensor health  
- **Conflict resolution**: Handles sensor ID conflicts and data consistency
- **Error recovery**: Graceful handling of API failures and network issues
- Open “Schedules” and add a schedule (e.g., every 5 minutes):
  - Name: `tempstick-sync-5min`
  - Cron: `*/5 * * * *`
  - Request method: `POST`
  - Optional payload: `{ "reason": "scheduled" }`




 The dashboard’s realtime subscription will display new points when the UI is open.

## Voice Processing Features

### Voice Commands
The system supports natural language voice commands for common operations:

- **Inventory Updates**: "Add 50 pounds of salmon to freezer A"
- **Product Lookup**: "Show me the current stock of cod"
- **Batch Tracking**: "Move batch 12345 from processing to finished goods"
- **Quality Checks**: "Record temperature check for freezer B at 32 degrees"

### Quality Assurance
- Confidence scoring for all voice transcriptions
- Manual review workflow for low-confidence events
- Audio playback for verification
- Batch approval and correction capabilities

## Compliance Features

### HACCP Compliance
- Critical Control Point (CCP) monitoring
- Temperature logging and alerts
- Corrective action tracking
- Compliance reporting and documentation

### Traceability (GDST)
- Complete supply chain visibility
- Catch/harvest information tracking
- Processing history documentation
- Chain of custody maintenance

### Audit Trail
- Complete audit trail for all operations
- User attribution and timestamps
- Change tracking with before/after values
- Compliance reporting capabilities

## Performance & Monitoring

### Performance Budgets
- JavaScript bundle: < 1MB
- CSS bundle: < 50KB
- First Contentful Paint: < 2s
- Largest Contentful Paint: < 3s

### Monitoring
- Real-time error tracking
- Performance monitoring
- User analytics
- Custom business metrics

## Development Maintenance Toolkit

Local environments accumulate caches and Docker artifacts quickly. The maintenance suite keeps developer machines healthy and aligns with our incident playbooks.

- **Daily cleanup:** `npm run maintenance:light`
- **Weekly cleanup:** `npm run maintenance:medium`
- **Deep reset:** `npm run maintenance:deep`
- **Resource report:** `npm run monitor:report`
- **Continuous watch:** `npm run monitor:watch`

See [docs/DEVELOPMENT_MAINTENANCE.md](./docs/DEVELOPMENT_MAINTENANCE.md) for full usage instructions, emergency procedures, and CI integration details.

## Security

### Data Protection
- Row Level Security (RLS) for database access
- Encrypted data transmission (HTTPS)
- Secure audio file storage with signed URLs
- Input validation and sanitization

### Authentication & Authorization
- Supabase Auth for user management
- Role-based access control
- Session management
- API key security

## Contributing

### Development Guidelines
1. Follow TypeScript best practices
2. Write comprehensive tests for new features
3. Maintain accessibility standards (WCAG 2.1 AA)
4. Use semantic commit messages
5. Update documentation for significant changes

### Code Review Process
1. Create feature branch from `main`
2. Implement changes with tests
3. Run quality checks locally
4. Submit pull request with description
5. Address review feedback
6. Merge after approval

## Support & Documentation

### Documentation
- [API Documentation](./docs/)
- [Database Schema](./supabase/README.md)
- [Component Library](./src/components/README.md)
- [Deployment Guide](./docs/PRODUCTION_DEPLOYMENT_GUIDE.md)

### Getting Help
- Check existing documentation in each directory's README
- Review issue templates for bug reports and feature requests
- Contact the development team for technical support

## License

This project is proprietary software owned by Pacific Cloud Seafoods. All rights reserved.

## Changelog

See [CHANGELOG.md](./CHANGELOG.md) for a detailed history of changes and releases.

---

**Built with ❤️ for the seafood industry**
