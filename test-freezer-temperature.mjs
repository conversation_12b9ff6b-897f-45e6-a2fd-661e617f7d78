#!/usr/bin/env node

/**
 * Test script for freezer temperature query function
 * Tests the voice assistant's ability to get temperature data
 */

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'http://127.0.0.1:54321';
const SERVICE_ROLE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;
const USER_ID = process.env.TEST_USER_ID || '8ef6a32c-e34c-46cc-bbb8-2e534fa64dcc'; // <EMAIL>

if (!SERVICE_ROLE_KEY) {
  console.error('❌ VITE_SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SERVICE_ROLE_KEY);

console.log('🧪 Testing Freezer Temperature Query Function\n');
console.log('=' .repeat(60));

async function testFreezerQuery() {
  try {
    // Test 1: Get all sensors
    console.log('\n📡 Test 1: Fetching all sensors...');
    const { data: sensors, error: sensorsError } = await supabase
      .from('sensors')
      .select('id, sensor_id, name, location_description, is_online, battery_level')
      .eq('user_id', USER_ID)
      .eq('is_active', true)
      .order('name');

    if (sensorsError) throw sensorsError;

    console.log(`✅ Found ${sensors.length} active sensors:`);
    sensors.forEach(sensor => {
      const status = sensor.is_online ? '🟢 Online' : '🔴 Offline';
      console.log(`   ${status} ${sensor.name} (${sensor.sensor_id})`);
      console.log(`      Battery: ${sensor.battery_level}%`);
    });

    // Test 2: Get recent temperature readings
    console.log('\n🌡️  Test 2: Fetching recent temperature readings...');
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();

    const { data: readings, error: readingsError } = await supabase
      .from('temperature_readings')
      .select(`
        id,
        temp_fahrenheit,
        temp_celsius,
        humidity,
        recorded_at,
        sensor:sensors!inner(id, name, location_description)
      `)
      .eq('user_id', USER_ID)
      .gte('recorded_at', oneHourAgo)
      .order('recorded_at', { ascending: false })
      .limit(10);

    if (readingsError) throw readingsError;

    console.log(`✅ Found ${readings.length} readings in the last hour:`);
    readings.forEach(reading => {
      const temp = reading.temp_fahrenheit?.toFixed(1) || 'N/A';
      const time = new Date(reading.recorded_at).toLocaleTimeString();
      console.log(`   ${time} - ${reading.sensor.name}: ${temp}°F`);
    });

    // Test 3: Query by location/name pattern (simulate voice query)
    console.log('\n🗣️  Test 3: Simulating voice query "What\'s the temperature in the freezer?"');

    // Get sensors matching "freezer" keyword
    const { data: freezerSensors, error: freezerError } = await supabase
      .from('sensors')
      .select('id, name, location_description')
      .eq('user_id', USER_ID)
      .eq('is_active', true)
      .ilike('name', '%freezer%');

    if (freezerError) throw freezerError;

    // Get latest reading for each freezer sensor
    const freezerSensorsWithReadings = await Promise.all(
      freezerSensors.map(async (sensor) => {
        const { data: readings } = await supabase
          .from('temperature_readings')
          .select('temp_fahrenheit, temp_celsius, recorded_at')
          .eq('sensor_id', sensor.id)
          .order('recorded_at', { ascending: false })
          .limit(1);
        return { ...sensor, temperature_readings: readings || [] };
      })
    );

    console.log(`✅ Found ${freezerSensorsWithReadings.length} freezer sensors:`);
    freezerSensorsWithReadings.forEach(sensor => {
      const latestReading = sensor.temperature_readings?.[0];
      if (latestReading) {
        const temp = latestReading.temp_fahrenheit?.toFixed(1) || 'N/A';
        const ago = Math.round((Date.now() - new Date(latestReading.recorded_at).getTime()) / 60000);
        console.log(`   📍 ${sensor.name}`);
        console.log(`      Current: ${temp}°F (${ago} min ago)`);
      } else {
        console.log(`   📍 ${sensor.name}: No recent readings`);
      }
    });

    // Test 4: Format voice response
    console.log('\n🎤 Test 4: Formatting voice response...');

    const voiceResponse = freezerSensorsWithReadings
      .map(sensor => {
        const reading = sensor.temperature_readings?.[0];
        if (!reading) return null;
        const temp = Math.round(reading.temp_fahrenheit);
        return `${sensor.name}: ${temp}°F`;
      })
      .filter(Boolean)
      .join(', ');

    console.log('✅ Voice Assistant Response:');
    console.log(`   "${voiceResponse}"`);

    // Test 5: Check storage areas
    console.log('\n🏢 Test 5: Checking storage areas...');
    const { data: storageAreas, error: storageError } = await supabase
      .from('storage_areas')
      .select('id, name, area_type, temp_min_fahrenheit, temp_max_fahrenheit')
      .eq('user_id', USER_ID)
      .eq('is_active', true);

    if (storageError) throw storageError;

    if (storageAreas.length === 0) {
      console.log('⚠️  No storage areas configured');
      console.log('   Sensors are not linked to storage areas yet');
    } else {
      console.log(`✅ Found ${storageAreas.length} storage areas:`);
      storageAreas.forEach(area => {
        console.log(`   📦 ${area.name} (${area.area_type})`);
        if (area.temp_min_fahrenheit && area.temp_max_fahrenheit) {
          console.log(`      Range: ${area.temp_min_fahrenheit}°F - ${area.temp_max_fahrenheit}°F`);
        }
      });
    }

    console.log('\n' + '='.repeat(60));
    console.log('✅ All tests completed successfully!\n');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error(error);
    process.exit(1);
  }
}

testFreezerQuery();
