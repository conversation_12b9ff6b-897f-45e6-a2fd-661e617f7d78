// Minimal Express server for voice realtime and tool endpoints
// Run in development: `node server/index.js` (PORT defaults to 3001)

import express from 'express';
import cors from 'cors';
import fetch from 'node-fetch';
import OpenAI from 'openai';
import { WebSocketServer, WebSocket } from 'ws';
import { createServer } from 'http';
import dotenv from 'dotenv';

// Load environment variables from .env.local first, then .env (for local dev)
dotenv.config({ path: '.env.local' });
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

app.use(cors({ origin: true }));
app.use(express.json());

// Debug endpoint to check environment variables
app.get('/api/debug/env', (req, res) => {
  res.json({
    REALTIME_MODEL: process.env.REALTIME_MODEL,
    VITE_REALTIME_MODEL: process.env.VITE_REALTIME_MODEL,
    REALTIME_VOICE: process.env.REALTIME_VOICE,
    hasOpenAIKey: !!process.env.OPENAI_API_KEY,
    hasViteOpenAIKey: !!process.env.VITE_OPENAI_API_KEY,
    nodeEnv: process.env.NODE_ENV,
    openaiKeyLength: process.env.OPENAI_API_KEY ? process.env.OPENAI_API_KEY.length : 0,
    viteOpenaiKeyLength: process.env.VITE_OPENAI_API_KEY ? process.env.VITE_OPENAI_API_KEY.length : 0
  });
});

// Proxy for OpenAI Realtime WebRTC SDP negotiation to avoid browser CORS
// Accept raw SDP text and forward to OpenAI with correct headers
app.post('/api/openai/realtime/calls', express.text({ type: 'application/sdp' }), async (req, res) => {
  const correlationId = `sdp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  try {
    console.log(`🔌 [${correlationId}] SDP proxy request received:`, {
      hasBody: Boolean(req.body),
      bodyLength: typeof req.body === 'string' ? req.body.length : 0,
      bodyType: typeof req.body,
      hasAuth: Boolean(req.headers['authorization']),
      contentType: req.headers['content-type']
    });

    const auth = req.headers['authorization'];
    if (!auth) {
      console.error(`❌ [${correlationId}] Missing Authorization header`);
      return res.status(401).send('Missing Authorization header');
    }

    const model = (req.query?.model
      || process.env.REALTIME_MODEL
      || process.env.VITE_REALTIME_MODEL
      || 'gpt-4o-realtime-preview-2024-12-17');

    // GA endpoint requires /v1/realtime/calls for WebRTC SDP negotiation
    const upstreamUrl = `https://api.openai.com/v1/realtime/calls${model ? `?model=${encodeURIComponent(String(model))}` : ''}`;

    // Extract auth token type for debugging
    const authPrefix = auth.startsWith('Bearer ') ? auth.substring(7, 17) : auth.substring(0, 10);
    const authTokenType = authPrefix.startsWith('sk-') ? 'API_KEY' : authPrefix.startsWith('ek_') ? 'EPHEMERAL_TOKEN' : 'UNKNOWN';

    const logData = {
      url: upstreamUrl,
      model,
      hasBody: Boolean(req.body),
      bodyType: typeof req.body,
      bodyLength: typeof req.body === 'string' ? req.body.length : 0,
      bodyPreview: typeof req.body === 'string' ? req.body.substring(0, 100) : null,
      hasSdkHeader: Boolean(req.headers['x-openai-agents-sdk']),
      sdkHeader: req.headers['x-openai-agents-sdk'],
      userAgent: req.headers['user-agent'],
      authTokenType,
      authPrefix
    };
    console.log(`📤 [${correlationId}] Forwarding SDP to OpenAI:`, JSON.stringify(logData, null, 2));

    // Log full SDP for debugging
    if (typeof req.body === 'string') {
      console.log(`📋 [${correlationId}] Full SDP offer:\n${req.body}`);
    }

    const needsRealtimeBeta = String(model).toLowerCase().includes('realtime-preview');

    const upstream = await fetch(upstreamUrl, {
      method: 'POST',
      headers: {
        'Authorization': auth,
        'Accept': 'application/sdp',
        'Content-Type': 'application/sdp',
        ...(needsRealtimeBeta ? { 'OpenAI-Beta': 'realtime=v1' } : {}),
        ...(process.env.OPENAI_ORG ? { 'OpenAI-Organization': process.env.OPENAI_ORG } : {}),
      },
      body: typeof req.body === 'string' ? req.body : '',
    });

    const contentType = upstream.headers.get('content-type') || '';
    const text = await upstream.text();
    
    // Enhanced debug logging with SDP validation
    const isValidSDP = text.startsWith('v=');
    const sdpLines = text.split('\n');
    console.log(`📥 [${correlationId}] OpenAI SDP response:`, {
      status: upstream.status,
      statusText: upstream.statusText,
      contentType,
      responseLength: text.length,
      isValidSDP,
      lineCount: sdpLines.length,
      firstLine: sdpLines[0],
      hasMediaSection: text.includes('m='),
      hasConnection: text.includes('c=IN'),
      hasCandidates: text.includes('a=candidate')
    });

    // Log errors if SDP is invalid
    if (upstream.status !== 200) {
      console.error(`❌ [${correlationId}] OpenAI returned error status ${upstream.status}:`, text.substring(0, 500));
    } else if (!isValidSDP) {
      console.error(`❌ [${correlationId}] Invalid SDP response (doesn't start with 'v='):`, text.substring(0, 200));
    }
    
    res.status(upstream.status);
    if (contentType) res.set('Content-Type', contentType);
    res.set('X-Correlation-ID', correlationId);
    
    return res.send(text);
  } catch (err) {
    console.error(`❌ [${correlationId}] Realtime calls proxy error:`, {
      error: err.message,
      type: err.name,
      stack: err.stack?.split('\n')[0]
    });
    return res.status(502).send(`Realtime calls proxy error [${correlationId}]: ${err.message}`);
  }
});

function resolveOpenAIClient() {
  const apiKey = process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_PRIMARY;
  if (!apiKey) {
    return null;
  }
  return new OpenAI({ apiKey });
}

// Health/capability check for realtime
app.all('/api/voice-realtime-check', async (req, res) => {
  try {
    console.log('🔍 Environment check:');
    console.log('  OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? 'SET' : 'NOT SET');
    console.log('  OPENAI_API_KEY_PRIMARY:', process.env.OPENAI_API_KEY_PRIMARY ? 'SET' : 'NOT SET');
    console.log('  VITE_OPENAI_API_KEY:', process.env.VITE_OPENAI_API_KEY ? 'SET' : 'NOT SET');
    const hasServerKey = Boolean(process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_PRIMARY);
    const enableDirect = String(process.env.ENABLE_DIRECT_REALTIME || process.env.VITE_ENABLE_DIRECT_REALTIME) === 'true';
    const supported = hasServerKey || !enableDirect;
    return res.status(200).json({ supported, mode: enableDirect ? 'direct' : 'fallback', hasServerKey });
  } catch (err) {
    console.error('voice-realtime-check error:', err);
    return res.status(200).json({ supported: false });
  }
});

// Enhanced environment diagnostics with comprehensive API health checks
app.get('/api/env-check', async (req, res) => {
  try {
    const mask = (s) => (typeof s === 'string' && s.length > 8 ? `${s.slice(0, 4)}…${s.slice(-4)}` : undefined);
    const startTime = Date.now();

    // Basic environment check
    const envCheck = {
      nodeEnv: process.env.NODE_ENV || 'development',
      openai: {
        hasServerKey: Boolean(process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_PRIMARY),
        hasViteClientKey: Boolean(process.env.VITE_OPENAI_API_KEY),
      },
      supabase: {
        hasUrl: Boolean(process.env.VITE_SUPABASE_URL),
        hasAnonKey: Boolean(process.env.VITE_SUPABASE_ANON_KEY),
      },
      tempstick: {
        hasKey: Boolean(process.env.VITE_TEMPSTICK_API_KEY || process.env.TEMPSTICK_API_KEY),
        keyMasked: mask(process.env.VITE_TEMPSTICK_API_KEY || process.env.TEMPSTICK_API_KEY || ''),
      },
    };

    // Extended API connectivity tests
    const healthChecks = {};

    // OpenAI API Health Check
    if (envCheck.openai.hasServerKey) {
      try {
        const apiKey = process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_PRIMARY;
        const testStart = Date.now();
        const openaiResponse = await fetch('https://api.openai.com/v1/models', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            ...(process.env.OPENAI_ORG ? { 'OpenAI-Organization': process.env.OPENAI_ORG } : {}),
          },
          signal: AbortSignal.timeout(5000) // 5 second timeout
        });
        const responseTime = Date.now() - testStart;

        if (openaiResponse.ok) {
          const data = await openaiResponse.json();
          const realtimeModels = data.data?.filter(m => m.id.includes('realtime')) || [];
          healthChecks.openai = {
            status: 'healthy',
            responseTime,
            modelsAvailable: data.data?.length || 0,
            realtimeModels: realtimeModels.length,
            realtimeModelIds: realtimeModels.map(m => m.id)
          };
        } else {
          healthChecks.openai = {
            status: 'unhealthy',
            responseTime,
            error: `HTTP ${openaiResponse.status}`,
            message: await openaiResponse.text().catch(() => 'Unknown error')
          };
        }
      } catch (error) {
        healthChecks.openai = {
          status: 'error',
          error: error.message,
          type: error.name === 'TimeoutError' ? 'timeout' : 'network'
        };
      }
    } else {
      healthChecks.openai = { status: 'not_configured', reason: 'No API key available' };
    }

    // TempStick API Health Check
    if (envCheck.tempstick.hasKey) {
      try {
        const apiKey = process.env.VITE_TEMPSTICK_API_KEY || process.env.TEMPSTICK_API_KEY;
        const testStart = Date.now();
        const tempstickResponse = await fetch('https://tempstickapi.com/api/v1/sensors/all', {
          method: 'GET',
          headers: {
            'X-API-KEY': apiKey,
            'Accept': 'application/json',
            'User-Agent': ''
          },
          signal: AbortSignal.timeout(10000) // 10 second timeout for TempStick
        });
        const responseTime = Date.now() - testStart;

        if (tempstickResponse.ok) {
          const data = await tempstickResponse.json();
          healthChecks.tempstick = {
            status: 'healthy',
            responseTime,
            sensorsCount: data.data?.length || 0,
            apiStatus: data.type || 'unknown'
          };
        } else {
          healthChecks.tempstick = {
            status: 'unhealthy',
            responseTime,
            error: `HTTP ${tempstickResponse.status}`,
            message: await tempstickResponse.text().catch(() => 'Unknown error')
          };
        }
      } catch (error) {
        healthChecks.tempstick = {
          status: 'error',
          error: error.message,
          type: error.name === 'TimeoutError' ? 'timeout' : 'network'
        };
      }
    } else {
      healthChecks.tempstick = { status: 'not_configured', reason: 'No API key available' };
    }

    // Supabase Health Check
    if (envCheck.supabase.hasUrl && envCheck.supabase.hasAnonKey) {
      try {
        const testStart = Date.now();
        const supabaseResponse = await fetch(`${process.env.VITE_SUPABASE_URL}/rest/v1/`, {
          method: 'GET',
          headers: {
            'apikey': process.env.VITE_SUPABASE_ANON_KEY,
            'Authorization': `Bearer ${process.env.VITE_SUPABASE_ANON_KEY}`
          },
          signal: AbortSignal.timeout(5000)
        });
        const responseTime = Date.now() - testStart;

        healthChecks.supabase = {
          status: supabaseResponse.ok ? 'healthy' : 'unhealthy',
          responseTime,
          httpStatus: supabaseResponse.status
        };
      } catch (error) {
        healthChecks.supabase = {
          status: 'error',
          error: error.message,
          type: error.name === 'TimeoutError' ? 'timeout' : 'network'
        };
      }
    } else {
      healthChecks.supabase = { status: 'not_configured', reason: 'Missing URL or anon key' };
    }

    const totalTime = Date.now() - startTime;
    const overallHealth = Object.values(healthChecks).every(check =>
      check.status === 'healthy' || check.status === 'not_configured'
    ) ? 'healthy' : 'degraded';

    res.status(200).json({
      ...envCheck,
      health: {
        overall: overallHealth,
        checks: healthChecks,
        checkDuration: totalTime,
        timestamp: new Date().toISOString()
      }
    });
  } catch (err) {
    console.error('env-check error:', err);
    res.status(500).json({
      error: 'env_check_failed',
      message: err.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Detailed API health status endpoint
app.get('/api/health-status', async (req, res) => {
  try {
    const results = {
      timestamp: new Date().toISOString(),
      services: {},
      proxy: {}
    };

    // Test each service with detailed diagnostics
    const services = ['openai', 'tempstick', 'supabase'];

    for (const service of services) {
      try {
        const testResult = await testServiceHealth(service);
        results.services[service] = testResult;
      } catch (error) {
        results.services[service] = {
          status: 'error',
          error: error.message,
          timestamp: new Date().toISOString()
        };
      }
    }

    // Test proxy chain health
    try {
      results.proxy = await testProxyChain();
    } catch (error) {
      results.proxy = {
        status: 'error',
        error: error.message
      };
    }

    // Determine overall status
    const serviceStatuses = Object.values(results.services).map(s => s.status);
    const hasErrors = serviceStatuses.includes('error') || serviceStatuses.includes('unhealthy');
    results.overall = hasErrors ? 'degraded' : 'healthy';

    res.status(200).json(results);
  } catch (err) {
    console.error('health-status error:', err);
    res.status(500).json({
      error: 'health_check_failed',
      message: err.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Proxy chain diagnostic endpoint
app.get('/api/diagnostics/proxy-chain', async (req, res) => {
  try {
    const results = await testCompleteProxyFlow();
    res.status(200).json(results);
  } catch (err) {
    console.error('proxy-chain diagnostics error:', err);
    res.status(500).json({
      error: 'proxy_diagnostics_failed',
      message: err.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Helper function to test individual service health
async function testServiceHealth(service) {
  const startTime = Date.now();

  switch (service) {
    case 'openai': {
      const apiKey = process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_PRIMARY;
      if (!apiKey) {
        return { status: 'not_configured', reason: 'No API key' };
      }

      try {
        // Test models endpoint
        const modelsResponse = await fetch('https://api.openai.com/v1/models', {
          headers: { 'Authorization': `Bearer ${apiKey}` },
          signal: AbortSignal.timeout(5000)
        });

        // Test realtime client secrets endpoint (GA API)
        const sessionResponse = await fetch('https://api.openai.com/v1/realtime/client_secrets', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ model: 'gpt-4o-realtime-preview' }),
          signal: AbortSignal.timeout(5000)
        });

        const responseTime = Date.now() - startTime;

        return {
          status: modelsResponse.ok && (sessionResponse.ok || sessionResponse.status === 400) ? 'healthy' : 'unhealthy',
          responseTime,
          modelsEndpoint: modelsResponse.status,
          realtimeEndpoint: sessionResponse.status,
          details: {
            canListModels: modelsResponse.ok,
            canCreateSessions: sessionResponse.ok || sessionResponse.status === 400 // 400 is expected for invalid model
          }
        };
      } catch (error) {
        return {
          status: 'error',
          responseTime: Date.now() - startTime,
          error: error.message,
          type: error.name
        };
      }
    }

    case 'tempstick': {
      const apiKey = process.env.VITE_TEMPSTICK_API_KEY || process.env.TEMPSTICK_API_KEY;
      if (!apiKey) {
        return { status: 'not_configured', reason: 'No API key' };
      }

      try {
        const response = await fetch('https://tempstickapi.com/api/v1/sensors/all', {
          headers: {
            'X-API-KEY': apiKey,
            'Accept': 'application/json',
            'User-Agent': ''
          },
          signal: AbortSignal.timeout(10000)
        });

        const responseTime = Date.now() - startTime;

        if (response.ok) {
          const data = await response.json();
          return {
            status: 'healthy',
            responseTime,
            sensorsCount: data.data?.length || 0,
            apiType: data.type
          };
        } else {
          return {
            status: 'unhealthy',
            responseTime,
            httpStatus: response.status,
            error: await response.text().catch(() => 'Unknown error')
          };
        }
      } catch (error) {
        return {
          status: 'error',
          responseTime: Date.now() - startTime,
          error: error.message,
          type: error.name
        };
      }
    }

    case 'supabase': {
      const url = process.env.VITE_SUPABASE_URL;
      const key = process.env.VITE_SUPABASE_ANON_KEY;
      if (!url || !key) {
        return { status: 'not_configured', reason: 'Missing URL or key' };
      }

      try {
        const response = await fetch(`${url}/rest/v1/`, {
          headers: {
            'apikey': key,
            'Authorization': `Bearer ${key}`
          },
          signal: AbortSignal.timeout(5000)
        });

        const responseTime = Date.now() - startTime;

        return {
          status: response.ok ? 'healthy' : 'unhealthy',
          responseTime,
          httpStatus: response.status
        };
      } catch (error) {
        return {
          status: 'error',
          responseTime: Date.now() - startTime,
          error: error.message,
          type: error.name
        };
      }
    }

    default:
      throw new Error(`Unknown service: ${service}`);
  }
}

// Helper function to test proxy functionality
async function testProxyChain() {
  const results = {
    expressServer: { status: 'unknown' },
    tempstickProxy: { status: 'unknown' },
    voiceProxy: { status: 'unknown' },
    websocketRelay: { status: 'unknown' }
  };

  // Test Express server responsiveness
  try {
    const serverTest = await fetch(`http://localhost:${PORT}/api/debug/env`, {
      signal: AbortSignal.timeout(2000)
    });
    results.expressServer = {
      status: serverTest.ok ? 'healthy' : 'unhealthy',
      httpStatus: serverTest.status
    };
  } catch (error) {
    results.expressServer = {
      status: 'error',
      error: error.message
    };
  }

  // Test TempStick proxy if API key is available
  if (process.env.VITE_TEMPSTICK_API_KEY || process.env.TEMPSTICK_API_KEY) {
    try {
      const proxyTest = await fetch(`http://localhost:${PORT}/api/v1/sensors/all`, {
        signal: AbortSignal.timeout(5000)
      });
      results.tempstickProxy = {
        status: proxyTest.ok ? 'healthy' : 'unhealthy',
        httpStatus: proxyTest.status
      };
    } catch (error) {
      results.tempstickProxy = {
        status: 'error',
        error: error.message
      };
    }
  } else {
    results.tempstickProxy = { status: 'not_configured' };
  }

  // Test voice endpoints
  try {
    const voiceTest = await fetch(`http://localhost:${PORT}/api/voice-realtime-check`, {
      signal: AbortSignal.timeout(2000)
    });
    results.voiceProxy = {
      status: voiceTest.ok ? 'healthy' : 'unhealthy',
      httpStatus: voiceTest.status
    };
  } catch (error) {
    results.voiceProxy = {
      status: 'error',
      error: error.message
    };
  }

  // WebSocket relay test would require actual connection, so just check if it's configured
  results.websocketRelay = {
    status: wss ? 'configured' : 'not_configured',
    path: '/api/realtime-relay',
    port: PORT
  };

  return results;
}

// Helper function to test complete proxy flow
async function testCompleteProxyFlow() {
  const results = {
    timestamp: new Date().toISOString(),
    steps: []
  };

  // Step 1: Test direct API access
  results.steps.push({
    step: 1,
    name: 'Direct API Access',
    description: 'Testing direct external API connectivity',
    ...(await testDirectApiAccess())
  });

  // Step 2: Test Express proxy
  results.steps.push({
    step: 2,
    name: 'Express Proxy Layer',
    description: 'Testing Express server proxy endpoints',
    ...(await testExpressProxyLayer())
  });

  // Step 3: Test header propagation
  results.steps.push({
    step: 3,
    name: 'Header Propagation',
    description: 'Testing API key and header forwarding',
    ...(await testHeaderPropagation())
  });

  // Summary
  const allPassed = results.steps.every(step => step.status === 'pass');
  results.summary = {
    overall: allPassed ? 'pass' : 'fail',
    passedSteps: results.steps.filter(s => s.status === 'pass').length,
    totalSteps: results.steps.length
  };

  return results;
}

async function testDirectApiAccess() {
  try {
    const apiKey = process.env.VITE_TEMPSTICK_API_KEY || process.env.TEMPSTICK_API_KEY;
    if (!apiKey) {
      return { status: 'skip', reason: 'No TempStick API key configured' };
    }

    const response = await fetch('https://tempstickapi.com/api/v1/sensors/all', {
      headers: {
        'X-API-KEY': apiKey,
        'Accept': 'application/json'
      },
      signal: AbortSignal.timeout(10000)
    });

    return {
      status: response.ok ? 'pass' : 'fail',
      httpStatus: response.status,
      details: response.ok ? 'Direct API access successful' : await response.text().catch(() => 'Unknown error')
    };
  } catch (error) {
    return {
      status: 'fail',
      error: error.message,
      details: 'Direct API access failed'
    };
  }
}

async function testExpressProxyLayer() {
  try {
    const response = await fetch(`http://localhost:${PORT}/api/v1/sensors/all`, {
      signal: AbortSignal.timeout(10000)
    });

    return {
      status: response.ok ? 'pass' : 'fail',
      httpStatus: response.status,
      details: response.ok ? 'Express proxy successful' : await response.text().catch(() => 'Unknown error')
    };
  } catch (error) {
    return {
      status: 'fail',
      error: error.message,
      details: 'Express proxy failed'
    };
  }
}

async function testHeaderPropagation() {
  try {
    // Test that API keys are properly forwarded
    const response = await fetch(`http://localhost:${PORT}/api/debug/env`, {
      signal: AbortSignal.timeout(2000)
    });

    if (!response.ok) {
      return {
        status: 'fail',
        details: 'Could not test header propagation - debug endpoint failed'
      };
    }

    const data = await response.json();
    const hasKeys = data.tempstick?.hasKey && (data.openai?.hasServerKey || data.openai?.hasViteClientKey);

    return {
      status: hasKeys ? 'pass' : 'fail',
      details: hasKeys ? 'API keys properly configured' : 'Missing required API keys',
      apiKeys: {
        tempstick: data.tempstick?.hasKey || false,
        openai: data.openai?.hasServerKey || data.openai?.hasViteClientKey || false
      }
    };
  } catch (error) {
    return {
      status: 'fail',
      error: error.message,
      details: 'Header propagation test failed'
    };
  }
}

// Enhanced TempStick API relay with correlation IDs and detailed logging
app.use('/api/v1', async (req, res) => {
  // Generate correlation ID for request tracing
  const correlationId = `tempstick_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const startTime = Date.now();

  try {
    const original = req.originalUrl || req.url || '';
    const tail = original.replace(/^\/api\/v1\/?/, '');
    const [pathOnly, qs] = tail.split('?');
    const apiPath = (pathOnly || '').replace(/^\//, '');
    const url = `https://tempstickapi.com/api/v1/${apiPath}${qs ? `?${qs}` : ''}`;
    const apiKey = process.env.VITE_TEMPSTICK_API_KEY || process.env.TEMPSTICK_API_KEY;

    console.log(`🌡️ [${correlationId}] TempStick proxy request:`, {
      method: req.method,
      originalUrl: original,
      targetUrl: url,
      hasApiKey: Boolean(apiKey),
      userAgent: req.headers['user-agent']?.substring(0, 50) || 'unknown',
      timestamp: new Date().toISOString()
    });

    if (!apiKey) {
      console.error(`❌ [${correlationId}] Missing TempStick API key`);
      return res.status(500).json({
        error: 'Missing TempStick API key (set VITE_TEMPSTICK_API_KEY in .env)',
        correlationId,
        remediation: 'Configure VITE_TEMPSTICK_API_KEY or TEMPSTICK_API_KEY in environment variables'
      });
    }

    // Validate API key format
    if (!apiKey.startsWith('TS_')) {
      console.warn(`⚠️ [${correlationId}] Unexpected TempStick API key format:`, {
        keyPrefix: apiKey.substring(0, 3),
        keyLength: apiKey.length
      });
    }

    // Prepare headers - don't send Content-Type for GET requests (TempStick doesn't like it)
    const headers = {
      'X-API-KEY': apiKey,
      // TempStick API prefers empty UA to avoid firewall blocks
      'User-Agent': '',
      'Accept': 'application/json',
      'Accept-Encoding': 'gzip, deflate',
    };

    // Only add Content-Type for non-GET requests
    if (!['GET', 'HEAD'].includes(req.method)) {
      headers['Content-Type'] = req.headers['content-type'] || 'application/json';
    }

    const requestBody = ['GET','HEAD'].includes(req.method) ? undefined : JSON.stringify(req.body ?? {});

    console.log(`📤 [${correlationId}] Forwarding to TempStick:`, {
      method: req.method,
      url,
      headers: {
        hasApiKey: Boolean(headers['X-API-KEY']),
        contentType: headers['Content-Type'],
        accept: headers['Accept']
      },
      hasBody: Boolean(requestBody),
      bodyLength: requestBody?.length || 0
    });

    const requestStart = Date.now();
    const upstream = await fetch(url, {
      method: req.method,
      headers,
      body: requestBody,
      signal: AbortSignal.timeout(15000) // 15 second timeout for TempStick
    });

    const responseTime = Date.now() - requestStart;
    const totalTime = Date.now() - startTime;

    console.log(`📥 [${correlationId}] TempStick response received:`, {
      status: upstream.status,
      statusText: upstream.statusText,
      responseTime,
      totalTime,
      contentType: upstream.headers.get('content-type'),
      contentLength: upstream.headers.get('content-length'),
      hasGzipEncoding: upstream.headers.get('content-encoding')?.includes('gzip')
    });

    // Handle GZIP compressed responses
    const contentType = upstream.headers.get('content-type') || 'application/json';
    res.status(upstream.status);
    res.set('Content-Type', contentType);

    // Add correlation ID to response headers for client-side tracing
    res.set('X-Correlation-ID', correlationId);
    res.set('X-Response-Time', responseTime.toString());

    // node-fetch automatically handles gzip decompression when we call .json() or .text()
    if (contentType.includes('application/json')) {
      try {
        const json = await upstream.json();

        // Log successful JSON response with data analysis
        console.log(`✅ [${correlationId}] TempStick JSON response processed:`, {
          responseType: json.type || 'unknown',
          dataCount: json.data?.length || 0,
          hasData: Boolean(json.data),
          responseKeys: Object.keys(json),
          totalTime
        });

        // Enhance response with tracing information
        if (typeof json === 'object' && json !== null) {
          json._meta = {
            correlationId,
            responseTime,
            totalTime,
            timestamp: new Date().toISOString()
          };
        }

        return res.json(json);
      } catch (parseError) {
        console.error(`❌ [${correlationId}] TempStick JSON parse error:`, {
          error: parseError.message,
          responseTime,
          totalTime,
          contentType,
          contentLength: upstream.headers.get('content-length')
        });

        const text = await upstream.text().catch(() => 'Unable to read response');
        return res.status(502).json({
          error: 'TempStick response parse error',
          correlationId,
          details: text.substring(0, 200),
          responseTime,
          remediation: 'TempStick API returned invalid JSON - check API status'
        });
      }
    } else {
      const text = await upstream.text();
      console.log(`📄 [${correlationId}] TempStick text response:`, {
        responseLength: text.length,
        responsePreview: text.substring(0, 100),
        contentType,
        totalTime
      });

      // Add metadata to text response if possible
      res.set('X-Total-Time', totalTime.toString());
      return res.send(text);
    }
  } catch (err) {
    const totalTime = Date.now() - startTime;
    console.error(`❌ [${correlationId}] TempStick proxy error:`, {
      error: err.message,
      type: err.name,
      stack: err.stack?.split('\n')[0],
      totalTime
    });

    let errorCategory = 'unknown';
    let remediation = 'Check network connectivity and try again';

    if (err.name === 'TimeoutError') {
      errorCategory = 'timeout';
      remediation = 'Request timed out - check TempStick API status and network connection';
    } else if (err.name === 'TypeError' && err.message.includes('fetch')) {
      errorCategory = 'network';
      remediation = 'Network error - verify internet connection and TempStick API availability';
    } else if (err.message.includes('ENOTFOUND')) {
      errorCategory = 'dns';
      remediation = 'DNS resolution failed - check network connectivity';
    } else if (err.message.includes('ECONNREFUSED')) {
      errorCategory = 'connection_refused';
      remediation = 'Connection refused - TempStick API may be down';
    }

    return res.status(502).json({
      error: 'TempStick proxy error',
      details: err.message,
      category: errorCategory,
      correlationId,
      totalTime,
      remediation
    });
  }
});

// Enhanced ephemeral token minting with correlation IDs and detailed logging
app.all('/api/voice/ephemeral-token', async (req, res) => {
  // Generate correlation ID for request tracing
  const correlationId = `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const startTime = Date.now();

  // Log incoming request
  console.log(`🎯 [${correlationId}] Token request received:`, {
    method: req.method,
    userAgent: req.headers['user-agent']?.substring(0, 50) || 'unknown',
    timestamp: new Date().toISOString()
  });

  if (req.method !== 'POST' && req.method !== 'GET') {
    console.log(`❌ [${correlationId}] Method not allowed: ${req.method}`);
    res.set('Allow', 'GET, POST');
    return res.status(405).json({
      error: 'Method not allowed',
      correlationId,
      supportedMethods: ['GET', 'POST']
    });
  }

  const apiKey = process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_PRIMARY;
  if (!apiKey) {
    console.error(`❌ [${correlationId}] OpenAI API key not configured`);
    return res.status(500).json({
      error: 'OPENAI_API_KEY not configured',
      correlationId,
      remediation: 'Set OPENAI_API_KEY or OPENAI_API_KEY_PRIMARY in environment variables'
    });
  }

  // Note: model and voice parameters are extracted but NOT sent to the token endpoint
  // They should be applied by the client when connecting with the ephemeral token
  const model = (req.query.model || process.env.REALTIME_MODEL || 'gpt-4o-realtime-preview-2024-12-17');
  const voice = (req.query.voice || process.env.REALTIME_VOICE || 'alloy');

  console.log(`🔧 [${correlationId}] Token request (config params for client reference only):`, {
    model,
    voice,
    hasApiKey: Boolean(apiKey),
    apiKeyLength: apiKey.length,
    apiKeyPrefix: apiKey.substring(0, 7) + '...'
  });

  try {
    // CRITICAL: The client_secrets endpoint is for ephemeral token creation only
    // It does NOT accept session configuration (model, voice, modalities, etc.)
    // All session configuration must be applied when the client connects with the token
    const requestStart = Date.now();

    console.log(`📤 [${correlationId}] Requesting ephemeral token:`, {
      url: 'https://api.openai.com/v1/realtime/client_secrets',
      method: 'POST',
      headers: {
        hasAuth: Boolean(apiKey),
        hasOrg: Boolean(process.env.OPENAI_ORG)
      },
      note: 'Session config (model, voice, etc.) will be applied during WebSocket connection'
    });

    // Request ephemeral token - no body parameters needed
    // CRITICAL: WebRTC requires /v1/realtime/client_secrets endpoint for ephemeral tokens
    // The /realtime/sessions endpoint is for WebSocket connections only
    // CRITICAL FIX: Remove model parameter from ephemeral token request
    // The OpenAI client_secrets endpoint doesn't accept model parameter
    const r = await fetch('https://api.openai.com/v1/realtime/client_secrets', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        ...(process.env.OPENAI_ORG ? { 'OpenAI-Organization': process.env.OPENAI_ORG } : {}),
      },
      // CRITICAL: No body needed for ephemeral token creation
      // Model will be specified when establishing WebSocket connection
      signal: AbortSignal.timeout(10000) // 10 second timeout
    });

    const responseTime = Date.now() - requestStart;
    const totalTime = Date.now() - startTime;

    console.log(`📥 [${correlationId}] OpenAI response received:`, {
      status: r.status,
      statusText: r.statusText,
      responseTime,
      totalTime,
      contentType: r.headers.get('content-type')
    });

    let data;
    try {
      data = await r.json();
      console.log(`📦 [${correlationId}] Response data:`, JSON.stringify(data, null, 2));
    } catch (parseError) {
      console.error(`❌ [${correlationId}] JSON parse error:`, parseError);
      const text = await r.text().catch(() => 'Unable to read response');
      return res.status(502).json({
        error: 'Invalid JSON response from OpenAI',
        correlationId,
        details: text.substring(0, 200),
        remediation: 'Check OpenAI API status and try again'
      });
    }

    // Check if request failed or token is missing
    // OpenAI /realtime/sessions returns ephemeral token in 'client_secret.value' field
    const ephemeralToken = data?.client_secret?.value || data?.value || data?.client_secret;
    
    if (!r.ok || !ephemeralToken) {
      console.error(`❌ [${correlationId}] Token creation failed:`, {
        status: r.status,
        statusText: r.statusText,
        error: data?.error,
        hasValue: Boolean(data?.value),
        hasClientSecret: Boolean(data?.client_secret),
        responseKeys: Object.keys(data || {}),
        fullResponse: JSON.stringify(data, null, 2)
      });

      const errorMessage = data?.error?.message || data?.error || 'Failed to mint token';
      const errorType = data?.error?.type || 'unknown';
      const errorCode = data?.error?.code || r.status;

      let remediation = 'Check OpenAI API key and try again';
      if (r.status === 401) {
        remediation = 'Verify OpenAI API key is valid and has sufficient permissions';
      } else if (r.status === 429) {
        remediation = 'Rate limit exceeded - wait before retrying';
      } else if (r.status === 400) {
        remediation = 'Check model parameter - ensure realtime model is supported';
      }

      return res.status(r.status || 500).json({
        error: errorMessage,
        errorType,
        errorCode,
        correlationId,
        responseTime,
        remediation
      });
    }

    // Validate token format (should start with 'ek_' for ephemeral keys)
    if (typeof ephemeralToken !== 'string') {
      console.error(`❌ [${correlationId}] Token is not a string:`, {
        tokenType: typeof ephemeralToken,
        actualValue: JSON.stringify(ephemeralToken)
      });
      return res.status(500).json({
        error: 'Ephemeral token extraction failed - token is not a string',
        correlationId,
        remediation: 'Check OpenAI API response format'
      });
    }

    if (!ephemeralToken.startsWith('ek_')) {
      console.warn(`⚠️ [${correlationId}] Unexpected token format (expected ek_ prefix):`, {
        tokenPrefix: ephemeralToken.substring(0, 10),
        tokenLength: ephemeralToken.length
      });
    }

    console.log(`✅ [${correlationId}] Token created successfully:`, {
      hasToken: Boolean(ephemeralToken),
      tokenLength: ephemeralToken?.length,
      tokenPrefix: ephemeralToken?.substring(0, 5) + '...',
      sessionId: data.session?.id || data.id,
      expiresAt: data.expires_at,
      responseTime,
      totalTime
    });

    // Return the token in the format the client expects (client_secret)
    // but also include the full response for compatibility
    return res.status(200).json({
      client_secret: ephemeralToken, // Client expects this field
      value: ephemeralToken,          // OpenAI returns this field
      ...data,
      correlationId,
      performance: {
        responseTime,
        totalTime
      }
    });

  } catch (err) {
    const totalTime = Date.now() - startTime;
    console.error(`❌ [${correlationId}] Token creation error:`, {
      error: err.message,
      type: err.name,
      stack: err.stack?.split('\n')[0],
      totalTime
    });

    let remediation = 'Check network connectivity and try again';
    if (err.name === 'TimeoutError') {
      remediation = 'Request timed out - check network connection and OpenAI API status';
    } else if (err.name === 'TypeError') {
      remediation = 'Network error - verify internet connection';
    }

    return res.status(502).json({
      error: 'Network error',
      details: err.message,
      type: err.name,
      correlationId,
      totalTime,
      remediation
    });
  }
});

app.post('/api/voice/realtime-session', async (req, res) => {
  try {
    const apiKey = process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_PRIMARY;
    if (!apiKey) {
      return res.status(500).json({ error: 'OPENAI_API_KEY not configured' });
    }

    const {
      model = process.env.REALTIME_MODEL || 'gpt-4o-realtime-preview-2024-12-17',
      voice = process.env.REALTIME_VOICE || 'alloy',
      instructions,
      modalities,
    } = req.body ?? {};

    const body = {
      model,
      voice,
      modalities: Array.isArray(modalities) && modalities.length > 0 ? modalities : ['audio', 'text'],
      instructions:
        instructions ||
        'You are the Pacific Cloud Seafoods realtime assistant. Help manage seafood inventory with short, precise voice responses.',
    };

    // Use GA endpoint for creating client secrets (not the beta /sessions endpoint)
    const upstream = await fetch('https://api.openai.com/v1/realtime/client_secrets', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        ...(process.env.OPENAI_ORG ? { 'OpenAI-Organization': process.env.OPENAI_ORG } : {}),
      },
      body: JSON.stringify(body),
    });

    const data = await upstream.json().catch(() => ({}));

    if (!upstream.ok) {
      console.error('Realtime session creation failed:', data);
      return res.status(upstream.status).json({ error: data?.error?.message || data?.error || 'Failed to create realtime session' });
    }

    if (process.env.NODE_ENV !== 'production') {
      console.log('Realtime session created:', {
        id: data?.id,
        model: data?.model,
      });
    }

    return res.status(200).json(data);
  } catch (err) {
    console.error('Realtime session creation failed:', err);
    return res.status(500).json({ error: 'Failed to create realtime session' });
  }
});

// Optional: serve static built app if present (production use)
// Uncomment after running `npm run build`
// import path from 'path';
// import { fileURLToPath } from 'url';
// const __filename = fileURLToPath(import.meta.url);
// const __dirname = path.dirname(__filename);
// const distPath = path.join(__dirname, '..', 'dist');
// app.use(express.static(distPath));
// app.get('*', (_req, res) => {
//   res.sendFile(path.join(distPath, 'index.html'));
// });

// Create HTTP server to support both Express and WebSocket
const server = createServer(app);

// WebSocket server for OpenAI Realtime API relay
const wss = new WebSocketServer({ 
  server,
  path: '/api/realtime-relay'
});

// Store active connections
const connections = new Map();

function generateSessionId() {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

wss.on('connection', async (clientWs, request) => {
  console.log('🔗 Client connected to realtime relay');
  
  const sessionId = generateSessionId();
  // Prefer server-side OPENAI_API_KEY; fall back to VITE_ for convenience
  const apiKey = process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY;
  
  if (!apiKey) {
    console.error('❌ OpenAI API key not configured');
    clientWs.send(JSON.stringify({
      type: 'error',
      error: { message: 'OpenAI API key not configured', code: 'config_error' }
    }));
    clientWs.close();
    return;
  }

  try {
    // Connect to OpenAI Realtime API
    const wsModel = process.env.REALTIME_MODEL || 'gpt-realtime';
    const openaiWs = new WebSocket(
      `wss://api.openai.com/v1/realtime?model=${encodeURIComponent(wsModel)}`,
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          ...(process.env.OPENAI_ORG ? { 'OpenAI-Organization': process.env.OPENAI_ORG } : {}),
        }
      }
    );

    // Store connection pair
    connections.set(sessionId, { clientWs, openaiWs, sessionId });

    // Handle OpenAI WebSocket events
    openaiWs.on('open', () => {
      console.log(`✅ OpenAI connection established for session ${sessionId}`);
    });

    openaiWs.on('message', (data, isBinary) => {
      // Debug: Log message types (but not full content for privacy)
      if (!isBinary) {
        try {
          const message = JSON.parse(data.toString());
          console.log(`📨 OpenAI → Client: ${message.type || 'unknown'}`);
        } catch (e) {
          console.log('📨 OpenAI → Client: non-JSON message');
        }
      } else {
        console.log('📨 OpenAI → Client: binary audio data');
      }

      // Forward message to client unchanged (preserve binary/text)
      if (clientWs.readyState === WebSocket.OPEN) {
        try {
          clientWs.send(data, { binary: isBinary });
        } catch (err) {
          console.error('Error forwarding message to client:', err);
        }
      }
    });

    openaiWs.on('error', (error) => {
      console.error('OpenAI WebSocket error:', error);
      if (clientWs.readyState === WebSocket.OPEN) {
        clientWs.send(JSON.stringify({
          type: 'error',
          error: { message: 'OpenAI connection error', code: 'connection_error' }
        }));
      }
    });

    openaiWs.on('close', (code, reason) => {
      console.log(`OpenAI connection closed: ${code} ${reason}`);
      connections.delete(sessionId);
      if (clientWs.readyState === WebSocket.OPEN) {
        clientWs.close(code, reason.toString());
      }
    });

    // Handle client WebSocket events
    clientWs.on('message', (data, isBinary) => {
      // Debug: Log message types (but not full content for privacy)
      if (!isBinary) {
        try {
          const message = JSON.parse(data.toString());
          console.log(`📤 Client → OpenAI: ${message.type || 'unknown'}`);
        } catch (e) {
          console.log('📤 Client → OpenAI: non-JSON message');
        }
      } else {
        console.log('📤 Client → OpenAI: binary audio data');
      }

      // Forward message to OpenAI unchanged (preserve binary/text)
      if (openaiWs.readyState === WebSocket.OPEN) {
        try {
          openaiWs.send(data, { binary: isBinary });
        } catch (err) {
          console.error('Error forwarding message to OpenAI:', err);
        }
      }
    });

    // Keepalive ping to prevent idle disconnects
    const pingInterval = setInterval(() => {
      if (clientWs.readyState === WebSocket.OPEN) {
        try { clientWs.ping(); } catch {}
      }
      if (openaiWs.readyState === WebSocket.OPEN) {
        try { openaiWs.ping(); } catch {}
      }
    }, 20000);

    clientWs.on('error', (error) => {
      console.error('Client WebSocket error:', error);
    });

    clientWs.on('close', (code, reason) => {
      console.log(`Client connection closed: ${code} ${reason}`);
      connections.delete(sessionId);
      if (openaiWs.readyState === WebSocket.OPEN) {
        openaiWs.close();
      }
      clearInterval(pingInterval);
    });

  } catch (error) {
    console.error('Error setting up relay connection:', error);
    if (clientWs.readyState === WebSocket.OPEN) {
      clientWs.send(JSON.stringify({
        type: 'error',
        error: { message: 'Failed to establish relay connection', code: 'relay_error' }
      }));
      clientWs.close();
    }
  }
});

server.listen(PORT, () => {
  console.log(`🚀 Server listening on http://localhost:${PORT}`);
  console.log(`📡 WebSocket relay available at ws://localhost:${PORT}/api/realtime-relay`);
});
