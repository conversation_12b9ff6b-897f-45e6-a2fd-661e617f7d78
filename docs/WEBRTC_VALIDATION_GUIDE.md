# WebRTC Voice Assistant Validation Guide
**Date**: October 4, 2025  
**Purpose**: End-to-end validation of WebRTC implementation fixes

## Quick Validation Checklist

- [ ] Dev servers running (frontend + backend)
- [ ] Environment variables configured correctly
- [ ] Server token endpoint returns ephemeral tokens
- [ ] WebRTC connection establishes successfully
- [ ] Tools are registered and show in logs
- [ ] Voice commands execute tool functions
- [ ] Database records are created
- [ ] Error handling provides clear messages

## Pre-Validation Setup

### 1. Environment Configuration

Ensure these environment variables are set in `.env` or `.env.local`:

```env
# OpenAI Configuration (Required for WebRTC)
OPENAI_API_KEY=sk-...                                    # Your OpenAI API key
REALTIME_MODEL=gpt-4o-realtime-preview-2024-12-17       # Realtime model
REALTIME_VOICE=alloy                                     # Voice selection

# Supabase Configuration (Required for tool execution)
VITE_SUPABASE_URL=https://...supabase.co                # Supabase project URL
VITE_SUPABASE_ANON_KEY=eyJ...                           # Supabase anon key

# Optional: Organization ID
OPENAI_ORG=org-...                                       # If using organization
```

### 2. Start Dev Servers

```bash
# Start backend server (runs on port 3001)
node server/index.js

# In separate terminal, start frontend (runs on port 5177)
npm run dev
```

**Expected Output**:
```
🚀 Server listening on http://localhost:3001
📡 WebSocket relay available at ws://localhost:3001/api/realtime-relay

VITE v5.x.x  ready in xxx ms
➜  Local:   http://localhost:5177/
```

## Validation Tests

### Test 1: Server Token Endpoint

**Purpose**: Verify server generates ephemeral tokens via correct endpoint

```bash
curl -X POST http://localhost:3001/api/voice/ephemeral-token
```

**Expected Response**:
```json
{
  "client_secret": "ek_...",
  "value": "ek_...",
  "id": "...",
  "correlationId": "token_...",
  "performance": {
    "responseTime": 200,
    "totalTime": 210
  }
}
```

**✅ Pass Criteria**:
- HTTP 200 status
- `client_secret` field present
- Token starts with `ek_` prefix
- Response time < 5 seconds

**❌ Fail Indicators**:
- HTTP 500: Missing OPENAI_API_KEY
- HTTP 401: Invalid API key
- HTTP 400: Incorrect model parameter
- Token doesn't start with `ek_`: Wrong endpoint being used

### Test 2: WebRTC Connection Establishment

**Purpose**: Verify WebRTC transport connects to OpenAI

**Steps**:
1. Navigate to `http://localhost:5177/voice` (or voice test page)
2. Open browser DevTools Console (F12)
3. Click "Connect" button
4. Watch console logs

**Expected Console Output**:
```
🔍 [CONSTRUCTOR] RealtimeAgent created { toolsRegistered: 14 }
🔍 [CONSTRUCTOR] WebRTC transport created
🔍 [CONSTRUCTOR] RealtimeSession instantiated { transport: 'webrtc' }
🔌 Connecting to OpenAI Realtime API...
🔑 Using API credentials: { hasEphemeralToken: true, ... }
🔌 About to call session.connect() - session state captured
✅ session.connect() completed - checking for DataChannel
✅ DataChannel patch applied immediately after connection
✅ Connected - now sending session configuration with instructions
📋 Building session configuration payload
🔍 Agent tools: 14 [{ name: '...', hasInvoke: true }, ...]
🔧 Formatted tool: calculate_stock_levels has invoke: true
🔧 Formatted tool: query_inventory has invoke: true
... (more tools)
✅ Session configuration applied successfully
✅ Connected to OpenAI Realtime API with modern SDK
```

**✅ Pass Criteria**:
- WebRTC transport created (not WebSocket)
- All tools show `hasInvoke: true`
- DataChannel patch applied successfully
- Session configuration sent without errors
- No "Unknown parameter" errors

**❌ Fail Indicators**:
- "Unknown parameter: 'session.type'" error → DataChannel patch failed
- Tools show `hasInvoke: false` → Tools not properly defined
- "Missing API credentials" → Environment variable issue
- Connection timeout → Network or API issue

### Test 3: Tool Registration Verification

**Purpose**: Verify tools are properly registered and formatted

**Steps**:
1. With WebRTC connected, check console logs
2. Look for tool registration messages

**Expected Logs**:
```
🚀 buildSessionConfigurationPayload called
🔍 Agent tools: 14 [
  { name: 'calculate_stock_levels', hasInvoke: true },
  { name: 'query_inventory', hasInvoke: true },
  { name: 'query_recent_events', hasInvoke: true },
  { name: 'query_vendors', hasInvoke: true },
  { name: 'query_customers', hasInvoke: true },
  { name: 'query_partners', hasInvoke: true },
  { name: 'create_inventory_event', hasInvoke: true },
  { name: 'update_inventory_event', hasInvoke: true },
  { name: 'get_temperature', hasInvoke: true },
  { name: 'get_current_view', hasInvoke: true },
  { name: 'fill_form_field', hasInvoke: true },
  { name: 'submit_form', hasInvoke: true },
  { name: 'clear_form', hasInvoke: true },
  { name: 'get_form_fields', hasInvoke: true }
]
📋 Session payload with tools: { toolCount: 14, tools: [...] }
```

**✅ Pass Criteria**:
- All 14 tools listed
- All tools show `hasInvoke: true`
- Tool count matches expected (14)
- No warnings about missing methods

**❌ Fail Indicators**:
- Tools show `hasInvoke: false` → Wrong SDK version or import
- Tool count < 14 → Missing tool imports
- Warnings about "missing invoke function" → Tool definition error

### Test 4: Voice Command Execution

**Purpose**: Verify voice commands trigger tool execution and database updates

**Test 4A: Inventory Query**
```
Voice Command: "What do we have in stock?"
```

**Expected Behavior**:
1. User speaks command
2. Transcript appears in UI: "What do we have in stock?"
3. Console shows: `🔧 Tool call detected: calculate_stock_levels`
4. Assistant responds with stock summary
5. No database errors

**Test 4B: Inventory Addition**
```
Voice Command: "Add 10 pounds of salmon to inventory"
```

**Expected Behavior**:
1. User speaks command
2. Transcript appears in UI: "Add 10 pounds of salmon to inventory"
3. Console shows: `🔧 Tool call detected: create_inventory_event`
4. Database creates new inventory_event record
5. Assistant confirms: "Added 10 pounds of salmon" (or similar)

**Test 4C: Temperature Query**
```
Voice Command: "What's the freezer temperature?"
```

**Expected Behavior**:
1. User speaks command
2. Console shows: `🔧 Tool call detected: get_temperature`
3. Assistant responds with temperature reading
4. No database errors

**✅ Pass Criteria**:
- Commands are transcribed correctly
- Tool calls appear in console logs
- Assistant provides relevant responses
- Database records created (for add operations)
- No authentication or RLS errors

**❌ Fail Indicators**:
- "Authentication required" → Voice auth not initialized
- "RLS policy violation" → Database permissions issue
- "Tool not found" → Tool registration failed
- No response from assistant → Tool execution failed silently

### Test 5: Error Handling

**Purpose**: Verify error messages are clear and actionable

**Test 5A: Connection Without Credentials**
1. Remove OPENAI_API_KEY from .env
2. Restart server
3. Try to connect

**Expected Error**:
```
❌ OPENAI_API_KEY not configured
```

**Test 5B: Database Access Without Auth**
1. Log out of application
2. Try voice command

**Expected Error**:
```
Authentication required for voice inventory operations
```

**Test 5C: Invalid Voice Command**
1. Say something ambiguous or nonsensical
2. Assistant should ask for clarification

## Common Issues and Solutions

### Issue: Token endpoint returns 401 Unauthorized

**Cause**: Invalid or missing OPENAI_API_KEY

**Solution**:
```bash
# Check environment variable
echo $OPENAI_API_KEY

# Set in .env file
OPENAI_API_KEY=sk-your-actual-key-here

# Restart server
node server/index.js
```

### Issue: Tools show hasInvoke: false

**Cause**: Tools not properly imported or wrong SDK version

**Solution**:
```bash
# Verify @openai/agents package installed
npm list @openai/agents

# Reinstall if needed
npm install @openai/agents@latest

# Check imports in realtime-tools.ts
grep "from '@openai/agents/realtime'" src/lib/realtime-tools.ts
```

### Issue: "Unknown parameter: session.type" error

**Cause**: DataChannel patch not applied or failed

**Solution**:
1. Check for patch confirmation logs:
   ```
   ✅ DataChannel patch applied immediately after connection
   ```
2. If missing, check patch status in console:
   ```javascript
   // In browser console
   voiceClient.getConnectionStatus()
   // Should show: patchApplied: true, dataChannelPatched: true
   ```

### Issue: Voice commands don't create database records

**Cause**: Authentication or RLS policy issues

**Solution**:
```bash
# Check voice authentication status
# In browser console:
voiceClient.getDiagnosticInfo()

# Should show:
# voiceAuthStatus: { isAuthenticated: true, user: {...} }

# If false, ensure user is logged in
# Check RLS policies allow voice operations:
# - inventory_events table needs voice_insert policy
# - User must have authenticated session
```

### Issue: WebRTC falls back to WebSocket

**Cause**: WebRTC negotiation failed, system auto-fell-back

**Solution**:
1. Check browser console for fallback message:
   ```
   ⚠️ Negotiation failed; switching to WebSocket transport
   ```
2. Review SDP negotiation logs in server console
3. Verify ephemeral token format (should start with `ek_`)
4. Check if firewall or network blocking WebRTC

## Advanced Diagnostics

### Check Connection Status

```javascript
// In browser console
const status = voiceClient.getConnectionStatus();
console.log(JSON.stringify(status, null, 2));
```

**Expected Output**:
```json
{
  "isConnected": true,
  "transport": "webrtc",
  "connectionState": "connected",
  "iceConnectionState": "connected",
  "toolRegistrationStatus": "success",
  "hasCredentials": true,
  "credentialType": "ephemeralToken",
  "patchApplied": true,
  "patchDetails": {
    "constructorPatched": true,
    "sessionPatched": true,
    "dataChannelPatched": true
  },
  "initializationStrategy": "standard"
}
```

### View Connection Logs

```javascript
// In browser console
const logs = voiceClient.getConnectionLog();
logs.forEach(log => console.log(log));
```

### Check Diagnostic Info

```javascript
// In browser console
const diagnostics = voiceClient.getDiagnosticInfo();
console.log(JSON.stringify(diagnostics, null, 2));
```

## Performance Benchmarks

### Expected Performance Metrics

- **Token Generation**: < 2 seconds
- **WebRTC Connection**: < 5 seconds
- **Tool Execution**: < 1 second (local database)
- **Voice Response**: < 3 seconds (including AI processing)

### Monitoring Points

1. **Server Console**: Token generation time in correlation logs
2. **Browser Console**: Connection establishment duration
3. **Network Tab**: WebRTC STUN/TURN traffic
4. **Database Logs**: Query execution times

## Success Criteria Summary

### ✅ Complete Success
- All tests pass
- WebRTC connects without fallback
- All tools show `hasInvoke: true`
- Voice commands create database records
- Error messages are clear and actionable

### ⚠️ Partial Success
- WebSocket fallback works (WebRTC negotiation failed)
- Some tools work, others fail
- Authentication warnings but operations succeed

### ❌ Failure
- Cannot connect to OpenAI
- Tools not registered (hasInvoke: false)
- Database operations fail with RLS errors
- Constant "Unknown parameter" errors

## Next Steps After Validation

### If All Tests Pass ✅
1. Update memory-bank with successful implementation
2. Create regression test suite
3. Deploy to staging for further testing
4. Document lessons learned

### If Tests Fail ❌
1. Review error messages carefully
2. Check specific failing test against troubleshooting guide
3. Verify environment configuration
4. Review recent code changes for additional reversions
5. Consult CLAUDE.md for working implementation details

## Regression Prevention

### Recommended Tests to Add

```typescript
// Test: Verify tool method interface
describe('Voice Tool Interface', () => {
  it('should have invoke method on all tools', () => {
    const tools = [
      queryInventoryTool,
      addInventoryTool,
      updateInventoryTool,
      getTemperatureTool
    ];
    
    tools.forEach(tool => {
      expect(typeof tool.invoke).toBe('function');
      expect(tool.invoke).toBeDefined();
    });
  });
});

// Test: Verify server token endpoint
describe('Server Token Endpoint', () => {
  it('should use client_secrets endpoint', async () => {
    const response = await fetch('http://localhost:3001/api/voice/ephemeral-token', {
      method: 'POST'
    });
    const data = await response.json();
    
    expect(response.status).toBe(200);
    expect(data.client_secret).toMatch(/^ek_/);
  });
});
```

### Git Hooks

Add pre-commit hook to prevent changes to critical files:

```bash
#!/bin/bash
# .git/hooks/pre-commit

# Check for changes to critical WebRTC files
CRITICAL_FILES=(
  "server/index.js"
  "src/lib/ModernRealtimeVoiceClient.ts"
  "src/lib/realtime-tools.ts"
)

for file in "${CRITICAL_FILES[@]}"; do
  if git diff --cached --name-only | grep -q "$file"; then
    echo "⚠️  Warning: Modifying critical WebRTC file: $file"
    echo "   Review changes carefully against CLAUDE.md documentation"
    echo "   Ensure:"
    echo "   - Token endpoint uses /v1/realtime/client_secrets"
    echo "   - Tools check for .invoke method, not .execute"
    echo "   - DataChannel patching logic intact"
  fi
done
```

## Reference Documentation

- **Implementation Details**: `docs/WEBRTC_REGRESSION_FIX_REPORT.md`
- **Working State**: `CLAUDE.md` (September 29, 2025 section)
- **Troubleshooting**: `docs/VOICE_WEBRTC_TROUBLESHOOTING.md`
- **API Documentation**: OpenAI Realtime API docs

## Support Resources

### Debug Commands

```bash
# Check server environment
curl http://localhost:3001/api/debug/env | jq .

# Test API health
curl http://localhost:3001/api/health-status | jq .

# View server logs
tail -f server/logs/voice-assistant.log  # If logging to file
```

### Browser Console Helpers

```javascript
// Enable debug logs
localStorage.setItem('voice_debug', 'true');

// Check tool definitions
console.log(window.__voice_tools__);  // If exposed for debugging

// Monitor WebRTC stats
voiceClient.getConnectionStatus();
voiceClient.getDiagnosticInfo();
voiceClient.getConnectionLog();
```

## Validation Completion Checklist

When all tests pass, complete these steps:

- [ ] Document test results in project logs
- [ ] Update CLAUDE.md with validation date
- [ ] Create ticket for remaining improvements
- [ ] Share results with team
- [ ] Plan deployment to staging/production

## Emergency Rollback

If critical issues discovered:

```bash
# Revert server fix
git diff HEAD server/index.js
git checkout HEAD -- server/index.js

# Revert client fix  
git diff HEAD src/lib/ModernRealtimeVoiceClient.ts
git checkout HEAD -- src/lib/ModernRealtimeVoiceClient.ts

# Restart servers
npm run dev
```

## Contact and Support

- **Primary Documentation**: `CLAUDE.md`
- **Fix Report**: `docs/WEBRTC_REGRESSION_FIX_REPORT.md`
- **Troubleshooting**: `docs/VOICE_WEBRTC_TROUBLESHOOTING.md`

---

**Note**: This validation guide should be executed after every WebRTC-related code change to prevent future regressions.