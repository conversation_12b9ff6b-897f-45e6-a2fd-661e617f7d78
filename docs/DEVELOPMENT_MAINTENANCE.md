# Development Maintenance Guide

Long-running development sessions for Pacific Cloud Seafoods can accumulate caches, Docker artifacts, and logs that lead to memory exhaustion and Docker instability. This guide explains how to use the maintenance toolkit to keep your environment reliable.

## Overview

The maintenance system addresses the most common local environment issues:

- Build caches (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, npm) expanding over time
- Docker images, volumes, and build cache consuming disk space
- Supabase local volumes growing without compaction
- Logs and diagnostics increasing disk usage
- Memory leaks from long-lived Node.js processes

## Quick Start

```bash
# Daily lightweight cleanup
npm run maintenance:light

# Weekly cleanup including Docker dangling resources
npm run maintenance:medium

# Monthly deep cleanup
npm run maintenance:deep

# Resource analysis without deleting anything
npm run maintenance:analyze

# Continuous monitoring during dev sessions
npm run monitor:watch
```

## Maintenance Scripts

### `scripts/dev-maintenance.sh`

**Purpose:** Unified cache, log, Docker, and Supabase maintenance with selectable intensity.

**Levels:**

- **Light**: Removes project build artifacts, node caches, temporary reports
- **Medium**: Light level + npm/npx caches, old logs, Docker dangling resources, migration clutter
- **Deep**: Medium level + pnpm/yarn caches, full Docker image/cache pruning, Supabase vacuum

**Options:**

- `--level=light|medium|deep` – Select cleanup intensity (default: medium)
- `--dry-run` – Show planned actions
- `--auto-confirm` – Skip prompts (useful for automation)
- `--keep-docker` – Skip Docker maintenance
- `--keep-logs` – Skip log cleanup
- `--analyze-only` – Only emit resource analysis

**Usage examples:**

```bash
# Daily baseline cleanup
bash scripts/dev-maintenance.sh --level=light

# Weekly maintenance with confirmation suppressed
bash scripts/dev-maintenance.sh --level=medium --auto-confirm

# Deep cleanup preview
bash scripts/dev-maintenance.sh --level=deep --dry-run
```

### `scripts/docker-maintenance.sh`

**Purpose:** Dedicated Docker optimization beyond the standard light prune.

**Capabilities:**

- Stops exited containers, prunes dangling resources
- Keeps the newest N images per repository (`--keep-images`)
- Measures build cache usage and prunes when exceeding `--max-build-cache`
- Identifies orphaned volumes, optionally backs them up (`--backup-volumes`)
- Preserves Supabase-related resources by default (`--preserve-supabase`)

**Common commands:**

```bash
# Standard Docker cleanup while keeping Supabase data
bash scripts/docker-maintenance.sh

# Aggressive cleanup keeping two images per repo and backing up volumes
bash scripts/docker-maintenance.sh --aggressive --backup-volumes --keep-images=2
```

### `scripts/monitor-dev-resources.sh`

**Purpose:** Observe memory, disk, and Docker usage before issues arise.

**Features:**

- Reports free system memory and disk space
- Lists cache directory sizes and highlights the largest offenders
- Displays Docker disk usage and container stats
- Monitors Node.js processes using notable resources
- Emits JSON for automation or log ingestion

**Common commands:**

```bash
# One-time check
npm run monitor:resources

# Continuous monitoring with 45s interval
bash scripts/monitor-dev-resources.sh --watch --interval=45

# Generate JSON report for audits
npm run monitor:report
```

**Thresholds:**

- Memory warning default: 4 GB free
- Disk warning default: 10 GB free
- Override using `--threshold-memory` or `--threshold-disk`

### `scripts/rotate-logs.sh`

**Purpose:** Manage log retention and prevent growth of report directories.

**Behaviors:**

- Removes, compresses, or archives logs older than `--days`
- Enforces total size limit with `--max-size`
- Truncates Docker and Supabase JSON logs
- Cleans temporary files in `tmp/`

**Examples:**

```bash
# Compress logs older than 7 days, keep total under 5GB
bash scripts/rotate-logs.sh --compress

# Archive logs older than 14 days and cap at 3GB
bash scripts/rotate-logs.sh --days=14 --max-size=3 --archive
```

### `scripts/emergency-cleanup.sh`

**Purpose:** Fast “break glass” script for critical disk or memory shortages.

**Actions:**

- Kills Node.js processes and stops Docker containers
- Removes caches, build artifacts, and temporary files
- Truncates logs (unless `--preserve-logs` is set)
- Aggressively prunes Docker resources while preserving Supabase data by default

**Usage:**

```bash
# Immediate emergency cleanup (default preserves Supabase data)
bash scripts/emergency-cleanup.sh

# When Docker resources must be kept
bash scripts/emergency-cleanup.sh --preserve-docker

# Nuclear option (erases Supabase volumes; confirmation required)
bash scripts/emergency-cleanup.sh --nuclear
```

## Troubleshooting

### Memory Issues

- Run `npm run maintenance:medium`
- Use `npm run monitor:watch` to observe a suspected leak
- Restart long-lived Node servers after intensive testing

### Docker Hanging

- Check Docker health with `bash scripts/docker-maintenance.sh --maintenance`
- Use the deep maintenance level if build cache exceeds limits
- Preserve Supabase volumes unless you have a recent backup

### Disk Space Pressure

- Start with `npm run maintenance:logs`
- Follow with `npm run maintenance:medium`
- Remove unnecessary Playwright reports or video recordings

## Best Practices

- **Daily:** Run `npm run maintenance:light` before starting the dev server
- **Weekly:** Execute `npm run maintenance:medium`, especially after heavy feature work
- **Monthly:** Plan a full `npm run maintenance:deep` and restart services afterward
- **During Long Sessions:** Launch dev via `npm run dev:monitored` to track resources in real time

## Automation

Add a cron entry to maintain a clean workspace:

```cron
# Daily 9 AM maintenance
0 9 * * * cd /path/to/Seafood-Manager && npm run maintenance:light

# Weekly Sunday deep maintenance
0 3 * * 0 cd /path/to/Seafood-Manager && npm run maintenance:medium --silent
```

CI pipelines can reuse the tooling via the `maintenance:analyze` and `maintenance:deep` scripts for ephemeral environments.

## Resource Thresholds

Recommended alerts for local development:

- Memory: warning at 4 GB free, critical at 6 GB consumed by a single process
- Disk: warning at 10 GB available; critical at 5 GB
- Docker build cache: limit to 10 GB (default in `docker-maintenance.sh`)
- Logs: rotate anything older than 7 days; delete or archive after 90 days

## Integration with Existing Scripts

- `start-all-services.sh`: run `npm run maintenance:light` before starting services on shared machines
- `stop-all-services.sh`: follow with `npm run maintenance:medium` for nightly cleanups
- `prune-docker-safely.sh`: still available for Supabase-only pruning; the new Docker maintenance script adds broader control

## Monitoring and Alerts

- `monitor-dev-resources.sh --watch` logs to `tmp/resource-monitoring.log`
- Parse JSON output (`--json` or `--report`) with existing monitoring pipelines
- Combine with macOS notifications or terminal alerts for proactive warnings

## Recovery Procedures

1. **Out of memory:** Stop dev server, run `bash scripts/emergency-cleanup.sh`, restart environment
2. **Docker unresponsive:** Quit Docker Desktop, run `bash scripts/docker-maintenance.sh --aggressive`, relaunch Docker, then restart services
3. **Disk full:** Execute `bash scripts/emergency-cleanup.sh --preserve-docker`, archive or delete large artifacts
4. **Services fail on restart:** Run `npm install` (if caches cleared), `npm run start-all`, and review `/tmp/emergency-cleanup.log`

## FAQ

- **How often should maintenance run?** Daily light, weekly medium, monthly deep cleans are recommended.
- **Is deep cleanup safe?** Yes, it preserves Supabase data by default and prompts before destructive actions.
- **Can I automate monitoring?** Yes, use `npm run monitor:report` in scheduled tasks or integrate JSON output with alerting systems.
- **What if services fail after cleanup?** Reinstall dependencies and restart services; logs in `/tmp/emergency-cleanup.log` detail removals.
- **Does cleanup touch source code?** No; scripts operate only on caches, logs, Docker artifacts, and temporary files.
