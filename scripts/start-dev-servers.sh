#!/usr/bin/env bash

# =============================================================================
# Seafood Manager - Core Development Servers Starter
# =============================================================================
# Boots the standard local services required for day-to-day development:
#   1. Express API gateway (server/index.js) on port 3001
#   2. TempStick CORS proxy (cors-proxy-server.js) on port 3002
#   3. Voice agent service (src/services/voice-agent/server.ts) on port 3003
#   4. Vite frontend dev server on port 5177 (strictPort)
#
# All child processes are started in the background and terminated automatically
# when this script exits. Logs are written to tmp/dev-logs/ for easy inspection.
# =============================================================================

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
LOG_DIR="${PROJECT_ROOT}/tmp/dev-logs"
PID_DIR="${PROJECT_ROOT}/tmp/dev-pids"

VITE_PORT=${VITE_PORT:-5177}
EXPRESS_PORT=${EXPRESS_PORT:-3001}
CORS_PROXY_PORT=${CORS_PROXY_PORT:-3002}
VOICE_AGENT_PORT=${VOICE_AGENT_PORT:-3003}

mkdir -p "${LOG_DIR}" "${PID_DIR}"

PIDS=()

log() {
  local level=$1
  shift
  printf '[%s] %s\n' "${level}" "$*"
}

ensure_command() {
  local cmd=$1
  if ! command -v "${cmd}" >/dev/null 2>&1; then
    log 'ERROR' "Required command '${cmd}' not found in PATH."
    exit 1
  fi
}

ensure_port_free() {
  local port=$1
  local name=$2
  if lsof -i ":${port}" >/dev/null 2>&1; then
    log 'ERROR' "${name} port ${port} is already in use. Stop the existing process or set a different port."
    exit 1
  fi
}

start_service() {
  local name=$1
  local logfile=$2
  local command=$3

  log 'INFO' "Starting ${name}…"
  (
    cd "${PROJECT_ROOT}" && \
      bash -c "${command}" >"${logfile}" 2>&1 &
    echo $! >"${PID_DIR}/${name}.pid"
  )

  local pid
  pid=$(cat "${PID_DIR}/${name}.pid")
  PIDS+=("${pid}")
  log 'INFO' "${name} started (PID ${pid}). Logs: ${logfile}"
}

cleanup() {
  local exit_code=$?
  log 'INFO' 'Stopping development servers…'
  for pid in "${PIDS[@]:-}"; do
    if kill -0 "${pid}" >/dev/null 2>&1; then
      kill "${pid}" >/dev/null 2>&1 || true
      wait "${pid}" >/dev/null 2>&1 || true
    fi
  done
  rm -f "${PID_DIR}"/*.pid 2>/dev/null || true
  log 'INFO' 'Shutdown complete.'
  exit "${exit_code}"
}

trap cleanup EXIT INT TERM

log 'INFO' 'Validating prerequisites…'
ensure_command node
ensure_command npm
ensure_command npx

log 'INFO' 'Checking for port availability…'
ensure_port_free "${EXPRESS_PORT}" 'Express API'
ensure_port_free "${CORS_PROXY_PORT}" 'TempStick proxy'
ensure_port_free "${VOICE_AGENT_PORT}" 'Voice agent'
ensure_port_free "${VITE_PORT}" 'Vite dev server'

log 'INFO' 'Launching development servers…'

start_service 'express-api' "${LOG_DIR}/express-api.log" "PORT=${EXPRESS_PORT} node server/index.js"
start_service 'tempstick-proxy' "${LOG_DIR}/tempstick-proxy.log" "node cors-proxy-server.js"
start_service 'voice-agent' "${LOG_DIR}/voice-agent.log" "VOICE_AGENT_PORT=${VOICE_AGENT_PORT} ./start-voice-agent.sh"
start_service 'vite-dev-server' "${LOG_DIR}/vite-dev-server.log" "npm run dev"

log 'INFO' 'All services are starting. Logs are under tmp/dev-logs/. Press Ctrl+C to stop.'
log 'INFO' "Endpoints:" 
log 'INFO' "  • Express API:        http://localhost:${EXPRESS_PORT}"
log 'INFO' "  • TempStick Proxy:    http://localhost:${CORS_PROXY_PORT}"
log 'INFO' "  • Voice Agent:        ws://localhost:${VOICE_AGENT_PORT}/voice-agent"
log 'INFO' "  • Frontend (Vite):    http://localhost:${VITE_PORT}"

wait
