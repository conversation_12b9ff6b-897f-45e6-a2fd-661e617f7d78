#!/usr/bin/env bash

set -euo pipefail

##################################################
# Pacific Cloud Seafoods - Emergency Cleanup Tool
##################################################

SCRIPT_NAME="$(basename "$0")"
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}" )"/.. && pwd)"
LOG_FILE="/tmp/emergency-cleanup.log"
PRESERVE_DOCKER=false
PRESERVE_LOGS=false
PRESERVE_SUPABASE=true
NUCLEAR=false

log_ts() {
  date '+%Y-%m-%d %H:%M:%S'
}

log() {
  local level="$1"; shift
  printf '[%s] [%s] %s\n' "$(log_ts)" "$level" "$*" | tee -a "$LOG_FILE" >&2
}

usage() {
  cat <<EOF
Usage: ${SCRIPT_NAME} [options]

Options:
  --preserve-docker     Do not remove Docker resources
  --preserve-logs       Do not truncate log files
  --preserve-supabase   Preserve Supabase volumes (default)
  --nuclear             Remove all resources including Supabase data (requires confirmation)
  --help                Show this help message
EOF
}

confirm_nuclear() {
  read -r -p "This will remove Supabase data and ALL Docker volumes. Type 'DELETE EVERYTHING' to continue: " response
  [[ "$response" == "DELETE EVERYTHING" ]]
}

parse_args() {
  for arg in "$@"; do
    case "$arg" in
      --preserve-docker) PRESERVE_DOCKER=true ;;
      --preserve-logs) PRESERVE_LOGS=true ;;
      --preserve-supabase) PRESERVE_SUPABASE=true ;;
      --nuclear) NUCLEAR=true ;;
      --help|-h) usage; exit 0 ;;
      *) log ERROR "Unknown option: $arg"; usage; exit 1 ;;
    esac
  done
  if $NUCLEAR && ! confirm_nuclear; then
    log ERROR "Confirmation failed. Aborting nuclear cleanup."
    exit 1
  fi
  if $NUCLEAR; then
    PRESERVE_SUPABASE=false
  fi
}

kill_node_processes() {
  log INFO "Terminating Node.js processes"
  pkill -f "node" 2>/dev/null || true
}

stop_docker() {
  if $PRESERVE_DOCKER; then
    log INFO "Preserving Docker resources (per flag)"
    return
  fi
  log INFO "Stopping Docker containers"
  docker stop $(docker ps -q) >/dev/null 2>&1 || true
}

remove_node_caches() {
  log INFO "Removing Node.js caches"
  rm -rf \
    "${PROJECT_ROOT}/node_modules/.vite" \
    "${PROJECT_ROOT}/node_modules/.vitest" \
    "${PROJECT_ROOT}/node_modules/.cache" \
    "${PROJECT_ROOT}/node_modules/.parcel-cache" \
    "${PROJECT_ROOT}/.parcel-cache" \
    "${PROJECT_ROOT}/dist" \
    "${PROJECT_ROOT}/coverage" \
    "${PROJECT_ROOT}/playwright-report" \
    "${PROJECT_ROOT}/test-results" \
    "$HOME/.npm/_cacache" \
    "$HOME/.npm/_npx" \
    "$HOME/Library/Caches/ms-playwright" \
    "$HOME/.cache/ms-playwright" \
    "$HOME/.cache/pnpm" \
    "$HOME/Library/Caches/npm" \
    "$HOME/.cache/yarn" 2>/dev/null || true
}

remove_temp_files() {
  log INFO "Clearing temporary files"
  rm -rf "${PROJECT_ROOT}/tmp"/* 2>/dev/null || true
}

truncate_logs() {
  if $PRESERVE_LOGS; then
    log INFO "Preserving logs (per flag)"
    return
  fi
  log INFO "Truncating log files"
  find "${PROJECT_ROOT}" -type f -name '*.log' -print0 2>/dev/null | xargs -0 truncate -s 0 2>/dev/null || true
}

cleanup_docker() {
  if $PRESERVE_DOCKER; then
    return
  fi
  log INFO "Aggressively cleaning Docker resources"
  docker container prune -f >/dev/null 2>&1 || true
  docker image prune -a -f >/dev/null 2>&1 || true
  if $PRESERVE_SUPABASE; then
    docker volume ls -q | while read -r vol; do
      if docker inspect volume "$vol" | jq -re '.Labels | to_entries[] | select(.value | test("supabase"; "i"))' >/dev/null 2>&1; then
        log INFO "Preserving Supabase volume $vol"
        continue
      fi
      docker volume rm "$vol" >/dev/null 2>&1 || true
    done
  else
    docker volume prune -f >/dev/null 2>&1 || true
  fi
  docker builder prune -a -f >/dev/null 2>&1 || true
  docker network prune -f >/dev/null 2>&1 || true
}

clear_playwright_browsers() {
  log INFO "Removing Playwright browser binaries"
  rm -rf "$HOME/Library/Caches/ms-playwright" "$HOME/.cache/ms-playwright" 2>/dev/null || true
}

clear_system_cache() {
  log INFO "Clearing macOS user caches"
  rm -rf "$HOME/Library/Caches"/* 2>/dev/null || true
}

main() {
  parse_args "$@"
  log INFO "Starting emergency cleanup"
  kill_node_processes
  stop_docker
  remove_node_caches
  remove_temp_files
  truncate_logs
  cleanup_docker
  clear_playwright_browsers
  clear_system_cache
  log SUCCESS "Emergency cleanup completed. Review ${LOG_FILE} for details."
}

main "$@"
