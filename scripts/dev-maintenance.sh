#!/usr/bin/env bash

set -euo pipefail

###############################################
# Pacific Cloud Seafoods - Dev Maintenance Tool
###############################################

SCRIPT_NAME="$(basename "$0")"
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")"/.. && pwd)"
LOG_DIR="${PROJECT_ROOT}/tmp"
LOG_FILE="${LOG_DIR}/dev-maintenance.log"
SUMMARY_FILE="${LOG_DIR}/dev-maintenance-summary.log"
DEFAULT_LEVEL="medium"
DRY_RUN=false
AUTO_CONFIRM=false
KEEP_DOCKER=false
KEEP_LOGS=false
ANALYZE_ONLY=false
LEVEL="$DEFAULT_LEVEL"
START_DISK_FREE=""
END_DISK_FREE=""
LIGHT_TARGETS=(
  "${PROJECT_ROOT}/dist"
  "${PROJECT_ROOT}/coverage"
  "${PROJECT_ROOT}/playwright-report"
  "${PROJECT_ROOT}/test-results"
  "${PROJECT_ROOT}/.lighthouseci"
  "${PROJECT_ROOT}/node_modules/.vite"
  "${PROJECT_ROOT}/node_modules/.vitest"
  "${PROJECT_ROOT}/node_modules/.cache"
  "${PROJECT_ROOT}/node_modules/.parcel-cache"
  "${PROJECT_ROOT}/.parcel-cache"
  "${PROJECT_ROOT}/.turbo"
)

MEDIUM_TARGETS=(
  "${HOME}/.npm/_cacache"
  "${HOME}/.npm/_npx"
  "${HOME}/Library/Caches/ms-playwright"
  "${HOME}/.cache/ms-playwright"
)

DEEP_TARGETS=(
  "${HOME}/.cache/pnpm"
  "${HOME}/Library/Caches/npm"
  "${HOME}/.cache/yarn"
)

mkdir -p "${LOG_DIR}"

log_ts() {
  date '+%Y-%m-%d %H:%M:%S'
}

log_prefix() {
  echo -n "[$(log_ts)] [$SCRIPT_NAME]"
}

log() {
  local level="$1"; shift
  local color_reset='\033[0m'
  local color
  case "$level" in
    INFO) color='\033[1;34m' ;;
    WARN) color='\033[1;33m' ;;
    ERROR) color='\033[1;31m' ;;
    SUCCESS) color='\033[1;32m' ;;
    *) color='\033[0m' ;;
  esac
  printf "%b%s %s%b\n" "${color}" "$(log_prefix) [$level]" "$*" "${color_reset}" | tee -a "${LOG_FILE}" >&2
}

confirm() {
  local message="$1"
  if $AUTO_CONFIRM; then
    return 0
  fi
  read -r -p "${message} [y/N]: " response
  [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]
}

ensure_services_stopped() {
  local active_services
  active_services=$(pgrep -af "(vite|server/index.js|supabase start)" 2>/dev/null || true)
  if [[ -z "$active_services" ]]; then
    return
  fi
  log WARN "Detected running development services:\n$active_services"
  if ! confirm "Services appear to be running. Continue with cleanup?"; then
    log WARN "Cleanup aborted to protect running services"
    exit 0
  fi
}

usage() {
  cat <<EOF
Usage: ${SCRIPT_NAME} [options]

Options:
  --level=light|medium|deep   Cleanup intensity (default: ${DEFAULT_LEVEL})
  --dry-run                   Show actions without executing
  --auto-confirm              Skip confirmation prompts
  --keep-docker               Skip Docker cleanup
  --keep-logs                 Skip log cleanup
  --analyze-only              Run resource analysis without cleanup
  --help                      Show this help message

Examples:
  ${SCRIPT_NAME} --level=light
  ${SCRIPT_NAME} --level=deep --auto-confirm
  ${SCRIPT_NAME} --analyze-only
EOF
}

parse_args() {
  for arg in "$@"; do
    case "$arg" in
      --level=*) LEVEL="${arg#*=}" ;;
      --dry-run) DRY_RUN=true ;;
      --auto-confirm) AUTO_CONFIRM=true ;;
      --keep-docker) KEEP_DOCKER=true ;;
      --keep-logs) KEEP_LOGS=true ;;
      --analyze-only) ANALYZE_ONLY=true ;;
      --help|-h) usage; exit 0 ;;
      *) log ERROR "Unknown option: $arg"; usage; exit 1 ;;
    esac
  done

  case "$LEVEL" in
    light|medium|deep) ;;
    *) log ERROR "Invalid cleanup level: $LEVEL"; exit 1 ;;
  esac
}

record_disk_usage() {
  local label="$1"
  df -h . | sed '1,1!d' >/dev/null 2>&1 || true
  local free=$(df -Pk . | awk 'NR==2 {print $4}')
  if [[ "$label" == "start" ]]; then
    START_DISK_FREE="$free"
  else
    END_DISK_FREE="$free"
  fi
}

bytes_to_human() {
  local bytes="$1"
  local kib=$((1024))
  local mib=$((kib * 1024))
  local gib=$((mib * 1024))
  if (( bytes >= gib )); then
    printf "%.2f GiB" "$(echo "$bytes / $gib" | bc -l)"
  elif (( bytes >= mib )); then
    printf "%.2f MiB" "$(echo "$bytes / $mib" | bc -l)"
  elif (( bytes >= kib )); then
    printf "%.2f KiB" "$(echo "$bytes / $kib" | bc -l)"
  else
    printf "%s B" "$bytes"
  fi
}

estimate_path_size() {
  local path="$1"
  if [[ ! -e "$path" ]]; then
    echo 0
    return
  fi
  local size
  size=$(du -sk "$path" 2>/dev/null | awk '{print $1}') || size=0
  echo $((size * 1024))
}

cleanup_path() {
  local path="$1"
  local size_before
  size_before=$(estimate_path_size "$path")
  if (( size_before == 0 )); then
    return
  fi
  if $DRY_RUN; then
    log INFO "[Dry Run] Would remove $(bytes_to_human "$size_before") from $path"
    return
  fi
  if rm -rf "$path"; then
    log SUCCESS "Removed $(bytes_to_human "$size_before") from $path"
  else
    log WARN "Failed to remove $path"
  fi
}

cleanup_targets() {
  local -n targets_ref=$1
  for target in "${targets_ref[@]}"; do
    cleanup_path "$target"
  done
}

cleanup_logs() {
  if $KEEP_LOGS; then
    log INFO "Skipping log cleanup (--keep-logs)"
    return
  fi
  local log_dirs=(
    "${PROJECT_ROOT}/tmp"
    "${PROJECT_ROOT}/playwright-report"
    "${PROJECT_ROOT}/test-results"
  )
  for dir in "${log_dirs[@]}"; do
    if [[ -d "$dir" ]]; then
      find "$dir" -type f -name '*.log' -mtime +14 -print0 2>/dev/null | while IFS= read -r -d '' file; do
        if $DRY_RUN; then
          log INFO "[Dry Run] Would remove log $file"
        else
          rm -f "$file" && log SUCCESS "Removed old log $file"
        fi
      done
    fi
  done
}

cleanup_docker_light() {
  log INFO "Pruning dangling Docker data (light)"
  local cmd=(docker system prune -f)
  if $DRY_RUN; then
    log INFO "[Dry Run] ${cmd[*]}"
  else
    if docker system prune -f >/dev/null; then
      log SUCCESS "Docker system prune completed"
    else
      log WARN "Docker system prune failed"
    fi
  fi
}

cleanup_docker_medium() {
  log INFO "Pruning unused Docker images and containers"
  if $DRY_RUN; then
    log INFO "[Dry Run] docker container prune -f"
    log INFO "[Dry Run] docker image prune -f"
    log INFO "[Dry Run] docker volume prune -f"
    return
  fi
  docker container prune -f >/dev/null && log SUCCESS "Removed stopped containers" || log WARN "Container prune failed"
  docker image prune -f >/dev/null && log SUCCESS "Removed dangling images" || log WARN "Image prune failed"
  docker volume prune -f >/dev/null && log SUCCESS "Removed dangling volumes" || log WARN "Volume prune failed"
}

cleanup_docker_deep() {
  log INFO "Deep Docker cleanup - removing build cache and unused data"
  if $DRY_RUN; then
    log INFO "[Dry Run] docker builder prune -a -f"
    log INFO "[Dry Run] docker image prune -a -f"
    return
  fi
  docker builder prune -a -f >/dev/null && log SUCCESS "Removed Docker build cache" || log WARN "Builder prune failed"
  docker image prune -a -f >/dev/null && log SUCCESS "Removed unused images" || log WARN "Image prune -a failed"
}

supabase_vacuum() {
  local container
  container=$(docker ps --filter "name=supabase_db" --format '{{.ID}}')
  if [[ -z "$container" ]]; then
    log WARN "Supabase DB container not running - skipping vacuum"
    return
  fi
  local vacuum_cmd="VACUUM ANALYZE;"
  if $DRY_RUN; then
    log INFO "[Dry Run] Would run VACUUM ANALYZE on Supabase container $container"
  else
    if docker exec "$container" psql -U postgres -d postgres -c "${vacuum_cmd}" >/dev/null; then
      log SUCCESS "Supabase vacuum completed"
    else
      log WARN "Supabase vacuum failed"
    fi
  fi
}

cleanup_migrations_cache() {
  local migrations_dir="${PROJECT_ROOT}/supabase/migrations"
  if [[ ! -d "$migrations_dir" ]]; then
    return
  fi
  find "$migrations_dir" -name '*.sql~' -type f -print0 2>/dev/null | while IFS= read -r -d '' stale; do
    if $DRY_RUN; then
      log INFO "[Dry Run] Would remove stale migration backup $stale"
    else
      rm -f "$stale" && log SUCCESS "Removed stale migration backup $stale"
    fi
  done
}

memory_analysis() {
  log INFO "Collecting memory usage information"
  local sys_mem
  if command -v vm_stat >/dev/null; then
    local page_size=$(vm_stat | awk '/page size of/ {print $8}')
    local free_pages=$(vm_stat | awk '/Pages free/ {print $3}' | tr -d '.')
    local inactive_pages=$(vm_stat | awk '/Pages inactive/ {print $3}' | tr -d '.')
    local free_bytes=$(((free_pages + inactive_pages) * page_size))
    log INFO "System free memory: $(bytes_to_human "$free_bytes")"
  elif command -v free >/dev/null; then
    sys_mem=$(free -b | awk 'NR==2 {print $4}')
    log INFO "System free memory: $(bytes_to_human "$sys_mem")"
  fi

  log INFO "Top memory-consuming Node processes:"
  ps -o pid,%mem,%cpu,etime,command -p $(pgrep -f "node" | tr '\n' ',') 2>/dev/null | head -n 10 || log INFO "No active Node processes found"

  if command -v docker >/dev/null; then
    log INFO "Docker stats snapshot:"
    docker stats --no-stream --format "table {{.Name}}\t{{.MemUsage}}\t{{.CPUPerc}}\t{{.NetIO}}" 2>/dev/null | head -n 6 || log WARN "Unable to gather Docker stats"
  fi
}

report_cleanup() {
  if [[ -n "$START_DISK_FREE" && -n "$END_DISK_FREE" ]]; then
    local freed=$((END_DISK_FREE - START_DISK_FREE))
    if (( freed > 0 )); then
      log SUCCESS "Estimated space freed: $(bytes_to_human $((freed * 1024)))"
    else
      log INFO "No additional free space detected (may require sudo for accurate measurement)"
    fi
  fi

  {
    echo "$(log_ts) Level: $LEVEL | Dry Run: $DRY_RUN | Keep Docker: $KEEP_DOCKER | Keep Logs: $KEEP_LOGS | Analyze Only: $ANALYZE_ONLY | SpaceFreedBytes=$(( (END_DISK_FREE-START_DISK_FREE) * 1024 ))"
  } >>"${SUMMARY_FILE}"
}

main() {
  parse_args "$@"
  record_disk_usage start
  log INFO "Starting development maintenance (level: $LEVEL)"
  memory_analysis

  if $ANALYZE_ONLY; then
    log INFO "Analyze-only mode complete"
    exit 0
  fi

  if ! $DRY_RUN && ! $AUTO_CONFIRM; then
    if ! confirm "Proceed with cleanup operations?"; then
      log WARN "Cleanup aborted by user"
      exit 0
    fi
  fi

  if [[ "$LEVEL" == "deep" ]]; then
    ensure_services_stopped
  fi

  cleanup_targets LIGHT_TARGETS

  if [[ "$LEVEL" == "medium" || "$LEVEL" == "deep" ]]; then
    cleanup_targets MEDIUM_TARGETS
    cleanup_logs
    cleanup_migrations_cache
    if ! $KEEP_DOCKER && command -v docker >/dev/null; then
      cleanup_docker_medium
    fi
  fi

  if [[ "$LEVEL" == "deep" ]]; then
    cleanup_targets DEEP_TARGETS
    if ! $KEEP_DOCKER && command -v docker >/dev/null; then
      cleanup_docker_deep
    fi
    supabase_vacuum
  else
    if ! $KEEP_DOCKER && command -v docker >/dev/null; then
      cleanup_docker_light
    fi
  fi

  record_disk_usage end
  report_cleanup
  log SUCCESS "Development maintenance completed"
}

main "$@"
