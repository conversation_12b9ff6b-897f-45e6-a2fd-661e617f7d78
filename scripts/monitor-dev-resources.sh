#!/usr/bin/env bash

set -euo pipefail

###############################################
# Pacific Cloud Seafoods - Dev Resource Monitor
###############################################

SCRIPT_NAME="$(basename "$0")"
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")"/.. && pwd)"
LOG_DIR="${PROJECT_ROOT}/tmp"
LOG_FILE="${LOG_DIR}/resource-monitoring.log"
REPORT_PREFIX="${LOG_DIR}/resource-report"
JSON_OUTPUT=false
WATCH_MODE=false
WATCH_INTERVAL=30
THRESHOLD_MEMORY_MB=4096
THRESHOLD_DISK_GB=10
ALERT_ONLY=false
REPORT_MODE=false

mkdir -p "${LOG_DIR}"

ts() {
  date '+%Y-%m-%d %H:%M:%S'
}

log() {
  local level="$1"; shift
  local msg="$*"
  printf '[%s] [%s] %s\n' "$(ts)" "$level" "$msg" | tee -a "$LOG_FILE" >&2
}

usage() {
  cat <<EOF
Usage: ${SCRIPT_NAME} [options]

Options:
  --watch                   Continuously monitor resources (default interval 30s)
  --interval=SECONDS        Interval for watch mode (default: 30)
  --json                    Output metrics in JSON format
  --threshold-memory=MB     Memory warning threshold (default: ${THRESHOLD_MEMORY_MB}MB)
  --threshold-disk=GB       Disk warning threshold (default: ${THRESHOLD_DISK_GB}GB)
  --alert-only              Only print warnings when thresholds are exceeded
  --report                  Generate a detailed resource report and exit
  --help                    Show this help message
EOF
}

parse_args() {
  for arg in "$@"; do
    case "$arg" in
      --watch) WATCH_MODE=true ;;
      --interval=*) WATCH_INTERVAL="${arg#*=}" ;;
      --json) JSON_OUTPUT=true ;;
      --threshold-memory=*) THRESHOLD_MEMORY_MB="${arg#*=}" ;;
      --threshold-disk=*) THRESHOLD_DISK_GB="${arg#*=}" ;;
      --alert-only) ALERT_ONLY=true ;;
      --report) REPORT_MODE=true ;;
      --help|-h) usage; exit 0 ;;
      *) log "ERROR" "Unknown option: $arg"; usage; exit 1 ;;
    esac
  done
}

bytes_to_human() {
  local bytes="$1"
  local kib=$((1024))
  local mib=$((kib * 1024))
  local gib=$((mib * 1024))
  if (( bytes >= gib )); then
    printf "%.2f GiB" "$(echo "$bytes / $gib" | bc -l)"
  elif (( bytes >= mib )); then
    printf "%.2f MiB" "$(echo "$bytes / $mib" | bc -l)"
  elif (( bytes >= kib )); then
    printf "%.2f KiB" "$(echo "$bytes / $kib" | bc -l)"
  else
    printf "%s B" "$bytes"
  fi
}

collect_system_memory() {
  if command -v vm_stat >/dev/null; then
    local page_size=$(vm_stat | awk '/page size of/ {print $8}')
    local free_pages=$(vm_stat | awk '/Pages free/ {print $3}' | tr -d '.')
    local inactive_pages=$(vm_stat | awk '/Pages inactive/ {print $3}' | tr -d '.')
    local speculative_pages=$(vm_stat | awk '/Pages speculative/ {print $3}' | tr -d '.')
    local free_bytes=$(((free_pages + inactive_pages + speculative_pages) * page_size))
    echo "$free_bytes"
  elif command -v free >/dev/null; then
    free -b | awk 'NR==2 {print $4}'
  else
    echo 0
  fi
}

collect_disk_free() {
  df -Pk . | awk 'NR==2 {print $4 * 1024}'
}

collect_node_processes() {
  if ! pgrep -f "node" >/dev/null; then
    echo ""
    return
  fi
  ps -o pid,%mem,%cpu,etime,command -p $(pgrep -f "node" | tr '\n' ',') 2>/dev/null | tail -n +2
}

collect_cache_sizes() {
  local -a paths=(
    "${PROJECT_ROOT}/node_modules/.vite"
    "${PROJECT_ROOT}/node_modules/.vitest"
    "${PROJECT_ROOT}/node_modules/.cache"
    "${PROJECT_ROOT}/node_modules/.parcel-cache"
    "${PROJECT_ROOT}/.parcel-cache"
    "${PROJECT_ROOT}/playwright-report"
    "${PROJECT_ROOT}/test-results"
    "${HOME}/.npm/_cacache"
    "${HOME}/.npm/_npx"
    "${HOME}/Library/Caches/ms-playwright"
    "${HOME}/.cache/ms-playwright"
  )
  local total=0
  printf '{'
  local first=true
  for path in "${paths[@]}"; do
    local size=0
    if [[ -e "$path" ]]; then
      size=$(du -sk "$path" 2>/dev/null | awk '{print $1 * 1024}')
      total=$((total + size))
    fi
    if ! $first; then printf ','; fi
    printf '"%s":%s' "${path}" "$size"
    first=false
  done
  printf ',"__total__":%s}' "$total"
}

collect_docker_stats() {
  if ! command -v docker >/dev/null; then
    echo '{}'
    return
  fi
  local stats="$(docker system df --format '{{json .}}' 2>/dev/null || true)"
  if [[ -z "$stats" ]]; then
    echo '{}'
    return
  fi
  printf '[%s]' "$(echo "$stats" | paste -sd ',' -)"
}

collect_container_usage() {
  if ! command -v docker >/dev/null; then
    echo '[]'
    return
  fi
  local stats
  stats=$(docker stats --no-stream --format '{{json .}}' 2>/dev/null || true)
  if [[ -z "$stats" ]]; then
    echo '[]'
    return
  fi
  echo "$stats" | paste -sd ',' - | sed 's/^/[/' | sed 's/$/]/'
}

check_thresholds() {
  local free_mem_bytes="$1"
  local free_disk_bytes="$2"
  local mem_limit=$((THRESHOLD_MEMORY_MB * 1024 * 1024))
  local disk_limit=$((THRESHOLD_DISK_GB * 1024 * 1024 * 1024))
  local alerts=()
  if (( free_mem_bytes < mem_limit )); then
    alerts+=("Low memory: $(bytes_to_human "$free_mem_bytes") free")
  fi
  if (( free_disk_bytes < disk_limit )); then
    alerts+=("Low disk space: $(bytes_to_human "$free_disk_bytes") free")
  fi
  if [ ${#alerts[@]} -gt 0 ]; then
    printf '%s\n' "${alerts[@]}"
    return 1
  fi
  return 0
}

emit_json() {
  local free_mem_bytes="$1"
  local free_disk_bytes="$2"
  local cache_json="$3"
  local docker_df="$4"
  local container_usage="$5"
  local node_processes_json="$6"
  cat <<EOF
{
  "timestamp": "$(ts)",
  "freeMemoryBytes": $free_mem_bytes,
  "freeDiskBytes": $free_disk_bytes,
  "cacheSizes": $cache_json,
  "dockerDf": $docker_df,
  "dockerContainers": $container_usage,
  "nodeProcesses": $node_processes_json,
  "thresholds": {
    "memoryBytes": $((THRESHOLD_MEMORY_MB * 1024 * 1024)),
    "diskBytes": $((THRESHOLD_DISK_GB * 1024 * 1024 * 1024))
  }
}
EOF
}

emit_text() {
  local free_mem_bytes="$1"
  local free_disk_bytes="$2"
  local cache_json="$3"
  local docker_df="$4"
  local container_usage="$5"
  local node_processes="$6"
  local alerts=("$(check_thresholds "$free_mem_bytes" "$free_disk_bytes" 2>/dev/null || true)")
  if $ALERT_ONLY && [ -z "${alerts[*]}" ]; then
    return
  fi
  if [ -n "${alerts[*]}" ]; then
    log "WARN" "Alerts detected:\n${alerts[*]}"
  else
    log "INFO" "Resources within configured thresholds"
  fi
  log "INFO" "Free memory: $(bytes_to_human "$free_mem_bytes")"
  log "INFO" "Free disk space: $(bytes_to_human "$free_disk_bytes")"
  log "INFO" "Cache totals: $(bytes_to_human "$(echo "$cache_json" | jq -r '."__total__"')")"
  if command -v jq >/dev/null; then
    log "INFO" "Largest cache directories:"
    echo "$cache_json" | jq -r 'del(."__total__") | to_entries | sort_by(.value) | reverse | .[0:5][] | "  \(.key): \(.value | tostring) bytes"' | tee -a "$LOG_FILE"
  fi
  if command -v jq >/dev/null && [ "$docker_df" != "[]" ]; then
    log "INFO" "Docker disk usage:"
    echo "$docker_df" | jq -r '.[] | "  Type: \(.Type) | Reclaimable: \(.Reclaimable) | Size: \(.Size)"' | tee -a "$LOG_FILE"
  fi
  if command -v jq >/dev/null && [ "$container_usage" != "[]" ]; then
    log "INFO" "Docker container stats:"
    echo "$container_usage" | jq -r '.[0:5][] | "  \(.Name): CPU \(.CPUPerc), Mem \(.MemUsage), Net \(.NetIO)"' | tee -a "$LOG_FILE"
  fi
  if [ -n "$node_processes" ]; then
    log "INFO" "Node processes consuming resources:"
    printf '%s\n' "$node_processes" | tee -a "$LOG_FILE"
  fi
}

collect_node_processes_json() {
  local data="$(collect_node_processes)"
  if [[ -z "$data" ]]; then
    echo '[]'
    return
  fi
  printf '['
  local first=true
  while read -r line; do
    local pid mem cpu etime command
    pid="$(echo "$line" | awk '{print $1}')"
    mem="$(echo "$line" | awk '{print $2}')"
    cpu="$(echo "$line" | awk '{print $3}')"
    etime="$(echo "$line" | awk '{print $4}')"
    command="$(echo "$line" | cut -d' ' -f5-)"
    if ! $first; then printf ','; fi
    local command_json="${command//"/\\"}"
    printf '{"pid":%s,"memPercent":%s,"cpuPercent":%s,"elapsed":"%s","command":"%s"}' "$pid" "$mem" "$cpu" "${etime}" "${command_json}"
    first=false
  done <<<"$data"
  printf ']'
}

collect_metrics() {
  local free_mem_bytes free_disk_bytes cache_json docker_df container_usage node_processes node_processes_json
  free_mem_bytes="$(collect_system_memory)"
  free_disk_bytes="$(collect_disk_free)"
  cache_json="$(collect_cache_sizes)"
  docker_df="$(collect_docker_stats)"
  container_usage="$(collect_container_usage)"
  node_processes="$(collect_node_processes)"
  node_processes_json="$(collect_node_processes_json)"
  if $JSON_OUTPUT || $REPORT_MODE; then
    emit_json "$free_mem_bytes" "$free_disk_bytes" "$cache_json" "$docker_df" "$container_usage" "$node_processes_json"
  else
    emit_text "$free_mem_bytes" "$free_disk_bytes" "$cache_json" "$docker_df" "$container_usage" "$node_processes"
  fi
}

create_report() {
  local report_file="${REPORT_PREFIX}-$(date '+%Y%m%d-%H%M%S').json"
  JSON_OUTPUT=true collect_metrics >"$report_file"
  log "INFO" "Resource report generated: $report_file"
}

main_once() {
  if $REPORT_MODE; then
    create_report
    exit 0
  fi
  collect_metrics
}

main_watch() {
  while true; do
    main_once
    sleep "$WATCH_INTERVAL"
  done
}

main() {
  parse_args "$@"
  if $WATCH_MODE; then
    main_watch
  else
    main_once
  fi
}

main "$@"
