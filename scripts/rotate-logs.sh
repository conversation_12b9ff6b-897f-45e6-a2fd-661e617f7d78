#!/usr/bin/env bash

set -euo pipefail

###############################################
# Pacific Cloud Seafoods - Log Rotation Utility
###############################################

SCRIPT_NAME="$(basename "$0")"
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}" )"/.. && pwd)"
LOG_DIR="${PROJECT_ROOT}/tmp"
ROTATION_LOG="${LOG_DIR}/log-rotation.log"
DEFAULT_DAYS=7
DEFAULT_MAX_SIZE_GB=5
DAYS_TO_KEEP=$DEFAULT_DAYS
MAX_SIZE_GB=$DEFAULT_MAX_SIZE_GB
ARCHIVE=false
COMPRESS=false
DRY_RUN=false
AUTO_CONFIRM=false

mkdir -p "$LOG_DIR"

log_ts() {
  date '+%Y-%m-%d %H:%M:%S'
}

log() {
  local level="$1"; shift
  printf '[%s] [%s] %s\n' "$(log_ts)" "$level" "$*" | tee -a "$ROTATION_LOG" >&2
}

usage() {
  cat <<EOF
Usage: ${SCRIPT_NAME} [options]

Options:
  --days=N             Keep logs from last N days (default: ${DEFAULT_DAYS})
  --max-size=GB        Maximum total log size before cleanup (default: ${DEFAULT_MAX_SIZE_GB}GB)
  --archive            Archive logs before deletion
  --compress           Compress old logs instead of deleting
  --dry-run            Show actions without performing them
  --auto-confirm       Skip confirmation prompts
  --help               Show this help message
EOF
}

parse_args() {
  for arg in "$@"; do
    case "$arg" in
      --days=*) DAYS_TO_KEEP="${arg#*=}" ;;
      --max-size=*) MAX_SIZE_GB="${arg#*=}" ;;
      --archive) ARCHIVE=true ;;
      --compress) COMPRESS=true ;;
      --dry-run) DRY_RUN=true ;;
      --auto-confirm) AUTO_CONFIRM=true ;;
      --help|-h) usage; exit 0 ;;
      *) log ERROR "Unknown option: $arg"; usage; exit 1 ;;
    esac
  done
}

confirm() {
  local message="$1"
  if $AUTO_CONFIRM; then
    return 0
  fi
  read -r -p "$message [y/N]: " response
  [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]
}

get_total_size() {
  local path="$1"
  if [[ ! -d "$path" ]]; then
    echo 0
    return
  fi
  du -sk "$path" 2>/dev/null | awk '{print $1 * 1024}'
}

rotate_path() {
  local path="$1"
  local pattern="$2"
  local action="$3"
  if [[ ! -d "$path" ]]; then
    return
  fi
  find "$path" -type f $pattern -print0 2>/dev/null | while IFS= read -r -d '' file; do
    case "$action" in
      delete)
        if $DRY_RUN; then
          log INFO "[Dry Run] Would delete $file"
        else
          rm -f "$file" && log INFO "Deleted $file"
        fi
        ;;
      compress)
        if $DRY_RUN; then
          log INFO "[Dry Run] Would compress $file"
        else
          gzip -f "$file" && log INFO "Compressed $file.gz"
        fi
        ;;
      archive)
        archive_file "$file"
        ;;
    esac
  done
}

archive_file() {
  local file="$1"
  local archive_dir="${LOG_DIR}/archives"
  mkdir -p "$archive_dir"
  local destination="${archive_dir}/$(basename "$file")-$(date '+%Y%m%d%H%M%S')"
  if $DRY_RUN; then
    log INFO "[Dry Run] Would archive $file -> $destination"
    return
  fi
  if $COMPRESS; then
    gzip -c "$file" >"${destination}.gz" && rm -f "$file" && log INFO "Archived and compressed $file"
  else
    mv "$file" "$destination" && log INFO "Archived $file"
  fi
}

trim_by_date() {
  local path="$1"
  if [[ ! -d "$path" ]]; then
    return
  fi
  local pattern="-mtime +$DAYS_TO_KEEP"
  if $ARCHIVE; then
    rotate_path "$path" "$pattern" archive
  elif $COMPRESS; then
    rotate_path "$path" "$pattern" compress
  else
    rotate_path "$path" "$pattern" delete
  fi
}

trim_by_size() {
  local path="$1"
  local max_bytes=$((MAX_SIZE_GB * 1024 * 1024 * 1024))
  while true; do
    local total
    total=$(get_total_size "$path")
    if (( total <= max_bytes )); then
      break
    fi
    local oldest_file
    oldest_file=$(find "$path" -type f -print0 2>/dev/null | xargs -0 ls -1tr | head -n 1)
    if [[ -z "$oldest_file" ]]; then
      break
    fi
    if $ARCHIVE; then
      archive_file "$oldest_file"
    else
      if $DRY_RUN; then
        log INFO "[Dry Run] Would delete $oldest_file (size limit exceeded)"
      else
        rm -f "$oldest_file" && log INFO "Deleted $oldest_file to reduce size"
      fi
    fi
  done
}

truncate_docker_logs() {
  log INFO "Truncating Docker container logs"
  if $DRY_RUN; then
    log INFO "[Dry Run] Would truncate Docker logs via docker logs --tail=0"
    return
  fi
  docker ps --format '{{.ID}} {{.Names}}' | while read -r id name; do
    docker logs "$id" --tail 0 >/dev/null 2>&1 || true
    : >"/var/lib/docker/containers/${id}/${id}-json.log" 2>/dev/null || true
    log INFO "Truncated Docker log for $name"
  done
}

truncate_supabase_logs() {
  log INFO "Truncating Supabase container logs"
  if $DRY_RUN; then
    log INFO "[Dry Run] Would truncate Supabase logs"
    return
  fi
  docker ps --filter "name=supabase" --format '{{.ID}} {{.Names}}' | while read -r id name; do
    docker logs "$id" --tail 0 >/dev/null 2>&1 || true
    : >"/var/lib/docker/containers/${id}/${id}-json.log" 2>/dev/null || true
    log INFO "Truncated Supabase log for $name"
  done
}

cleanup_temp_files() {
  local temp_dir="${PROJECT_ROOT}/tmp"
  if [[ ! -d "$temp_dir" ]]; then
    return
  fi
  find "$temp_dir" -type f -name '*.tmp' -o -name '*.temp' -print0 2>/dev/null | while IFS= read -r -d '' file; do
    if $DRY_RUN; then
      log INFO "[Dry Run] Would remove temp file $file"
    else
      rm -f "$file" && log INFO "Removed temp file $file"
    fi
  done
}

main() {
  parse_args "$@"
  log INFO "Starting log rotation (days=$DAYS_TO_KEEP, maxSize=${MAX_SIZE_GB}GB, archive=$ARCHIVE, compress=$COMPRESS)"
  if ! $DRY_RUN && ! $AUTO_CONFIRM; then
    if ! confirm "Proceed with log rotation?"; then
      log WARN "Operation cancelled"
      exit 0
    fi
  fi
  local log_paths=(
    "${PROJECT_ROOT}/tmp"
    "${PROJECT_ROOT}/playwright-report"
    "${PROJECT_ROOT}/test-results"
    "${PROJECT_ROOT}/coverage"
    "${PROJECT_ROOT}/logs"
  )
  for path in "${log_paths[@]}"; do
    trim_by_date "$path"
    trim_by_size "$path"
  done
  truncate_docker_logs || log WARN "Failed to truncate some Docker logs"
  truncate_supabase_logs || log WARN "Failed to truncate some Supabase logs"
  cleanup_temp_files
  log INFO "Log rotation complete"
}

main "$@"
