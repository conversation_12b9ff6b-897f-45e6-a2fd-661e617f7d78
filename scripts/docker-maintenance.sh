#!/usr/bin/env bash

set -euo pipefail

#################################################
# Pacific Cloud Seafoods - Docker Maintenance Tool
#################################################

SCRIPT_NAME="$(basename "$0")"
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}" )"/.. && pwd)"
LOG_DIR="${PROJECT_ROOT}/tmp"
LOG_FILE="${LOG_DIR}/docker-maintenance.log"
AUTO_CONFIRM=false
DRY_RUN=false
AGGRESSIVE=false
PRESERVE_SUPABASE=true
BACKUP_VOLUMES=false
MAX_BUILD_CACHE_GB=10
KEEP_IMAGES=2

mkdir -p "$LOG_DIR"

log_ts() {
  date '+%Y-%m-%d %H:%M:%S'
}

log() {
  local level="$1"; shift
  local color_reset='\033[0m'
  local color
  case "$level" in
    INFO) color='\033[1;34m' ;;
    WARN) color='\033[1;33m' ;;
    ERROR) color='\033[1;31m' ;;
    SUCCESS) color='\033[1;32m' ;;
    *) color='\033[0m' ;;
  esac
  printf "%b[%s] [%s] %s%b\n" "$color" "$(log_ts)" "$SCRIPT_NAME" "$*" "$color_reset" | tee -a "$LOG_FILE" >&2
}

usage() {
  cat <<EOF
Usage: ${SCRIPT_NAME} [options]

Options:
  --aggressive             Remove all unused Docker resources (images, volumes, networks)
  --preserve-supabase      Preserve Supabase resources (default: true)
  --no-preserve-supabase   Allow removal of Supabase containers and volumes
  --backup-volumes         Backup volumes before removal
  --max-build-cache=GB     Set maximum build cache size (default: ${MAX_BUILD_CACHE_GB}GB)
  --keep-images=N          Keep N most recent images per repository (default: ${KEEP_IMAGES})
  --dry-run                Show planned actions without executing
  --auto-confirm           Skip interactive confirmations
  --help                   Show this help message
EOF
}

parse_args() {
  for arg in "$@"; do
    case "$arg" in
      --aggressive) AGGRESSIVE=true ;;
      --preserve-supabase) PRESERVE_SUPABASE=true ;;
      --no-preserve-supabase) PRESERVE_SUPABASE=false ;;
      --backup-volumes) BACKUP_VOLUMES=true ;;
      --max-build-cache=*) MAX_BUILD_CACHE_GB="${arg#*=}" ;;
      --keep-images=*) KEEP_IMAGES="${arg#*=}" ;;
      --dry-run) DRY_RUN=true ;;
      --auto-confirm) AUTO_CONFIRM=true ;;
      --help|-h) usage; exit 0 ;;
      *) log ERROR "Unknown option: $arg"; usage; exit 1 ;;
    esac
  done
}

confirm() {
  local message="$1"
  if $AUTO_CONFIRM; then
    return 0
  fi
  read -r -p "$message [y/N]: " response
  [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]
}

supabase_resources() {
  docker ps -a --format '{{.ID}} {{.Names}}' | awk '/supabase/ {print $1}'
}

preserve_filter() {
  local id="$1"
  if ! $PRESERVE_SUPABASE; then
    return 1
  fi
  if docker inspect --format '{{index .Config.Labels "com.docker.compose.project"}}' "$id" 2>/dev/null | grep -qi "supabase"; then
    return 0
  fi
  docker inspect --format '{{.Name}}' "$id" 2>/dev/null | grep -qi "supabase"
}

stop_nonessential_containers() {
  log INFO "Stopping exited or dead containers"
  local ids
  ids=$(docker ps -a --filter status=exited --filter status=dead --format '{{.ID}}') || true
  if [[ -z "$ids" ]]; then
    log INFO "No exited or dead containers found"
    return
  fi
  if $DRY_RUN; then
    log INFO "[Dry Run] Would remove containers: $ids"
    return
  fi
  docker rm $ids >/dev/null && log SUCCESS "Removed exited/dead containers" || log WARN "Failed to remove some containers"
}

size_to_bytes() {
  local size="$1"
  [[ -z "$size" ]] && echo 0 && return
  local number unit
  number="${size%%[kKmMgGtTpPbB]*}"
  unit="${size:${#number}}"
  # Strip spaces
  number="$(echo "$number" | tr -d ' ')"
  unit="$(echo "$unit" | tr -d ' ')"
  [[ -z "$number" ]] && echo 0 && return
  local bytes="$(echo "$number" | awk '{printf "%f", $1}')"
  case "$unit" in
    B|b|byte|bytes|"") echo "${bytes%.*}" ;;
    kB|KB|kb) awk -v n="$bytes" 'BEGIN {printf "%.0f", n*1024}' ;;
    MB|mb|mB) awk -v n="$bytes" 'BEGIN {printf "%.0f", n*1024*1024}' ;;
    GB|gb|gB) awk -v n="$bytes" 'BEGIN {printf "%.0f", n*1024*1024*1024}' ;;
    TB|tb|tB) awk -v n="$bytes" 'BEGIN {printf "%.0f", n*1024*1024*1024*1024}' ;;
    *) echo 0 ;;
  esac
}

remove_old_images() {
  log INFO "Removing unused Docker images (keeping ${KEEP_IMAGES} per repository)"
  local repos
  repos=$(docker images --format '{{.Repository}}' | sort -u)
  for repo in $repos; do
    [[ "$repo" == "<none>" ]] && continue
    local count=0
    docker images --format '{{.Repository}}||{{.ID}}||{{.Tag}}||{{.CreatedAt}}' "$repo" | sort -r -k4 | while IFS='||' read -r repo_name image_id tag created_at; do
      count=$((count + 1))
      if (( count <= KEEP_IMAGES )); then
        continue
      fi
      if preserve_filter "$image_id"; then
        log INFO "Preserving Supabase image $image_id"
        continue
      fi
      if $DRY_RUN; then
        log INFO "[Dry Run] Would remove image ${repo_name}:${tag} ($image_id)"
      else
        docker rmi "$image_id" >/dev/null && log SUCCESS "Removed image ${repo_name}:${tag}" || log WARN "Failed to remove image ${repo_name}:${tag}"
      fi
    done
  done
}

prune_dangling_images() {
  log INFO "Pruning dangling images"
  if $DRY_RUN; then
    log INFO "[Dry Run] docker image prune -f"
  else
    docker image prune -f >/dev/null && log SUCCESS "Removed dangling images" || log WARN "Image prune failed"
  fi
}

prune_volumes() {
  log INFO "Pruning unused volumes"
  if $DRY_RUN; then
    log INFO "[Dry Run] docker volume prune -f"
    return
  fi
  docker volume prune -f >/dev/null && log SUCCESS "Removed dangling volumes" || log WARN "Volume prune failed"
}

volume_has_supabase_label() {
  local vol="$1"
  local labels
  labels=$(docker volume inspect "$vol" --format '{{json .Labels}}' 2>/dev/null || echo null)
  if [[ "$labels" == "null" ]]; then
    return 1
  fi
  echo "$labels" | tr '"' '\n' | grep -qi "supabase"
}

remove_orphaned_volumes() {
  log INFO "Identifying orphaned volumes"
  local volumes
  volumes=$(docker volume ls -q)
  for vol in $volumes; do
    if $PRESERVE_SUPABASE && volume_has_supabase_label "$vol"; then
      log INFO "Preserving Supabase volume $vol"
      continue
    fi
    local refs
    refs=$(docker ps -a --filter volume="$vol" --format '{{.ID}}')
    if [[ -n "$refs" ]]; then
      continue
    fi
    if $BACKUP_VOLUMES; then
      backup_volume "$vol"
    fi
    if $DRY_RUN; then
      log INFO "[Dry Run] Would remove orphaned volume $vol"
    else
      docker volume rm "$vol" >/dev/null && log SUCCESS "Removed orphaned volume $vol" || log WARN "Failed to remove volume $vol"
    fi
  done
}

backup_volume() {
  local vol="$1"
  local backup_dir="${LOG_DIR}/docker-volume-backups"
  mkdir -p "$backup_dir"
  local backup_file="${backup_dir}/${vol}-$(date '+%Y%m%d-%H%M%S').tar.gz"
  log INFO "Creating backup for volume $vol"
  if $DRY_RUN; then
    log INFO "[Dry Run] Would create volume backup at $backup_file"
    return
  fi
  docker run --rm -v "$vol":/volume -v "$backup_dir":/backup alpine sh -c "cd /volume && tar -czf /backup/$(basename \"$backup_file\") ." >/dev/null \
    && log SUCCESS "Backup created at $backup_file" \
    || log WARN "Failed to backup volume $vol"
}

prune_networks() {
  log INFO "Pruning unused networks"
  if $DRY_RUN; then
    log INFO "[Dry Run] docker network prune -f"
    return
  fi
  docker network prune -f >/dev/null && log SUCCESS "Removed unused networks" || log WARN "Network prune failed"
}

get_build_cache_bytes() {
  local data
  data=$(docker builder df --format '{{.Type}}||{{.Size}}' 2>/dev/null || true)
  while IFS='||' read -r type size; do
    if [[ "$type" == "Build Cache" ]]; then
      size_to_bytes "$size"
      return
    fi
  done <<<"$data"
  echo 0
}

log_build_cache_size() {
  local bytes="$1"
  if [[ "$bytes" -eq 0 ]]; then
    log INFO "Docker build cache size could not be determined"
  else
    log INFO "Docker build cache size: $(bytes_to_human "$bytes")"
  fi
}

clean_build_cache() {
  local limit_bytes=$((MAX_BUILD_CACHE_GB * 1024 * 1024 * 1024))
  local current_bytes
  current_bytes=$(get_build_cache_bytes)
  log_build_cache_size "$current_bytes"
  if (( current_bytes == 0 )); then
    if $DRY_RUN; then
      log INFO "[Dry Run] Would execute docker builder prune -f"
    else
      docker builder prune -f >/dev/null && log SUCCESS "Builder prune executed" || log WARN "Builder prune failed"
    fi
    return
  fi
  if (( current_bytes <= limit_bytes )); then
    log INFO "Build cache within limit (${MAX_BUILD_CACHE_GB}GB)"
    return
  fi
  log WARN "Build cache exceeds limit (${MAX_BUILD_CACHE_GB}GB). Pruning..."
  if $DRY_RUN; then
    log INFO "[Dry Run] docker builder prune -f"
  else
    docker builder prune -f >/dev/null && log SUCCESS "Reduced build cache" || log WARN "Builder prune failed"
    if $AGGRESSIVE; then
      docker builder prune -a -f >/dev/null && log SUCCESS "Aggressive build cache prune completed" || log WARN "Aggressive builder prune failed"
    fi
  fi
}

docker_health_check() {
  log INFO "Checking Docker daemon health"
  if ! docker info >/dev/null 2>&1; then
    log ERROR "Docker daemon unreachable. Please ensure Docker Desktop is running."
    exit 1
  fi
}

cleanup_images() {
  prune_dangling_images
  remove_old_images
  if $AGGRESSIVE; then
    log INFO "Aggressive mode enabled - removing all unused images"
    if $DRY_RUN; then
      log INFO "[Dry Run] docker image prune -a -f"
    else
      docker image prune -a -f >/dev/null && log SUCCESS "Removed all unused images" || log WARN "docker image prune -a failed"
    fi
  fi
}

cleanup_volumes() {
  prune_volumes
  remove_orphaned_volumes
}

cleanup_networks() {
  prune_networks
}

cleanup_build_cache() {
  if $AGGRESSIVE; then
    clean_build_cache_aggressive
  else
    clean_build_cache
  fi
}

preserve_running_supabase() {
  if ! $PRESERVE_SUPABASE; then
    return
  fi
  local supabase_ids
  supabase_ids=$(supabase_resources)
  if [[ -z "$supabase_ids" ]]; then
    return
  fi
  log INFO "Supabase resources detected. They will be preserved."
}

main() {
  parse_args "$@"
  docker_health_check
  preserve_running_supabase
  if ! $AUTO_CONFIRM; then
    if ! confirm "Proceed with Docker maintenance operations?"; then
      log WARN "Docker maintenance cancelled"
      exit 0
    fi
  fi
  stop_nonessential_containers
  cleanup_images
  cleanup_build_cache
  cleanup_volumes
  cleanup_networks
  log SUCCESS "Docker maintenance completed"
}

main "$@"
